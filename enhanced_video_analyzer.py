"""
增强版视频分析器 - 优化意图显示
"""

import cv2
import numpy as np
from ultralytics import YOLO
import os
import json
from collections import deque
import time

def analyze_video_enhanced(video_path='left.mp4', output_path='left_enhanced_analysis.mp4'):
    """
    增强版视频分析，优化意图显示
    """
    print(f'开始增强分析视频: {video_path}')
    
    # 检查视频文件
    if not os.path.exists(video_path):
        print(f'错误: 视频文件不存在 - {video_path}')
        return
    
    # 加载YOLOv8姿态检测模型
    print('加载YOLOv8姿态检测模型...')
    try:
        model = YOLO('yolov8n-pose.pt')
        print('模型加载成功!')
    except Exception as e:
        print(f'模型加载失败: {e}')
        return
    
    # 打开视频
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f'错误: 无法打开视频文件 - {video_path}')
        return
    
    # 获取视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f'视频信息:')
    print(f'  分辨率: {width} x {height}')
    print(f'  帧率: {fps} FPS')
    print(f'  总帧数: {total_frames}')
    print(f'  时长: {total_frames/fps:.2f} 秒')
    
    # 创建输出视频写入器
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    print(f'将保存增强标注视频到: {output_path}')
    
    # COCO关键点连接关系
    connections = [
        (0, 1), (0, 2), (1, 3), (2, 4),  # 头部
        (5, 6), (5, 7), (7, 9), (6, 8), (8, 10),  # 上身
        (5, 11), (6, 12), (11, 12),  # 躯干
        (11, 13), (13, 15), (12, 14), (14, 16)  # 下身
    ]
    
    # 意图类别和颜色
    intent_classes = ['直行', '左转', '右转', '停止']
    intent_colors = [
        (0, 255, 0),    # 直行 - 绿色
        (255, 0, 0),    # 左转 - 蓝色
        (0, 0, 255),    # 右转 - 红色
        (0, 255, 255)   # 停止 - 黄色
    ]
    
    # 姿态缓冲区（增加缓冲区大小以提高稳定性）
    pose_buffer = deque(maxlen=15)  # 增加到15帧
    confidence_buffer = deque(maxlen=15)
    
    # 意图历史缓冲区（用于平滑预测）
    intent_history = deque(maxlen=10)
    
    # 统计信息
    frame_count = 0
    detection_count = 0
    analysis_results = []
    
    print('开始处理视频帧...')
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # YOLOv8姿态检测
            results = model(frame, conf=0.3, verbose=False)  # 降低置信度阈值
            
            # 创建标注帧
            annotated_frame = frame.copy()
            
            # 帧分析结果
            frame_result = {
                'frame_id': frame_count,
                'timestamp': frame_count / fps,
                'person_detected': False,
                'keypoints_visible': 0,
                'occlusion_ratio': 1.0,
                'predicted_intent': None,
                'intent_confidence': 0.0
            }
            
            current_intent = None
            intent_confidence = 0.0
            
            # 处理检测结果
            if len(results) > 0 and results[0].keypoints is not None:
                keypoints_data = results[0].keypoints.data
                
                if len(keypoints_data) > 0:
                    detection_count += 1
                    frame_result['person_detected'] = True
                    
                    # 获取第一个人的关键点
                    keypoints = keypoints_data[0]  # (17, 3) [x, y, confidence]
                    
                    if keypoints.shape[0] == 17:
                        xy = keypoints[:, :2].cpu().numpy()  # (17, 2)
                        conf = keypoints[:, 2].cpu().numpy()  # (17,)
                        
                        # 分析遮挡情况
                        visible_points = np.sum(conf > 0.3)
                        occlusion_ratio = (17 - visible_points) / 17
                        
                        frame_result['keypoints_visible'] = int(visible_points)
                        frame_result['occlusion_ratio'] = float(occlusion_ratio)
                        
                        # 绘制关键点
                        for i, (x, y) in enumerate(xy):
                            if conf[i] > 0.3:
                                # 根据置信度选择颜色
                                color = (0, 255, 0) if conf[i] > 0.7 else (0, 255, 255) if conf[i] > 0.5 else (0, 0, 255)
                                cv2.circle(annotated_frame, (int(x), int(y)), 5, color, -1)
                                
                                # 显示关键点编号
                                cv2.putText(annotated_frame, str(i), (int(x), int(y-10)), 
                                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
                        
                        # 绘制连接线
                        for start_idx, end_idx in connections:
                            if conf[start_idx] > 0.3 and conf[end_idx] > 0.3:
                                start_point = (int(xy[start_idx][0]), int(xy[start_idx][1]))
                                end_point = (int(xy[end_idx][0]), int(xy[end_idx][1]))
                                cv2.line(annotated_frame, start_point, end_point, (255, 0, 0), 3)
                        
                        # 归一化关键点
                        normalized_kp = xy.copy()
                        normalized_kp[:, 0] /= width
                        normalized_kp[:, 1] /= height
                        
                        # 添加到缓冲区
                        pose_buffer.append(normalized_kp)
                        confidence_buffer.append(conf)
                        
                        # 意图判断（需要足够的历史数据）
                        if len(pose_buffer) >= 8:  # 降低要求的帧数
                            recent_poses = np.array(list(pose_buffer)[-8:])
                            recent_conf = np.array(list(confidence_buffer)[-8:])
                            
                            # 只使用高置信度的关键点进行计算
                            valid_mask = recent_conf > 0.5
                            
                            if np.sum(valid_mask[-1, [11, 12]]) >= 1:  # 至少一个髋部关键点可见
                                # 计算髋部中心的移动
                                hip_centers = []
                                for t in range(len(recent_poses)):
                                    valid_hips = recent_poses[t, [11, 12], :][valid_mask[t, [11, 12]]]
                                    if len(valid_hips) > 0:
                                        hip_centers.append(np.mean(valid_hips, axis=0))
                                    else:
                                        # 如果髋部不可见，使用肩膀中心
                                        valid_shoulders = recent_poses[t, [5, 6], :][valid_mask[t, [5, 6]]]
                                        if len(valid_shoulders) > 0:
                                            hip_centers.append(np.mean(valid_shoulders, axis=0))
                                        else:
                                            hip_centers.append(np.array([0.5, 0.5]))  # 默认中心
                                
                                if len(hip_centers) >= 5:
                                    hip_centers = np.array(hip_centers)
                                    
                                    # 计算移动向量（使用更长的时间窗口）
                                    movement = hip_centers[-1] - hip_centers[0]
                                    
                                    # 计算移动速度
                                    speed = np.linalg.norm(movement)
                                    
                                    # 改进的意图判断逻辑
                                    if speed < 0.008:  # 几乎没有移动
                                        predicted_intent = 3  # 停止
                                        intent_confidence = 0.8
                                    elif movement[0] < -0.015:  # 向左移动
                                        predicted_intent = 1  # 左转
                                        intent_confidence = min(0.9, abs(movement[0]) * 50)
                                    elif movement[0] > 0.015:  # 向右移动
                                        predicted_intent = 2  # 右转
                                        intent_confidence = min(0.9, abs(movement[0]) * 50)
                                    else:  # 主要向前移动
                                        predicted_intent = 0  # 直行
                                        intent_confidence = 0.7
                                    
                                    # 添加到历史缓冲区
                                    intent_history.append(predicted_intent)
                                    
                                    # 使用历史数据平滑预测（投票机制）
                                    if len(intent_history) >= 5:
                                        intent_counts = np.bincount(list(intent_history), minlength=4)
                                        smoothed_intent = np.argmax(intent_counts)
                                        
                                        # 如果当前预测与历史一致，提高置信度
                                        if smoothed_intent == predicted_intent:
                                            intent_confidence = min(0.95, intent_confidence + 0.1)
                                        
                                        current_intent = smoothed_intent
                                        frame_result['predicted_intent'] = intent_classes[current_intent]
                                        frame_result['intent_confidence'] = intent_confidence
            
            # 绘制增强的信息面板
            panel_height = 200
            panel_width = 500
            
            # 半透明背景
            overlay = annotated_frame.copy()
            cv2.rectangle(overlay, (10, 10), (panel_width, panel_height), (0, 0, 0), -1)
            cv2.addWeighted(overlay, 0.7, annotated_frame, 0.3, 0, annotated_frame)
            
            # 边框
            cv2.rectangle(annotated_frame, (10, 10), (panel_width, panel_height), (255, 255, 255), 3)
            
            # 显示信息
            y_offset = 40
            line_height = 30
            
            # 帧信息
            cv2.putText(annotated_frame, f'Frame: {frame_count}/{total_frames}', 
                       (25, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
            y_offset += line_height
            
            # 时间戳
            cv2.putText(annotated_frame, f'Time: {frame_count/fps:.2f}s', 
                       (25, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
            y_offset += line_height
            
            if frame_result['person_detected']:
                # 检测状态
                cv2.putText(annotated_frame, f'Person: DETECTED', 
                           (25, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
                y_offset += line_height
                
                # 关键点信息
                cv2.putText(annotated_frame, f'Keypoints: {frame_result["keypoints_visible"]}/17', 
                           (25, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
                y_offset += line_height
                
                # 质量评估
                quality = "GOOD" if frame_result['occlusion_ratio'] < 0.3 else \
                         "MEDIUM" if frame_result['occlusion_ratio'] < 0.6 else "POOR"
                quality_color = (0, 255, 0) if quality == "GOOD" else \
                               (0, 255, 255) if quality == "MEDIUM" else (0, 0, 255)
                cv2.putText(annotated_frame, f'Quality: {quality}', 
                           (25, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.8, quality_color, 2)
                y_offset += line_height
                
                # 意图预测（重点显示）
                if frame_result['predicted_intent']:
                    intent_text = f'INTENT: {frame_result["predicted_intent"]}'
                    confidence_text = f'Confidence: {frame_result["intent_confidence"]:.2f}'
                    
                    # 根据意图选择颜色
                    intent_idx = intent_classes.index(frame_result['predicted_intent'])
                    intent_color = intent_colors[intent_idx]
                    
                    # 大字体显示意图
                    cv2.putText(annotated_frame, intent_text, 
                               (25, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 1.0, intent_color, 3)
                    y_offset += line_height + 5
                    
                    # 显示置信度
                    cv2.putText(annotated_frame, confidence_text, 
                               (25, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.7, intent_color, 2)
                    
                    # 在右上角也显示当前意图（更醒目）
                    intent_display = f'{frame_result["predicted_intent"]} ({frame_result["intent_confidence"]:.2f})'
                    text_size = cv2.getTextSize(intent_display, cv2.FONT_HERSHEY_SIMPLEX, 1.5, 3)[0]
                    cv2.rectangle(annotated_frame, 
                                 (width - text_size[0] - 20, 10), 
                                 (width - 10, 60), 
                                 intent_color, -1)
                    cv2.putText(annotated_frame, intent_display, 
                               (width - text_size[0] - 15, 45), 
                               cv2.FONT_HERSHEY_SIMPLEX, 1.5, (255, 255, 255), 3)
                else:
                    cv2.putText(annotated_frame, 'INTENT: ANALYZING...', 
                               (25, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (128, 128, 128), 3)
            else:
                cv2.putText(annotated_frame, f'Person: NOT DETECTED', 
                           (25, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
            
            # 保存帧结果
            analysis_results.append(frame_result)
            
            # 写入输出视频
            writer.write(annotated_frame)
            
            # 显示进度
            if frame_count % 30 == 0 or frame_count == total_frames:
                progress = frame_count / total_frames * 100
                print(f'处理进度: {progress:.1f}% ({frame_count}/{total_frames})')
    
    except Exception as e:
        print(f'处理过程中出现错误: {e}')
    
    finally:
        cap.release()
        writer.release()
    
    # 输出统计结果
    detection_rate = detection_count / frame_count if frame_count > 0 else 0
    
    # 统计意图分布
    intent_stats = {'直行': 0, '左转': 0, '右转': 0, '停止': 0, '未识别': 0}
    for result in analysis_results:
        intent = result.get('predicted_intent', '未识别')
        if intent in intent_stats:
            intent_stats[intent] += 1
        else:
            intent_stats['未识别'] += 1
    
    print(f'\n=== 增强分析结果 ===')
    print(f'总帧数: {frame_count}')
    print(f'检测到人体的帧数: {detection_count}')
    print(f'检测率: {detection_rate:.2%}')
    print(f'\n意图分布:')
    for intent, count in intent_stats.items():
        percentage = count / frame_count * 100 if frame_count > 0 else 0
        print(f'  {intent}: {count} 帧 ({percentage:.1f}%)')
    
    # 保存增强分析结果
    result_file = 'left_enhanced_analysis.json'
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump({
            'video_info': {
                'path': video_path,
                'width': width,
                'height': height,
                'fps': fps,
                'total_frames': total_frames
            },
            'statistics': {
                'total_frames': frame_count,
                'detection_count': detection_count,
                'detection_rate': detection_rate,
                'intent_distribution': intent_stats
            },
            'frame_results': analysis_results
        }, f, ensure_ascii=False, indent=2)
    
    print(f'\n增强分析结果已保存到: {result_file}')
    print(f'增强标注视频已保存到: {output_path}')
    print('增强视频分析完成!')
    
    return analysis_results

if __name__ == '__main__':
    # 运行增强分析
    analyze_video_enhanced()

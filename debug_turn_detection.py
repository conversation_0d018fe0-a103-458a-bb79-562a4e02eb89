"""
调试转弯检测 - 分析实际数据
"""

import cv2
import numpy as np
import os
from collections import deque
from ultralytics import YOLO
import json

def debug_turn_detection(video_path='left.mp4', output_path='left_debug_turn.mp4'):
    """
    调试转弯检测，输出详细数据
    """
    print(f"开始调试转弯检测: {video_path}")
    
    # 检查视频文件
    if not os.path.exists(video_path):
        print(f"错误: 视频文件不存在")
        return False
    
    # 加载模型
    print("加载YOLOv8模型...")
    try:
        model = YOLO('yolov8n-pose.pt')
        print("✓ 模型加载成功")
    except Exception as e:
        print(f"✗ 模型加载失败: {e}")
        return False
    
    # 打开视频
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("错误: 无法打开视频")
        return False
    
    # 视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"视频: {width}x{height}, {fps}FPS, {total_frames}帧")
    
    # 创建输出视频
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    # 数据收集
    frame_data = []
    body_centers = deque(maxlen=30)
    
    frame_count = 0
    detection_count = 0
    
    print("开始分析数据...")
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 姿态检测
            results = model(frame, conf=0.3, verbose=False)
            
            annotated_frame = frame.copy()
            
            current_data = {
                'frame': frame_count,
                'timestamp': frame_count / fps,
                'person_detected': False,
                'body_center': None,
                'shoulder_angle': None,
                'hip_angle': None,
                'movement_vector': None,
                'movement_magnitude': None
            }
            
            # 处理检测结果
            if len(results) > 0 and results[0].keypoints is not None:
                keypoints_data = results[0].keypoints.data
                
                if len(keypoints_data) > 0:
                    detection_count += 1
                    current_data['person_detected'] = True
                    
                    keypoints = keypoints_data[0]
                    if keypoints.shape[0] == 17:
                        xy = keypoints[:, :2].cpu().numpy()
                        conf = keypoints[:, 2].cpu().numpy()
                        
                        # 计算身体中心
                        if (conf[5] > 0.4 and conf[6] > 0.4 and conf[11] > 0.4 and conf[12] > 0.4):
                            left_shoulder = xy[5]
                            right_shoulder = xy[6]
                            left_hip = xy[11]
                            right_hip = xy[12]
                            
                            shoulder_center = (left_shoulder + right_shoulder) / 2
                            hip_center = (left_hip + right_hip) / 2
                            body_center = (shoulder_center + hip_center) / 2
                            
                            # 归一化
                            normalized_center = body_center / np.array([width, height])
                            current_data['body_center'] = normalized_center.tolist()
                            
                            # 计算肩膀角度
                            shoulder_vector = right_shoulder - left_shoulder
                            shoulder_angle = np.degrees(np.arctan2(shoulder_vector[1], shoulder_vector[0]))
                            current_data['shoulder_angle'] = shoulder_angle
                            
                            # 计算髋部角度
                            hip_vector = right_hip - left_hip
                            hip_angle = np.degrees(np.arctan2(hip_vector[1], hip_vector[0]))
                            current_data['hip_angle'] = hip_angle
                            
                            # 添加到历史
                            body_centers.append(normalized_center)
                            
                            # 计算移动向量
                            if len(body_centers) >= 10:
                                movement_vector = body_centers[-1] - body_centers[-10]
                                movement_magnitude = np.linalg.norm(movement_vector)
                                current_data['movement_vector'] = movement_vector.tolist()
                                current_data['movement_magnitude'] = movement_magnitude
                        
                        # 绘制关键点
                        for i, (x, y) in enumerate(xy):
                            if conf[i] > 0.3:
                                color = (0, 255, 0) if conf[i] > 0.7 else (0, 255, 255)
                                cv2.circle(annotated_frame, (int(x), int(y)), 6, color, -1)
                        
                        # 绘制骨架
                        connections = [
                            (5, 6), (5, 7), (7, 9), (6, 8), (8, 10),
                            (5, 11), (6, 12), (11, 12),
                            (11, 13), (13, 15), (12, 14), (14, 16)
                        ]
                        
                        for start_idx, end_idx in connections:
                            if conf[start_idx] > 0.3 and conf[end_idx] > 0.3:
                                start_point = (int(xy[start_idx][0]), int(xy[start_idx][1]))
                                end_point = (int(xy[end_idx][0]), int(xy[end_idx][1]))
                                cv2.line(annotated_frame, start_point, end_point, (255, 0, 0), 3)
                        
                        # 绘制身体中心
                        if current_data['body_center']:
                            center_pixel = (current_data['body_center'][0] * width, 
                                          current_data['body_center'][1] * height)
                            cv2.circle(annotated_frame, (int(center_pixel[0]), int(center_pixel[1])), 
                                     10, (255, 255, 0), -1)
                        
                        # 绘制移动轨迹
                        if len(body_centers) >= 2:
                            for i in range(1, len(body_centers)):
                                prev_center = body_centers[i-1] * np.array([width, height])
                                curr_center = body_centers[i] * np.array([width, height])
                                cv2.line(annotated_frame, 
                                        (int(prev_center[0]), int(prev_center[1])),
                                        (int(curr_center[0]), int(curr_center[1])),
                                        (0, 255, 255), 3)
                        
                        # 绘制移动向量
                        if current_data['movement_vector']:
                            center_pixel = (current_data['body_center'][0] * width, 
                                          current_data['body_center'][1] * height)
                            vector_end = (center_pixel[0] + current_data['movement_vector'][0] * width * 10,
                                        center_pixel[1] + current_data['movement_vector'][1] * height * 10)
                            cv2.arrowedLine(annotated_frame, 
                                          (int(center_pixel[0]), int(center_pixel[1])),
                                          (int(vector_end[0]), int(vector_end[1])),
                                          (255, 0, 255), 5, tipLength=0.3)
            
            # 保存帧数据
            frame_data.append(current_data)
            
            # 绘制详细信息面板
            panel_width = 800
            panel_height = 300
            
            # 半透明背景
            overlay = annotated_frame.copy()
            cv2.rectangle(overlay, (10, 10), (panel_width, panel_height), (0, 0, 0), -1)
            cv2.addWeighted(overlay, 0.9, annotated_frame, 0.1, 0, annotated_frame)
            
            # 白色边框
            cv2.rectangle(annotated_frame, (10, 10), (panel_width, panel_height), (255, 255, 255), 3)
            
            # 显示详细信息
            y = 40
            line_height = 25
            
            # 基本信息
            cv2.putText(annotated_frame, f'Frame: {frame_count}/{total_frames} ({frame_count/fps:.1f}s)', 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            y += line_height
            
            if current_data['person_detected']:
                cv2.putText(annotated_frame, 'Person: DETECTED', 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                y += line_height
                
                # 身体中心
                if current_data['body_center']:
                    center_text = f"Body Center: ({current_data['body_center'][0]:.3f}, {current_data['body_center'][1]:.3f})"
                    cv2.putText(annotated_frame, center_text, 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                    y += line_height
                
                # 肩膀角度
                if current_data['shoulder_angle'] is not None:
                    angle_text = f"Shoulder Angle: {current_data['shoulder_angle']:.1f}°"
                    cv2.putText(annotated_frame, angle_text, 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                    y += line_height
                
                # 髋部角度
                if current_data['hip_angle'] is not None:
                    hip_text = f"Hip Angle: {current_data['hip_angle']:.1f}°"
                    cv2.putText(annotated_frame, hip_text, 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                    y += line_height
                
                # 移动向量
                if current_data['movement_vector']:
                    vector_text = f"Movement: ({current_data['movement_vector'][0]:.4f}, {current_data['movement_vector'][1]:.4f})"
                    cv2.putText(annotated_frame, vector_text, 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                    y += line_height
                    
                    magnitude_text = f"Magnitude: {current_data['movement_magnitude']:.4f}"
                    cv2.putText(annotated_frame, magnitude_text, 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                    y += line_height
                
                # 简单的转向判断
                if current_data['movement_vector']:
                    x_movement = current_data['movement_vector'][0]
                    if abs(x_movement) > 0.01:
                        if x_movement < 0:
                            direction_text = "Direction: LEFT"
                            direction_color = (255, 0, 0)
                        else:
                            direction_text = "Direction: RIGHT"
                            direction_color = (0, 0, 255)
                    else:
                        direction_text = "Direction: STRAIGHT/STOP"
                        direction_color = (0, 255, 0)
                    
                    cv2.putText(annotated_frame, direction_text, 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, direction_color, 2)
                    
                    # 右上角大字显示
                    direction_only = direction_text.split(": ")[1]
                    text_size = cv2.getTextSize(direction_only, cv2.FONT_HERSHEY_SIMPLEX, 2.0, 4)[0]
                    cv2.rectangle(annotated_frame, 
                                 (width - text_size[0] - 40, 20), 
                                 (width - 20, 80), 
                                 direction_color, -1)
                    cv2.putText(annotated_frame, direction_only, 
                               (width - text_size[0] - 30, 60), 
                               cv2.FONT_HERSHEY_SIMPLEX, 2.0, (255, 255, 255), 4)
            else:
                cv2.putText(annotated_frame, 'Person: NOT DETECTED', 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            
            # 写入视频
            writer.write(annotated_frame)
            
            # 显示进度
            if frame_count % 100 == 0:
                progress = frame_count / total_frames * 100
                print(f"进度: {progress:.1f}%")
    
    except Exception as e:
        print(f"处理错误: {e}")
        return False
    
    finally:
        cap.release()
        writer.release()
    
    # 保存详细数据
    with open('debug_turn_data.json', 'w') as f:
        json.dump(frame_data, f, indent=2)
    
    # 分析数据
    print(f"\n=== 调试数据分析 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测帧数: {detection_count}")
    
    # 分析移动方向
    left_frames = 0
    right_frames = 0
    straight_frames = 0
    
    for data in frame_data:
        if data['movement_vector']:
            x_movement = data['movement_vector'][0]
            if x_movement < -0.01:
                left_frames += 1
            elif x_movement > 0.01:
                right_frames += 1
            else:
                straight_frames += 1
    
    print(f"\n移动方向统计:")
    print(f"  LEFT: {left_frames} 帧 ({left_frames/frame_count*100:.1f}%)")
    print(f"  RIGHT: {right_frames} 帧 ({right_frames/frame_count*100:.1f}%)")
    print(f"  STRAIGHT/STOP: {straight_frames} 帧 ({straight_frames/frame_count*100:.1f}%)")
    
    print(f"\n输出文件:")
    print(f"  视频: {output_path}")
    print(f"  数据: debug_turn_data.json")
    
    return True

if __name__ == '__main__':
    success = debug_turn_detection()
    if success:
        print("\n🎉 调试转弯检测完成！")
        print("请查看 left_debug_turn.mp4 和 debug_turn_data.json")
        print("\n这个版本会显示:")
        print("1. 身体中心点和移动轨迹")
        print("2. 移动向量（紫色箭头）")
        print("3. 详细的数值数据")
        print("4. 简化的方向判断")
    else:
        print("\n❌ 调试转弯检测失败！")

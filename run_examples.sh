#!/bin/bash
# 人体姿态意图识别系统示例脚本

# 创建目录
mkdir -p data checkpoints results runs

# 1. 创建示例数据
echo "===== 创建示例数据 ====="
python main.py --mode create_data --data_dir data

# 2. 训练模型
echo "===== 训练模型 ====="
python main.py --mode train --data_dir data

# 3. 摄像头推理（取消注释运行）
# echo "===== 摄像头实时推理 ====="
# python main.py --mode inference --inference_mode camera

# 4. 视频文件推理（需要提供视频文件）
# echo "===== 视频文件推理 ====="
# python main.py --mode inference --inference_mode video --input your_video.mp4 --output output_video.mp4

echo "示例运行完成!"

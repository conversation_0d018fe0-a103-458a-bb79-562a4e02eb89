# 人体姿态意图识别系统

基于双层GRU的人体姿态意图识别系统，用于跟随机器人的行人意图判断。

## 系统特点

- **双层GRU架构**：第一层进行姿态补全，第二层进行意图识别
- **遮挡处理**：支持关节遮挡情况下的姿态补全
- **实时推理**：集成YOLOv8n-pose进行实时姿态检测
- **多种意图**：支持直行、左转、右转、停止四种行为意图识别
- **注意力机制**：可选的注意力机制提升识别精度

## 系统架构

```
输入图像 → YOLOv8n-pose → 关键点检测 → 姿态序列缓冲区
                                              ↓
第一层GRU ← 数据预处理 ← 遮挡处理 ← 归一化 ← 运动特征提取
    ↓
姿态补全 + 特征提取
    ↓
第二层GRU → 注意力机制 → 意图分类 → 输出结果
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

### 1. 创建示例数据

```bash
python main.py --mode create_data --data_dir data
```

### 2. 训练模型

```bash
python main.py --mode train --data_dir data
```

### 3. 实时推理

#### 摄像头推理
```bash
python main.py --mode inference --inference_mode camera
```

#### 视频文件推理
```bash
python main.py --mode inference --inference_mode video --input input_video.mp4 --output output_video.mp4
```

## 配置说明

主要配置项在 `config.yaml` 中：

### 数据配置
- `pose_keypoints`: 关键点数量（COCO格式为17）
- `sequence_length`: 时间序列长度（帧数）
- `num_classes`: 意图类别数量
- `confidence_threshold`: 关键点置信度阈值

### 模型配置
- `pose_completion`: 第一层GRU配置（姿态补全）
- `intent_recognition`: 第二层GRU配置（意图识别）
- `attention`: 注意力机制配置

### 训练配置
- `batch_size`: 批次大小
- `learning_rate`: 学习率
- `num_epochs`: 训练轮数
- `early_stopping`: 早停配置

## 数据格式

训练数据应为JSON格式，包含以下字段：

```json
[
  {
    "pose_sequence": [[[x1, y1], [x2, y2], ...], ...],  // (T, 17, 2)
    "confidence": [[c1, c2, ...], ...],                 // (T, 17)
    "label": 0                                          // 0:直行, 1:左转, 2:右转, 3:停止
  }
]
```

## 模型架构详解

### 第一层GRU（姿态补全）
- **输入**：关键点坐标 + 速度 + 加速度 (17×6维)
- **功能**：处理遮挡、提取姿态特征
- **输出**：补全的姿态序列 + 特征向量

### 第二层GRU（意图识别）
- **输入**：第一层提取的特征
- **功能**：基于时序特征识别行为意图
- **输出**：4类意图的概率分布

### 注意力机制
- **类型**：自注意力机制
- **功能**：关注关键时间步
- **输出**：注意力权重可视化

## 性能优化

### 数据增强
- 水平翻转（自动调整左右转标签）
- 噪声添加
- 小角度旋转

### 训练技巧
- 梯度裁剪
- 学习率调度
- 早停机制
- 权重初始化

## 实时推理特性

### YOLOv8集成
- 自动下载预训练模型
- 实时姿态检测
- 置信度过滤

### 缓冲区管理
- 固定长度序列缓冲
- 自动更新机制
- 内存高效

### 可视化
- 实时姿态绘制
- 预测结果显示
- FPS监控
- 注意力权重可视化

## 文件结构

```
├── main.py                 # 主程序
├── config.yaml            # 配置文件
├── requirements.txt       # 依赖包
├── README.md              # 说明文档
├── src/
│   ├── __init__.py
│   ├── data_processor.py  # 数据处理模块
│   ├── model.py           # 双层GRU模型
│   ├── trainer.py         # 训练模块
│   └── inference.py       # 实时推理模块
├── data/                  # 数据目录
├── checkpoints/           # 模型检查点
├── results/               # 结果输出
└── runs/                  # TensorBoard日志
```

## 使用示例

### 训练自定义数据

1. 准备数据（JSON格式）
2. 修改配置文件
3. 运行训练命令

```bash
python main.py --mode train --data_dir your_data_dir --config your_config.yaml
```

### 模型评估

训练完成后会自动生成：
- 混淆矩阵
- 训练历史图
- 评估指标报告

### 实时应用

```python
from src.inference import RealTimeInference
from src.data_processor import load_config

config = load_config('config.yaml')
inference = RealTimeInference('checkpoints/best_model.pth', config, device)

# 处理单帧
annotated_frame, info = inference.process_frame(frame)
```

## 扩展功能

### 添加新的意图类别
1. 修改 `config.yaml` 中的 `num_classes` 和 `class_names`
2. 准备对应标签的训练数据
3. 重新训练模型

### 集成到机器人系统
1. 使用 `RealTimeInference` 类
2. 获取预测结果
3. 转换为机器人控制指令

## 注意事项

1. **GPU推荐**：训练和实时推理建议使用GPU
2. **摄像头要求**：确保摄像头能清晰捕捉人体姿态
3. **环境光照**：良好的光照条件有助于提升检测精度
4. **人员距离**：建议人员在摄像头前1-3米距离

## 故障排除

### 常见问题

1. **CUDA内存不足**：减小batch_size或sequence_length
2. **检测不到人体**：调整YOLOv8置信度阈值
3. **预测不稳定**：增加sequence_length或改善数据质量

### 性能调优

1. **提升精度**：增加训练数据、调整模型参数
2. **提升速度**：使用更小的模型、减少序列长度
3. **减少内存**：使用混合精度训练、减小批次大小

## 技术支持

如有问题，请检查：
1. 依赖包版本是否正确
2. 配置文件格式是否正确
3. 数据格式是否符合要求
4. 设备驱动是否正常

## 更新日志

- v1.0.0: 初始版本，支持基本的姿态意图识别功能

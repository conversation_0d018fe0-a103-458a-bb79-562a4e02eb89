"""
基于经典计算机视觉的行走意图识别
实现您提出的完整方案：特征工程 + 传统机器学习
"""

import cv2
import numpy as np
import os
from collections import deque
from ultralytics import YOLO
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import pickle
import math

class ClassicalCVIntentRecognizer:
    def __init__(self, window_size=15):
        # 意图类别
        self.intent_classes = ['STRAIGHT', 'LEFT', 'RIGHT', 'STOP']
        self.intent_colors = [
            (0, 255, 0),    # STRAIGHT - 绿色
            (255, 0, 0),    # LEFT - 蓝色
            (0, 0, 255),    # RIGHT - 红色
            (0, 255, 255)   # STOP - 黄色
        ]
        
        # 时间窗口大小
        self.window_size = window_size
        
        # 关键点历史数据
        self.keypoints_history = deque(maxlen=window_size)
        self.timestamps = deque(maxlen=window_size)
        
        # 特征缓存
        self.feature_cache = deque(maxlen=100)
        
        # 模型和预处理器
        self.model = None
        self.scaler = None
        self.feature_names = []
        
        # 姿态检测模型
        print("加载YOLOv8姿态检测模型...")
        self.pose_model = YOLO('yolov8n-pose.pt')
        print("✓ 模型加载成功")
        
        # 关键点索引
        self.keypoint_indices = {
            'nose': 0, 'left_eye': 1, 'right_eye': 2, 'left_ear': 3, 'right_ear': 4,
            'left_shoulder': 5, 'right_shoulder': 6, 'left_elbow': 7, 'right_elbow': 8,
            'left_wrist': 9, 'right_wrist': 10, 'left_hip': 11, 'right_hip': 12,
            'left_knee': 13, 'right_knee': 14, 'left_ankle': 15, 'right_ankle': 16
        }
        
        print("经典CV方案特点:")
        print("- 基于几何特征工程")
        print("- 运动方向 vs 身体朝向分析")
        print("- 时间窗口统计特征")
        print("- 传统机器学习分类")
    
    def angle_normalize(self, angle):
        """角度标准化到[-π, π]"""
        while angle > math.pi:
            angle -= 2 * math.pi
        while angle < -math.pi:
            angle += 2 * math.pi
        return angle
    
    def angle_difference(self, angle1, angle2):
        """计算两个角度的差值，处理环绕问题"""
        diff = angle1 - angle2
        return self.angle_normalize(diff)
    
    def calculate_representative_points(self, keypoints, confidence):
        """计算代表点"""
        points = {}
        
        # 1. 骨盆中心 (COM)
        if confidence[11] > 0.4 and confidence[12] > 0.4:
            points['com'] = (keypoints[11] + keypoints[12]) / 2
        
        # 2. 质心 (所有可见关键点的平均)
        valid_points = []
        for i, conf in enumerate(confidence):
            if conf > 0.4:
                valid_points.append(keypoints[i])
        if valid_points:
            points['centroid'] = np.mean(valid_points, axis=0)
        
        # 3. 脚部中点
        if confidence[15] > 0.4 and confidence[16] > 0.4:
            points['foot_mid'] = (keypoints[15] + keypoints[16]) / 2
        
        # 4. 肩膀中心
        if confidence[5] > 0.4 and confidence[6] > 0.4:
            points['shoulder_center'] = (keypoints[5] + keypoints[6]) / 2
        
        return points
    
    def calculate_body_orientation(self, keypoints, confidence):
        """计算身体朝向"""
        orientations = {}
        
        # 1. 躯干朝向角 (肩髋线方法)
        if (confidence[5] > 0.4 and confidence[6] > 0.4 and 
            confidence[11] > 0.4 and confidence[12] > 0.4):
            
            shoulder_vec = keypoints[6] - keypoints[5]  # 右肩 - 左肩
            hip_vec = keypoints[12] - keypoints[11]     # 右髋 - 左髋
            
            # 平均方向
            avg_vec = (shoulder_vec + hip_vec) / 2
            trunk_angle = math.atan2(avg_vec[1], avg_vec[0])
            orientations['trunk_angle'] = trunk_angle
        
        # 2. 肩膀朝向角
        if confidence[5] > 0.4 and confidence[6] > 0.4:
            shoulder_vec = keypoints[6] - keypoints[5]
            shoulder_angle = math.atan2(shoulder_vec[1], shoulder_vec[0])
            orientations['shoulder_angle'] = shoulder_angle
        
        # 3. 髋部朝向角
        if confidence[11] > 0.4 and confidence[12] > 0.4:
            hip_vec = keypoints[12] - keypoints[11]
            hip_angle = math.atan2(hip_vec[1], hip_vec[0])
            orientations['hip_angle'] = hip_angle
        
        return orientations
    
    def extract_motion_features(self):
        """提取运动特征"""
        if len(self.keypoints_history) < 3:
            return {}
        
        features = {}
        
        # 获取代表点序列
        com_sequence = []
        centroid_sequence = []
        foot_mid_sequence = []
        
        for kp, conf in self.keypoints_history:
            points = self.calculate_representative_points(kp, conf)
            if 'com' in points:
                com_sequence.append(points['com'])
            if 'centroid' in points:
                centroid_sequence.append(points['centroid'])
            if 'foot_mid' in points:
                foot_mid_sequence.append(points['foot_mid'])
        
        # 计算运动特征
        for seq_name, sequence in [('com', com_sequence), ('centroid', centroid_sequence), ('foot_mid', foot_mid_sequence)]:
            if len(sequence) < 3:
                continue
            
            sequence = np.array(sequence)
            
            # 1. 速度矢量
            velocities = []
            for i in range(1, len(sequence)):
                velocity = sequence[i] - sequence[i-1]
                velocities.append(velocity)
            
            if not velocities:
                continue
            
            velocities = np.array(velocities)
            
            # 2. 运动方向角
            movement_angles = []
            for vel in velocities:
                if np.linalg.norm(vel) > 1e-6:
                    angle = math.atan2(vel[1], vel[0])
                    movement_angles.append(angle)
            
            if not movement_angles:
                continue
            
            # 3. 运动方向角变化率 (关键特征!)
            angle_changes = []
            for i in range(1, len(movement_angles)):
                change = self.angle_difference(movement_angles[i], movement_angles[i-1])
                angle_changes.append(change)
            
            # 4. 曲率计算
            curvatures = []
            for i in range(len(angle_changes)):
                if len(velocities) > i and np.linalg.norm(velocities[i]) > 1e-6:
                    curvature = abs(angle_changes[i]) / np.linalg.norm(velocities[i])
                    curvatures.append(curvature)
            
            # 5. 特征统计
            prefix = f"{seq_name}_"
            
            # 速度特征
            speed_magnitudes = [np.linalg.norm(v) for v in velocities]
            features[f"{prefix}speed_mean"] = np.mean(speed_magnitudes)
            features[f"{prefix}speed_std"] = np.std(speed_magnitudes)
            features[f"{prefix}speed_max"] = np.max(speed_magnitudes)
            
            # 运动方向角特征
            if movement_angles:
                features[f"{prefix}move_angle_mean"] = np.mean(movement_angles)
                features[f"{prefix}move_angle_std"] = np.std(movement_angles)
            
            # 运动方向角变化率特征 (核心!)
            if angle_changes:
                features[f"{prefix}angle_change_mean"] = np.mean(angle_changes)
                features[f"{prefix}angle_change_std"] = np.std(angle_changes)
                features[f"{prefix}angle_change_sum"] = np.sum(angle_changes)
                features[f"{prefix}angle_change_abs_mean"] = np.mean(np.abs(angle_changes))
                
                # 符号一致性
                positive_changes = sum(1 for x in angle_changes if x > 0)
                features[f"{prefix}angle_change_consistency"] = abs(positive_changes - len(angle_changes)/2) / (len(angle_changes)/2)
            
            # 曲率特征
            if curvatures:
                features[f"{prefix}curvature_mean"] = np.mean(curvatures)
                features[f"{prefix}curvature_max"] = np.max(curvatures)
        
        return features
    
    def extract_orientation_features(self):
        """提取身体朝向特征"""
        if len(self.keypoints_history) < 3:
            return {}
        
        features = {}
        
        # 获取朝向角序列
        trunk_angles = []
        shoulder_angles = []
        hip_angles = []
        
        for kp, conf in self.keypoints_history:
            orientations = self.calculate_body_orientation(kp, conf)
            if 'trunk_angle' in orientations:
                trunk_angles.append(orientations['trunk_angle'])
            if 'shoulder_angle' in orientations:
                shoulder_angles.append(orientations['shoulder_angle'])
            if 'hip_angle' in orientations:
                hip_angles.append(orientations['hip_angle'])
        
        # 计算朝向特征
        for angle_name, angles in [('trunk', trunk_angles), ('shoulder', shoulder_angles), ('hip', hip_angles)]:
            if len(angles) < 3:
                continue
            
            # 角度变化率
            angle_changes = []
            for i in range(1, len(angles)):
                change = self.angle_difference(angles[i], angles[i-1])
                angle_changes.append(change)
            
            if angle_changes:
                prefix = f"{angle_name}_"
                features[f"{prefix}angle_change_mean"] = np.mean(angle_changes)
                features[f"{prefix}angle_change_std"] = np.std(angle_changes)
                features[f"{prefix}angle_change_sum"] = np.sum(angle_changes)
        
        return features
    
    def extract_motion_orientation_alignment(self):
        """提取运动方向与身体朝向的对齐特征 (关键!)"""
        if len(self.keypoints_history) < 3:
            return {}
        
        features = {}
        
        # 获取最近的运动方向和身体朝向
        alignments = []
        
        for i in range(1, len(self.keypoints_history)):
            kp_prev, conf_prev = self.keypoints_history[i-1]
            kp_curr, conf_curr = self.keypoints_history[i]
            
            # 计算运动方向
            points_prev = self.calculate_representative_points(kp_prev, conf_prev)
            points_curr = self.calculate_representative_points(kp_curr, conf_curr)
            
            if 'com' in points_prev and 'com' in points_curr:
                movement_vec = points_curr['com'] - points_prev['com']
                if np.linalg.norm(movement_vec) > 1e-6:
                    movement_angle = math.atan2(movement_vec[1], movement_vec[0])
                    
                    # 计算身体朝向
                    orientations = self.calculate_body_orientation(kp_curr, conf_curr)
                    if 'trunk_angle' in orientations:
                        trunk_angle = orientations['trunk_angle']
                        
                        # 计算对齐度 (α_t = θ_trunk - θ_move)
                        alignment = self.angle_difference(trunk_angle, movement_angle)
                        alignments.append(alignment)
        
        if alignments:
            features['alignment_mean'] = np.mean(alignments)
            features['alignment_std'] = np.std(alignments)
            features['alignment_abs_mean'] = np.mean(np.abs(alignments))
        
        return features
    
    def extract_all_features(self):
        """提取所有特征"""
        if len(self.keypoints_history) < self.window_size // 2:
            return None
        
        features = {}
        
        # 1. 运动特征
        motion_features = self.extract_motion_features()
        features.update(motion_features)
        
        # 2. 朝向特征
        orientation_features = self.extract_orientation_features()
        features.update(orientation_features)
        
        # 3. 运动-朝向对齐特征
        alignment_features = self.extract_motion_orientation_alignment()
        features.update(alignment_features)
        
        # 4. 确保特征向量长度一致
        if not self.feature_names:
            self.feature_names = sorted(features.keys())
        
        # 构建特征向量
        feature_vector = []
        for name in self.feature_names:
            feature_vector.append(features.get(name, 0.0))
        
        return np.array(feature_vector)
    
    def update_and_extract_features(self, keypoints, confidence, timestamp):
        """更新历史数据并提取特征"""
        # 添加到历史
        self.keypoints_history.append((keypoints.copy(), confidence.copy()))
        self.timestamps.append(timestamp)
        
        # 提取特征
        features = self.extract_all_features()
        
        return features
    
    def train_model(self, training_data, labels, model_type='random_forest'):
        """训练分类模型"""
        print(f"开始训练{model_type}模型...")
        print(f"训练数据: {len(training_data)}个样本, {len(training_data[0])}维特征")
        
        # 数据预处理
        X = np.array(training_data)
        y = np.array(labels)
        
        # 特征标准化
        self.scaler = StandardScaler()
        X_scaled = self.scaler.fit_transform(X)
        
        # 划分训练测试集
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # 选择模型
        if model_type == 'random_forest':
            self.model = RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                class_weight='balanced'
            )
        elif model_type == 'svm':
            self.model = SVC(
                kernel='rbf',
                C=1.0,
                gamma='scale',
                class_weight='balanced',
                probability=True
            )
        else:
            raise ValueError(f"不支持的模型类型: {model_type}")
        
        # 训练模型
        self.model.fit(X_train, y_train)
        
        # 评估模型
        train_score = self.model.score(X_train, y_train)
        test_score = self.model.score(X_test, y_test)
        
        print(f"训练准确率: {train_score:.3f}")
        print(f"测试准确率: {test_score:.3f}")
        
        # 详细评估
        y_pred = self.model.predict(X_test)
        print("\n分类报告:")

        # 获取实际存在的类别
        unique_labels = sorted(np.unique(np.concatenate([y_test, y_pred])))
        target_names = [self.intent_classes[i] for i in unique_labels]

        print(classification_report(y_test, y_pred, labels=unique_labels, target_names=target_names))
        
        # 特征重要性 (如果是随机森林)
        if hasattr(self.model, 'feature_importances_'):
            importances = self.model.feature_importances_
            feature_importance = list(zip(self.feature_names, importances))
            feature_importance.sort(key=lambda x: x[1], reverse=True)
            
            print("\n前10个重要特征:")
            for name, importance in feature_importance[:10]:
                print(f"  {name}: {importance:.4f}")
        
        return test_score
    
    def predict(self, features):
        """预测意图"""
        if self.model is None or self.scaler is None:
            return None, 0.0
        
        if features is None or len(features) != len(self.feature_names):
            return None, 0.0
        
        # 特征标准化
        features_scaled = self.scaler.transform([features])
        
        # 预测
        prediction = self.model.predict(features_scaled)[0]
        
        # 预测概率
        if hasattr(self.model, 'predict_proba'):
            probabilities = self.model.predict_proba(features_scaled)[0]
            confidence = np.max(probabilities)
        else:
            confidence = 0.8  # 默认置信度
        
        return prediction, confidence
    
    def save_model(self, filepath):
        """保存模型"""
        model_data = {
            'model': self.model,
            'scaler': self.scaler,
            'feature_names': self.feature_names,
            'intent_classes': self.intent_classes
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)
        
        print(f"模型已保存到: {filepath}")
    
    def load_model(self, filepath):
        """加载模型"""
        with open(filepath, 'rb') as f:
            model_data = pickle.load(f)
        
        self.model = model_data['model']
        self.scaler = model_data['scaler']
        self.feature_names = model_data['feature_names']
        self.intent_classes = model_data['intent_classes']
        
        print(f"模型已从{filepath}加载")

def collect_training_data_from_video(video_path, recognizer, labels_per_segment=None):
    """从视频收集训练数据"""
    print(f"从视频收集训练数据: {video_path}")
    
    if not os.path.exists(video_path):
        print(f"错误: 视频文件不存在")
        return [], []
    
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("错误: 无法打开视频")
        return [], []
    
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    
    # 如果没有提供标签，使用简单的分段策略
    if labels_per_segment is None:
        # 假设前40%直行，中间30%左转，后30%直行
        labels_per_segment = []
        for frame_num in range(total_frames):
            progress = frame_num / total_frames
            if progress < 0.4:
                labels_per_segment.append(0)  # STRAIGHT
            elif progress < 0.7:
                labels_per_segment.append(1)  # LEFT
            else:
                labels_per_segment.append(0)  # STRAIGHT
    
    training_features = []
    training_labels = []
    
    frame_count = 0
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            timestamp = frame_count / fps
            
            # 姿态检测
            results = recognizer.pose_model(frame, conf=0.3, verbose=False)
            
            if len(results) > 0 and results[0].keypoints is not None:
                keypoints_data = results[0].keypoints.data
                
                if len(keypoints_data) > 0:
                    keypoints = keypoints_data[0]
                    
                    if keypoints.shape[0] == 17:
                        xy = keypoints[:, :2].cpu().numpy()
                        conf = keypoints[:, 2].cpu().numpy()
                        
                        # 归一化关键点
                        height, width = frame.shape[:2]
                        normalized_xy = xy.copy()
                        normalized_xy[:, 0] /= width
                        normalized_xy[:, 1] /= height
                        
                        # 提取特征
                        features = recognizer.update_and_extract_features(
                            normalized_xy, conf, timestamp
                        )
                        
                        if features is not None and frame_count < len(labels_per_segment):
                            training_features.append(features)
                            training_labels.append(labels_per_segment[frame_count])
            
            if frame_count % 100 == 0:
                progress = frame_count / total_frames * 100
                print(f"数据收集进度: {progress:.1f}%")
    
    finally:
        cap.release()
    
    print(f"收集完成: {len(training_features)}个特征样本")
    return training_features, training_labels

if __name__ == '__main__':
    # 创建识别器
    recognizer = ClassicalCVIntentRecognizer(window_size=15)
    
    # 从视频收集训练数据
    print("=== 收集训练数据 ===")
    training_features, training_labels = collect_training_data_from_video(
        'left.mp4', recognizer
    )
    
    if len(training_features) > 50:  # 确保有足够的训练数据
        # 训练模型
        print("\n=== 训练模型 ===")
        test_accuracy = recognizer.train_model(
            training_features, training_labels, model_type='random_forest'
        )
        
        # 保存模型
        recognizer.save_model('classical_cv_intent_model.pkl')
        
        print(f"\n🎉 经典CV意图识别模型训练完成!")
        print(f"测试准确率: {test_accuracy:.3f}")
        print("特点:")
        print("- ✅ 基于几何特征工程")
        print("- ✅ 运动方向变化率分析")
        print("- ✅ 身体朝向-运动对齐度")
        print("- ✅ 传统机器学习分类")
        print("- ✅ 完全可解释的特征")
    else:
        print("训练数据不足，请检查视频处理流程")

"""
简单的直行-左转识别
专门识别"前半段稳定直行，后半段明显左转"
"""

import cv2
import numpy as np
import os
from collections import deque
from ultralytics import YOLO

class SimpleStraightLeftRecognizer:
    def __init__(self):
        # 意图类别
        self.intent_classes = ['STRAIGHT', 'LEFT', 'RIGHT', 'TURN_AROUND', 'STOP']
        self.intent_colors = [
            (0, 255, 0),    # STRAIGHT - 绿色
            (255, 0, 0),    # LEFT - 蓝色
            (0, 0, 255),    # RIGHT - 红色
            (255, 0, 255),  # TURN_AROUND - 紫色
            (0, 255, 255)   # STOP - 黄色
        ]
        
        # 简化的阈值
        self.movement_threshold = 0.000005
        
        # 直行检测：角度变化的标准差要小
        self.straight_angle_std_threshold = 0.03  # 角度变化标准差阈值
        self.straight_angle_range_threshold = 0.08  # 角度变化范围阈值
        
        # 左转检测：累积角度变化
        self.left_turn_cumulative_threshold = -0.15  # 累积左转阈值
        self.left_turn_trend_threshold = -0.05       # 左转趋势阈值
        
        # 时序数据缓冲区
        self.body_center_sequence = deque(maxlen=15)
        self.body_angle_sequence = deque(maxlen=15)
        
        # 视频信息
        self.total_frames = 0
        
        # 加载姿态检测模型
        print("加载YOLOv8姿态检测模型...")
        self.model = YOLO('yolov8n-pose.pt')
        print("✓ 模型加载成功")
        print("简化逻辑: 稳定角度=直行, 累积左转=左转")
    
    def set_video_info(self, total_frames):
        """设置视频总帧数"""
        self.total_frames = total_frames
    
    def extract_features(self, keypoints, confidence):
        """提取基本特征"""
        features = {}
        
        # 身体中心和角度
        if all(confidence[i] > 0.4 for i in [5, 6, 11, 12]):
            shoulder_center = (keypoints[5] + keypoints[6]) / 2
            hip_center = (keypoints[11] + keypoints[12]) / 2
            body_center = (shoulder_center + hip_center) / 2
            
            # 身体朝向角度
            body_vector = shoulder_center - hip_center
            body_angle = np.degrees(np.arctan2(body_vector[1], body_vector[0]))
            
            features['body_center'] = body_center
            features['body_angle'] = body_angle
        
        return features
    
    def analyze_movement(self):
        """分析移动状态"""
        if len(self.body_center_sequence) < 3:
            return False, 0.0
        
        centers = np.array(list(self.body_center_sequence)[-5:])
        
        movements = []
        for i in range(1, len(centers)):
            movement = np.linalg.norm(centers[i] - centers[i-1])
            movements.append(movement)
        
        avg_movement = np.mean(movements) if movements else 0
        is_moving = avg_movement > self.movement_threshold
        
        return is_moving, avg_movement
    
    def analyze_angle_stability(self):
        """分析角度稳定性 - 关键函数"""
        if len(self.body_angle_sequence) < 10:
            return None, {}
        
        angles = list(self.body_angle_sequence)
        
        # 计算角度变化
        angle_changes = []
        for i in range(1, len(angles)):
            change = angles[i] - angles[i-1]
            
            # 处理角度跨越
            if change > 180:
                change -= 360
            elif change < -180:
                change += 360
            
            angle_changes.append(change)
        
        if not angle_changes:
            return None, {}
        
        # 关键统计量
        angle_std = np.std(angle_changes)           # 标准差 - 衡量稳定性
        angle_range = max(angle_changes) - min(angle_changes)  # 范围
        cumulative_change = sum(angle_changes)      # 累积变化
        avg_change = np.mean(angle_changes)         # 平均变化
        
        analysis = {
            'angle_std': angle_std,
            'angle_range': angle_range,
            'cumulative_change': cumulative_change,
            'avg_change': avg_change,
            'angle_changes': angle_changes
        }
        
        # 判断逻辑
        intent = None
        confidence = 0.0
        reason = ""
        
        # 1. 首先检查是否稳定（直行）
        if (angle_std < self.straight_angle_std_threshold and 
            angle_range < self.straight_angle_range_threshold):
            intent = 'STRAIGHT'
            confidence = 0.9 - angle_std * 10  # 越稳定置信度越高
            reason = f"stable_angles (std={angle_std:.4f}, range={angle_range:.4f})"
        
        # 2. 检查是否有明显的左转趋势
        elif (cumulative_change < self.left_turn_cumulative_threshold or 
              avg_change < self.left_turn_trend_threshold):
            intent = 'LEFT'
            confidence = min(0.9, abs(cumulative_change) / 0.3 + abs(avg_change) / 0.1)
            reason = f"left_turn (cum={cumulative_change:.4f}, avg={avg_change:.4f})"
        
        # 3. 检查右转（对称逻辑）
        elif (cumulative_change > -self.left_turn_cumulative_threshold or 
              avg_change > -self.left_turn_trend_threshold):
            intent = 'RIGHT'
            confidence = min(0.9, abs(cumulative_change) / 0.3 + abs(avg_change) / 0.1)
            reason = f"right_turn (cum={cumulative_change:.4f}, avg={avg_change:.4f})"
        
        # 4. 默认情况
        else:
            intent = 'STRAIGHT'
            confidence = 0.5
            reason = "default_straight"
        
        analysis['intent'] = intent
        analysis['confidence'] = confidence
        analysis['reason'] = reason
        
        return intent, analysis
    
    def classify_intent_simple(self, frame_num):
        """简单的意图分类"""
        
        # 1. 检查移动状态
        is_moving, movement_strength = self.analyze_movement()
        
        if not is_moving:
            return 4, 0.8, {'reason': 'not_moving', 'movement': movement_strength}
        
        # 2. 分析角度稳定性
        intent, analysis = self.analyze_angle_stability()
        
        if intent is None:
            return None, 0.0, {'reason': 'insufficient_data'}
        
        # 3. 转换为数字ID
        intent_id = None
        if intent == 'STRAIGHT':
            intent_id = 0
        elif intent == 'LEFT':
            intent_id = 1
        elif intent == 'RIGHT':
            intent_id = 2
        
        return intent_id, analysis['confidence'], analysis
    
    def update_and_classify(self, keypoints, confidence, frame_num):
        """更新数据并分类"""
        
        # 提取特征
        features = self.extract_features(keypoints, confidence)
        
        # 更新历史数据
        if 'body_center' in features:
            self.body_center_sequence.append(features['body_center'])
        
        if 'body_angle' in features:
            self.body_angle_sequence.append(features['body_angle'])
        
        # 进行分类
        if (len(self.body_center_sequence) >= 3 and 
            len(self.body_angle_sequence) >= 10):
            
            intent, confidence, debug_info = self.classify_intent_simple(frame_num)
            
            # 添加进度信息
            progress = frame_num / self.total_frames if self.total_frames > 0 else 0
            debug_info['progress'] = progress
            debug_info['frame'] = frame_num
            
            return intent, confidence, debug_info
        
        return None, 0.0, {'reason': 'insufficient_data', 'frame': frame_num}

def analyze_video_simple_straight_left(video_path='left.mp4', output_path='left_simple_straight_left_analysis.mp4'):
    """
    使用简单直行-左转方法分析视频
    """
    print(f"开始简单直行-左转识别: {video_path}")
    print("逻辑: 角度稳定=直行, 累积左转=左转")
    
    if not os.path.exists(video_path):
        print(f"错误: 视频文件不存在")
        return False
    
    # 创建识别器
    recognizer = SimpleStraightLeftRecognizer()
    
    # 打开视频
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("错误: 无法打开视频")
        return False
    
    # 视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    recognizer.set_video_info(total_frames)
    
    print(f"视频: {width}x{height}, {fps}FPS, {total_frames}帧")
    
    # 创建输出视频
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    frame_count = 0
    detection_count = 0
    analysis_count = 0
    intent_stats = {name: 0 for name in recognizer.intent_classes}
    intent_stats['ANALYZING'] = 0
    
    # 分段统计
    segment_stats = {'first_half': {}, 'second_half': {}}
    
    print("开始处理...")
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 姿态检测
            results = recognizer.model(frame, conf=0.3, verbose=False)
            
            annotated_frame = frame.copy()
            current_intent = None
            intent_confidence = 0.0
            debug_info = {}
            
            # 处理检测结果
            if len(results) > 0 and results[0].keypoints is not None:
                keypoints_data = results[0].keypoints.data
                
                if len(keypoints_data) > 0:
                    detection_count += 1
                    keypoints = keypoints_data[0]
                    
                    if keypoints.shape[0] == 17:
                        xy = keypoints[:, :2].cpu().numpy()
                        conf = keypoints[:, 2].cpu().numpy()
                        
                        # 归一化关键点
                        normalized_xy = xy.copy()
                        normalized_xy[:, 0] /= width
                        normalized_xy[:, 1] /= height
                        
                        # 简单分析
                        intent_id, confidence, debug_info = recognizer.update_and_classify(
                            normalized_xy, conf, frame_count
                        )
                        
                        if intent_id is not None:
                            current_intent = intent_id
                            intent_confidence = confidence
                            analysis_count += 1
                            
                            # 分段统计
                            segment = 'first_half' if frame_count <= total_frames // 2 else 'second_half'
                            intent_name = recognizer.intent_classes[intent_id]
                            segment_stats[segment][intent_name] = segment_stats[segment].get(intent_name, 0) + 1
                        
                        # 绘制关键点
                        for i, (x, y) in enumerate(xy):
                            if conf[i] > 0.3:
                                color = (0, 255, 0) if conf[i] > 0.7 else (0, 255, 255)
                                cv2.circle(annotated_frame, (int(x), int(y)), 6, color, -1)
                        
                        # 绘制骨架
                        connections = [
                            (5, 6), (5, 7), (7, 9), (6, 8), (8, 10),
                            (5, 11), (6, 12), (11, 12),
                            (11, 13), (13, 15), (12, 14), (14, 16)
                        ]
                        
                        for start_idx, end_idx in connections:
                            if conf[start_idx] > 0.3 and conf[end_idx] > 0.3:
                                start_point = (int(xy[start_idx][0]), int(xy[start_idx][1]))
                                end_point = (int(xy[end_idx][0]), int(xy[end_idx][1]))
                                cv2.line(annotated_frame, start_point, end_point, (255, 0, 0), 3)
                        
                        # 绘制身体中心轨迹
                        if len(recognizer.body_center_sequence) >= 2:
                            for i in range(1, len(recognizer.body_center_sequence)):
                                prev_center = recognizer.body_center_sequence[i-1] * np.array([width, height])
                                curr_center = recognizer.body_center_sequence[i] * np.array([width, height])
                                cv2.line(annotated_frame,
                                        (int(prev_center[0]), int(prev_center[1])),
                                        (int(curr_center[0]), int(curr_center[1])),
                                        (255, 255, 0), 4)
            
            # 更新统计
            if current_intent is not None:
                intent_stats[recognizer.intent_classes[current_intent]] += 1
            else:
                intent_stats['ANALYZING'] += 1
            
            # 绘制信息面板
            panel_width = 900
            panel_height = 380
            
            # 半透明背景
            overlay = annotated_frame.copy()
            cv2.rectangle(overlay, (10, 10), (panel_width, panel_height), (0, 0, 0), -1)
            cv2.addWeighted(overlay, 0.85, annotated_frame, 0.15, 0, annotated_frame)
            
            # 白色边框
            cv2.rectangle(annotated_frame, (10, 10), (panel_width, panel_height), (255, 255, 255), 3)
            
            # 显示信息
            y = 40
            line_height = 25
            
            # 标题
            cv2.putText(annotated_frame, "Simple Straight-Left Recognition", 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (255, 255, 255), 2)
            y += line_height + 5
            
            # 进度信息
            progress = frame_count / total_frames * 100
            half = "前半段" if frame_count <= total_frames // 2 else "后半段"
            cv2.putText(annotated_frame, f'进度: {progress:.1f}% ({half}) - 帧: {frame_count}/{total_frames}', 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            y += line_height
            
            # 进度条
            bar_width = 500
            bar_height = 15
            cv2.rectangle(annotated_frame, (25, y), (25 + bar_width, y + bar_height), (100, 100, 100), -1)
            cv2.rectangle(annotated_frame, (25, y), (25 + int(bar_width * progress / 100), y + bar_height), (0, 255, 0), -1)
            
            # 中点标记
            mid_x = 25 + bar_width // 2
            cv2.line(annotated_frame, (mid_x, y), (mid_x, y + bar_height), (255, 255, 255), 3)
            
            y += line_height + 10
            
            # 意图显示
            if current_intent is not None:
                intent_text = f'Intent: {recognizer.intent_classes[current_intent]}'
                confidence_text = f'Confidence: {intent_confidence:.2f}'
                intent_color = recognizer.intent_colors[current_intent]
                
                cv2.putText(annotated_frame, intent_text, 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.2, intent_color, 3)
                y += line_height + 5
                
                cv2.putText(annotated_frame, confidence_text, 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, intent_color, 2)
                y += line_height
                
                # 显示分析详情
                if debug_info:
                    reason = debug_info.get('reason', 'unknown')
                    cv2.putText(annotated_frame, f"Reason: {reason}", 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (200, 200, 200), 2)
                    y += line_height
                    
                    # 显示关键参数
                    if 'angle_std' in debug_info:
                        cv2.putText(annotated_frame, f"Angle Std: {debug_info['angle_std']:.4f}", 
                                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
                        y += 18
                    
                    if 'cumulative_change' in debug_info:
                        cv2.putText(annotated_frame, f"Cumulative: {debug_info['cumulative_change']:.4f}", 
                                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
                
                # 右上角大字显示
                intent_display = recognizer.intent_classes[current_intent]
                text_size = cv2.getTextSize(intent_display, cv2.FONT_HERSHEY_SIMPLEX, 2.5, 5)[0]
                
                cv2.rectangle(annotated_frame, 
                             (width - text_size[0] - 40, 20), 
                             (width - 20, 100), 
                             intent_color, -1)
                cv2.rectangle(annotated_frame, 
                             (width - text_size[0] - 40, 20), 
                             (width - 20, 100), 
                             (255, 255, 255), 3)
                cv2.putText(annotated_frame, intent_display, 
                           (width - text_size[0] - 30, 70), 
                           cv2.FONT_HERSHEY_SIMPLEX, 2.5, (255, 255, 255), 5)
            else:
                cv2.putText(annotated_frame, 'Status: Building angle data...', 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (128, 128, 128), 3)
            
            # 写入视频
            writer.write(annotated_frame)
            
            # 显示进度
            if frame_count % 100 == 0:
                progress_pct = frame_count / total_frames * 100
                current_intent_name = recognizer.intent_classes[current_intent] if current_intent is not None else 'ANALYZING'
                print(f"进度: {progress_pct:.1f}% - {half} - 意图: {current_intent_name}")
    
    except Exception as e:
        print(f"处理错误: {e}")
        return False
    
    finally:
        cap.release()
        writer.release()
    
    # 分段统计分析
    print(f"\n=== 前后半段对比分析 ===")
    
    for segment_name, stats in segment_stats.items():
        if stats:
            total_frames_in_segment = sum(stats.values())
            main_intent = max(stats.keys(), key=lambda x: stats[x])
            main_intent_ratio = stats[main_intent] / total_frames_in_segment
            
            segment_display = "前半段" if segment_name == 'first_half' else "后半段"
            print(f"{segment_display}: 主要意图 = {main_intent} ({main_intent_ratio:.1%})")
            
            for intent, count in stats.items():
                ratio = count / total_frames_in_segment
                print(f"  {intent}: {count}帧 ({ratio:.1%})")
    
    print(f"\n=== 简单直行-左转识别完成 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测帧数: {detection_count}")
    print(f"分析帧数: {analysis_count}")
    print(f"检测率: {detection_count/frame_count*100:.1f}%")
    print(f"分析率: {analysis_count/frame_count*100:.1f}%")
    print(f"\n总体意图分布:")
    for intent, count in intent_stats.items():
        percentage = count / frame_count * 100 if frame_count > 0 else 0
        print(f"  {intent}: {count} 帧 ({percentage:.1f}%)")
    print(f"输出视频: {output_path}")
    
    return True

if __name__ == '__main__':
    success = analyze_video_simple_straight_left()
    if success:
        print("\n🎉 简单直行-左转识别完成！")
        print("特点:")
        print("- ✅ 基于角度稳定性检测直行")
        print("- ✅ 基于累积角度变化检测左转")
        print("- ✅ 前后半段对比分析")
        print("- ✅ 简化的判断逻辑")
    else:
        print("\n❌ 简单直行-左转识别失败！")

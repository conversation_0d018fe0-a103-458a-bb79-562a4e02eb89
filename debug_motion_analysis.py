"""
调试运动分析 - 查看实际的关键点数据特征
"""

import cv2
import numpy as np
import os
from collections import deque
from ultralytics import YOLO
import json

class MotionDebugger:
    def __init__(self):
        self.model = YOLO('yolov8n-pose.pt')
        self.frame_data = []
        
    def analyze_frame(self, frame, frame_num):
        """分析单帧数据"""
        results = self.model(frame, conf=0.3, verbose=False)
        
        if len(results) > 0 and results[0].keypoints is not None:
            keypoints_data = results[0].keypoints.data
            
            if len(keypoints_data) > 0:
                keypoints = keypoints_data[0]
                
                if keypoints.shape[0] == 17:
                    xy = keypoints[:, :2].cpu().numpy()
                    conf = keypoints[:, 2].cpu().numpy()
                    
                    height, width = frame.shape[:2]
                    
                    # 归一化
                    normalized_xy = xy.copy()
                    normalized_xy[:, 0] /= width
                    normalized_xy[:, 1] /= height
                    
                    # 提取关键特征
                    frame_features = {
                        'frame': frame_num,
                        'time': frame_num / 30.0,  # 假设30fps
                    }
                    
                    # 腿部特征
                    if all(conf[i] > 0.4 for i in [11, 12, 13, 14, 15, 16]):
                        # 膝盖高度
                        left_knee_y = normalized_xy[13][1]
                        right_knee_y = normalized_xy[14][1]
                        left_ankle_y = normalized_xy[15][1]
                        right_ankle_y = normalized_xy[16][1]
                        
                        left_knee_lift = left_ankle_y - left_knee_y
                        right_knee_lift = right_ankle_y - right_knee_y
                        
                        frame_features.update({
                            'left_knee_lift': float(left_knee_lift),
                            'right_knee_lift': float(right_knee_lift),
                            'knee_lift_diff': float(abs(left_knee_lift - right_knee_lift)),
                            'step_width': float(np.linalg.norm(normalized_xy[16] - normalized_xy[15]))
                        })
                    
                    # 身体角度特征
                    if all(conf[i] > 0.4 for i in [5, 6, 11, 12]):
                        shoulder_center = (normalized_xy[5] + normalized_xy[6]) / 2
                        hip_center = (normalized_xy[11] + normalized_xy[12]) / 2
                        body_center = (shoulder_center + hip_center) / 2
                        
                        # 身体朝向
                        body_vector = shoulder_center - hip_center
                        body_angle = np.degrees(np.arctan2(body_vector[1], body_vector[0]))
                        
                        # 肩膀角度
                        shoulder_vector = normalized_xy[6] - normalized_xy[5]
                        shoulder_angle = np.degrees(np.arctan2(shoulder_vector[1], shoulder_vector[0]))
                        
                        frame_features.update({
                            'body_center_x': float(body_center[0]),
                            'body_center_y': float(body_center[1]),
                            'body_angle': float(body_angle),
                            'shoulder_angle': float(shoulder_angle)
                        })
                    
                    self.frame_data.append(frame_features)
                    return frame_features
        
        return None

def debug_video_motion(video_path='left.mp4', sample_frames=200):
    """调试视频运动特征"""
    print(f"调试视频运动特征: {video_path}")
    print(f"采样帧数: {sample_frames}")
    
    if not os.path.exists(video_path):
        print(f"错误: 视频文件不存在")
        return False
    
    debugger = MotionDebugger()
    
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("错误: 无法打开视频")
        return False
    
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    
    # 计算采样间隔
    sample_interval = max(1, total_frames // sample_frames)
    
    frame_count = 0
    sampled_count = 0
    
    print("开始采样分析...")
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 按间隔采样
            if frame_count % sample_interval == 0:
                features = debugger.analyze_frame(frame, frame_count)
                if features:
                    sampled_count += 1
                    
                    if sampled_count % 20 == 0:
                        print(f"已采样 {sampled_count} 帧...")
    
    except Exception as e:
        print(f"处理错误: {e}")
        return False
    
    finally:
        cap.release()
    
    # 分析数据
    print(f"\n=== 运动特征分析 ===")
    print(f"总帧数: {frame_count}")
    print(f"采样帧数: {sampled_count}")
    
    if len(debugger.frame_data) == 0:
        print("没有有效的数据")
        return False
    
    # 保存原始数据
    with open('motion_debug_data.json', 'w') as f:
        json.dump(debugger.frame_data, f, indent=2)
    
    print(f"原始数据已保存: motion_debug_data.json")
    
    # 统计分析
    data = debugger.frame_data
    
    # 1. 腿部运动分析
    if any('left_knee_lift' in d for d in data):
        knee_lifts_left = [d['left_knee_lift'] for d in data if 'left_knee_lift' in d]
        knee_lifts_right = [d['right_knee_lift'] for d in data if 'right_knee_lift' in d]
        knee_diffs = [d['knee_lift_diff'] for d in data if 'knee_lift_diff' in d]
        step_widths = [d['step_width'] for d in data if 'step_width' in d]
        
        print(f"\n📊 腿部运动统计:")
        print(f"  左膝抬起: 平均={np.mean(knee_lifts_left):.4f}, 范围=[{np.min(knee_lifts_left):.4f}, {np.max(knee_lifts_left):.4f}]")
        print(f"  右膝抬起: 平均={np.mean(knee_lifts_right):.4f}, 范围=[{np.min(knee_lifts_right):.4f}, {np.max(knee_lifts_right):.4f}]")
        print(f"  膝盖差异: 平均={np.mean(knee_diffs):.4f}, 范围=[{np.min(knee_diffs):.4f}, {np.max(knee_diffs):.4f}]")
        print(f"  步幅宽度: 平均={np.mean(step_widths):.4f}, 范围=[{np.min(step_widths):.4f}, {np.max(step_widths):.4f}]")
        
        # 检测交替模式
        alternating_count = 0
        for i in range(len(data)):
            if 'left_knee_lift' in data[i] and 'right_knee_lift' in data[i]:
                left_lift = data[i]['left_knee_lift']
                right_lift = data[i]['right_knee_lift']
                
                # 检测明显的交替（一个高一个低）
                if abs(left_lift - right_lift) > 0.01:  # 降低阈值
                    alternating_count += 1
        
        print(f"  交替模式帧数: {alternating_count}/{len(data)} ({alternating_count/len(data)*100:.1f}%)")
    
    # 2. 身体移动分析
    if any('body_center_x' in d for d in data):
        centers_x = [d['body_center_x'] for d in data if 'body_center_x' in d]
        centers_y = [d['body_center_y'] for d in data if 'body_center_y' in d]
        
        # 计算移动距离
        movements = []
        for i in range(1, len(data)):
            if 'body_center_x' in data[i] and 'body_center_x' in data[i-1]:
                prev_center = np.array([data[i-1]['body_center_x'], data[i-1]['body_center_y']])
                curr_center = np.array([data[i]['body_center_x'], data[i]['body_center_y']])
                movement = np.linalg.norm(curr_center - prev_center)
                movements.append(movement)
        
        print(f"\n📊 身体移动统计:")
        print(f"  X坐标: 平均={np.mean(centers_x):.4f}, 范围=[{np.min(centers_x):.4f}, {np.max(centers_x):.4f}]")
        print(f"  Y坐标: 平均={np.mean(centers_y):.4f}, 范围=[{np.min(centers_y):.4f}, {np.max(centers_y):.4f}]")
        
        if movements:
            print(f"  帧间移动: 平均={np.mean(movements):.6f}, 最大={np.max(movements):.6f}")
            
            # 分析移动模式
            significant_movements = [m for m in movements if m > 0.001]  # 显著移动
            print(f"  显著移动帧: {len(significant_movements)}/{len(movements)} ({len(significant_movements)/len(movements)*100:.1f}%)")
    
    # 3. 身体角度分析
    if any('body_angle' in d for d in data):
        body_angles = [d['body_angle'] for d in data if 'body_angle' in d]
        shoulder_angles = [d['shoulder_angle'] for d in data if 'shoulder_angle' in d]
        
        # 计算角度变化
        body_angle_changes = []
        for i in range(1, len(data)):
            if 'body_angle' in data[i] and 'body_angle' in data[i-1]:
                change = data[i]['body_angle'] - data[i-1]['body_angle']
                # 处理角度跨越
                if change > 180:
                    change -= 360
                elif change < -180:
                    change += 360
                body_angle_changes.append(change)
        
        print(f"\n📊 身体角度统计:")
        print(f"  身体角度: 平均={np.mean(body_angles):.2f}°, 范围=[{np.min(body_angles):.2f}°, {np.max(body_angles):.2f}°]")
        print(f"  肩膀角度: 平均={np.mean(shoulder_angles):.2f}°, 范围=[{np.min(shoulder_angles):.2f}°, {np.max(shoulder_angles):.2f}°]")
        
        if body_angle_changes:
            print(f"  角度变化: 平均={np.mean(body_angle_changes):.4f}°, 最大={np.max(np.abs(body_angle_changes)):.4f}°")
            
            # 分析角度变化趋势
            significant_changes = [c for c in body_angle_changes if abs(c) > 0.5]  # 显著变化
            print(f"  显著角度变化帧: {len(significant_changes)}/{len(body_angle_changes)} ({len(significant_changes)/len(body_angle_changes)*100:.1f}%)")
            
            if significant_changes:
                left_changes = [c for c in significant_changes if c < 0]
                right_changes = [c for c in significant_changes if c > 0]
                print(f"  左转趋势: {len(left_changes)} 次")
                print(f"  右转趋势: {len(right_changes)} 次")
    
    # 4. 建议阈值
    print(f"\n💡 建议的检测阈值:")
    
    if movements:
        movement_threshold = np.mean(movements) + 2 * np.std(movements)
        print(f"  移动检测阈值: {movement_threshold:.6f} (当前可能用了 0.01)")
    
    if knee_diffs:
        knee_threshold = np.mean(knee_diffs) + np.std(knee_diffs)
        print(f"  膝盖差异阈值: {knee_threshold:.4f} (当前可能用了 0.01)")
    
    if body_angle_changes:
        angle_threshold = np.std(body_angle_changes) * 2
        print(f"  角度变化阈值: {angle_threshold:.4f}° (当前可能用了 3.0°)")
    
    # 5. 时间段分析
    print(f"\n📈 时间段分析:")
    
    # 分析前半段和后半段
    mid_point = len(data) // 2
    first_half = data[:mid_point]
    second_half = data[mid_point:]
    
    def analyze_segment(segment, name):
        if not segment:
            return
        
        print(f"  {name}:")
        
        # 移动分析
        if len(segment) > 1:
            seg_movements = []
            for i in range(1, len(segment)):
                if 'body_center_x' in segment[i] and 'body_center_x' in segment[i-1]:
                    prev_center = np.array([segment[i-1]['body_center_x'], segment[i-1]['body_center_y']])
                    curr_center = np.array([segment[i]['body_center_x'], segment[i]['body_center_y']])
                    movement = np.linalg.norm(curr_center - prev_center)
                    seg_movements.append(movement)
            
            if seg_movements:
                print(f"    平均移动: {np.mean(seg_movements):.6f}")
        
        # 角度变化分析
        seg_angle_changes = []
        for i in range(1, len(segment)):
            if 'body_angle' in segment[i] and 'body_angle' in segment[i-1]:
                change = segment[i]['body_angle'] - segment[i-1]['body_angle']
                if change > 180:
                    change -= 360
                elif change < -180:
                    change += 360
                seg_angle_changes.append(change)
        
        if seg_angle_changes:
            avg_change = np.mean(seg_angle_changes)
            print(f"    平均角度变化: {avg_change:.4f}° ({'左转' if avg_change < 0 else '右转' if avg_change > 0 else '直行'})")
    
    analyze_segment(first_half, "前半段")
    analyze_segment(second_half, "后半段")
    
    return True

if __name__ == '__main__':
    success = debug_video_motion()
    if success:
        print("\n🎉 运动特征调试完成！")
        print("请查看:")
        print("- motion_debug_data.json (原始数据)")
        print("- 上面的统计分析结果")
        print("- 建议的检测阈值")
    else:
        print("\n❌ 运动特征调试失败！")

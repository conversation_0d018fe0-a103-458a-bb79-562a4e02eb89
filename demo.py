"""
演示脚本：展示如何使用人体姿态意图识别系统
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from src.data_processor import load_config, PoseDataProcessor
from src.model import create_model
from src.inference import RealTimeInference
import json


def demo_data_processing():
    """演示数据处理功能"""
    print("=" * 50)
    print("演示数据处理功能")
    print("=" * 50)
    
    # 加载配置
    config = load_config('config.yaml')
    processor = PoseDataProcessor(config)
    
    # 生成示例姿态数据
    seq_len = 30
    num_keypoints = 17
    
    # 模拟原始姿态数据
    pose_sequence = np.random.randn(seq_len, num_keypoints, 2) * 0.1
    confidence = np.random.uniform(0.2, 1.0, (seq_len, num_keypoints))
    
    # 模拟一些遮挡（低置信度）
    confidence[10:15, 5:8] = 0.1  # 肩膀和肘部遮挡
    
    print(f"原始姿态序列形状: {pose_sequence.shape}")
    print(f"置信度序列形状: {confidence.shape}")
    print(f"遮挡点数量: {np.sum(confidence < 0.3)}")
    
    # 处理遮挡
    completed_pose = processor.handle_occlusion(pose_sequence, confidence)
    print(f"遮挡处理完成")
    
    # 归一化
    normalized_pose = processor.normalize_pose(completed_pose)
    print(f"姿态归一化完成")
    
    # 提取运动特征
    motion_features = processor.extract_motion_features(normalized_pose)
    print(f"运动特征形状: {motion_features.shape}")
    
    # 数据增强
    augmented_data = processor.augment_data(normalized_pose, label=1)
    print(f"数据增强后样本数: {len(augmented_data)}")
    
    return motion_features


def demo_model_architecture():
    """演示模型架构"""
    print("=" * 50)
    print("演示模型架构")
    print("=" * 50)
    
    # 加载配置
    config = load_config('config.yaml')
    
    # 创建模型
    model = create_model(config)
    
    # 打印模型结构
    print("\n模型结构:")
    print(model)
    
    # 模拟输入数据
    batch_size = 4
    seq_len = config['data']['sequence_length']
    num_keypoints = config['data']['pose_keypoints']
    
    # 输入: (batch_size, seq_len, num_keypoints, 6)
    dummy_input = torch.randn(batch_size, seq_len, num_keypoints, 6)
    
    print(f"\n输入数据形状: {dummy_input.shape}")
    
    # 前向传播
    model.eval()
    with torch.no_grad():
        outputs = model(dummy_input, return_attention=True)
    
    print("\n模型输出:")
    for key, value in outputs.items():
        if isinstance(value, torch.Tensor):
            print(f"  {key}: {value.shape}")
        else:
            print(f"  {key}: {value}")
    
    # 预测意图
    predicted_class, confidence = model.predict_intent(dummy_input)
    print(f"\n预测结果:")
    print(f"  预测类别: {predicted_class}")
    print(f"  预测置信度: {confidence}")
    
    return model


def demo_training_process():
    """演示训练过程（使用小数据集）"""
    print("=" * 50)
    print("演示训练过程")
    print("=" * 50)
    
    # 加载配置
    config = load_config('config.yaml')
    
    # 修改配置用于快速演示
    config['training']['num_epochs'] = 5
    config['training']['batch_size'] = 8
    
    # 创建小型数据集
    from src.data_processor import PoseIntentDataset
    
    # 生成演示数据
    demo_data = []
    for i in range(50):  # 小数据集
        seq_len = config['data']['sequence_length']
        num_keypoints = config['data']['pose_keypoints']
        
        pose_seq = np.random.randn(seq_len, num_keypoints, 2) * 0.1
        confidence = np.random.uniform(0.5, 1.0, (seq_len, num_keypoints))
        label = i % 4
        
        demo_data.append({
            'pose_sequence': pose_seq.tolist(),
            'confidence': confidence.tolist(),
            'label': label
        })
    
    # 保存演示数据
    with open('demo_data.json', 'w') as f:
        json.dump(demo_data, f)
    
    # 创建数据集
    dataset = PoseIntentDataset('demo_data.json', config, mode='train')
    
    from torch.utils.data import DataLoader
    dataloader = DataLoader(dataset, batch_size=8, shuffle=True)
    
    print(f"演示数据集大小: {len(dataset)}")
    
    # 创建模型和训练器
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = create_model(config)
    
    from src.trainer import Trainer
    trainer = Trainer(model, config, device)
    
    # 简单训练演示
    print("开始训练演示...")
    for epoch in range(2):  # 只训练2个epoch作为演示
        train_metrics = trainer.train_epoch(dataloader)
        print(f"Epoch {epoch+1}: Loss={train_metrics['total_loss']:.4f}, "
              f"Acc={train_metrics['accuracy']:.4f}")
    
    print("训练演示完成!")
    
    # 清理
    import os
    if os.path.exists('demo_data.json'):
        os.remove('demo_data.json')


def demo_inference_features():
    """演示推理功能"""
    print("=" * 50)
    print("演示推理功能")
    print("=" * 50)
    
    # 加载配置
    config = load_config('config.yaml')
    
    # 创建模型
    model = create_model(config)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 保存临时模型用于演示
    temp_model_path = 'temp_model.pth'
    torch.save({
        'model_state_dict': model.state_dict(),
        'config': config
    }, temp_model_path)
    
    try:
        # 创建推理器
        inference = RealTimeInference(temp_model_path, config, device)
        
        # 模拟关键点检测
        print("模拟关键点检测...")
        dummy_keypoints = np.random.rand(17, 2) * 640  # 模拟640x480图像中的关键点
        dummy_confidence = np.random.uniform(0.5, 1.0, 17)
        
        print(f"检测到的关键点形状: {dummy_keypoints.shape}")
        print(f"置信度形状: {dummy_confidence.shape}")
        
        # 归一化关键点
        normalized_kp = inference.normalize_keypoints(dummy_keypoints, (480, 640))
        print(f"归一化后关键点范围: x=[{normalized_kp[:, 0].min():.3f}, {normalized_kp[:, 0].max():.3f}], "
              f"y=[{normalized_kp[:, 1].min():.3f}, {normalized_kp[:, 1].max():.3f}]")
        
        # 更新缓冲区
        for i in range(config['data']['sequence_length']):
            # 模拟连续帧
            kp = normalized_kp + np.random.normal(0, 0.01, normalized_kp.shape)
            conf = dummy_confidence + np.random.normal(0, 0.05, dummy_confidence.shape)
            conf = np.clip(conf, 0, 1)
            
            inference.update_pose_buffer(kp, conf)
        
        print(f"缓冲区状态: {len(inference.pose_buffer)}/{inference.sequence_length}")
        
        # 预测意图
        predicted_class, confidence, attention_weights = inference.predict_intent()
        
        if predicted_class is not None:
            print(f"\n预测结果:")
            print(f"  意图类别: {config['class_names'][predicted_class]}")
            print(f"  置信度: {confidence:.4f}")
            if attention_weights is not None:
                print(f"  注意力权重形状: {attention_weights.shape}")
                print(f"  最大注意力时间步: {np.argmax(attention_weights)}")
        else:
            print("缓冲区数据不足，无法预测")
        
    finally:
        # 清理临时文件
        import os
        if os.path.exists(temp_model_path):
            os.remove(temp_model_path)


def demo_visualization():
    """演示可视化功能"""
    print("=" * 50)
    print("演示可视化功能")
    print("=" * 50)
    
    # 生成示例训练历史
    epochs = range(1, 21)
    train_loss = [1.5 - 0.05*i + 0.1*np.random.random() for i in epochs]
    val_loss = [1.6 - 0.04*i + 0.15*np.random.random() for i in epochs]
    train_acc = [0.3 + 0.03*i + 0.05*np.random.random() for i in epochs]
    val_acc = [0.25 + 0.035*i + 0.08*np.random.random() for i in epochs]
    
    # 绘制训练历史
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # 损失曲线
    ax1.plot(epochs, train_loss, 'b-', label='Train Loss', linewidth=2)
    ax1.plot(epochs, val_loss, 'r-', label='Validation Loss', linewidth=2)
    ax1.set_title('Training Loss', fontsize=14)
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 准确率曲线
    ax2.plot(epochs, train_acc, 'b-', label='Train Accuracy', linewidth=2)
    ax2.plot(epochs, val_acc, 'r-', label='Validation Accuracy', linewidth=2)
    ax2.set_title('Training Accuracy', fontsize=14)
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Accuracy')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('demo_training_curves.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print("训练曲线已保存到 demo_training_curves.png")
    
    # 生成混淆矩阵
    from sklearn.metrics import confusion_matrix
    import seaborn as sns
    
    # 模拟预测结果
    y_true = np.random.randint(0, 4, 100)
    y_pred = y_true.copy()
    # 添加一些错误预测
    error_indices = np.random.choice(100, 20, replace=False)
    y_pred[error_indices] = np.random.randint(0, 4, 20)
    
    cm = confusion_matrix(y_true, y_pred)
    
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
               xticklabels=['直行', '左转', '右转', '停止'],
               yticklabels=['直行', '左转', '右转', '停止'])
    plt.title('Confusion Matrix', fontsize=16)
    plt.ylabel('True Label', fontsize=14)
    plt.xlabel('Predicted Label', fontsize=14)
    plt.tight_layout()
    plt.savefig('demo_confusion_matrix.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print("混淆矩阵已保存到 demo_confusion_matrix.png")


def main():
    """运行所有演示"""
    print("人体姿态意图识别系统演示")
    print("=" * 50)
    
    try:
        # 1. 数据处理演示
        motion_features = demo_data_processing()
        
        # 2. 模型架构演示
        model = demo_model_architecture()
        
        # 3. 训练过程演示
        demo_training_process()
        
        # 4. 推理功能演示
        demo_inference_features()
        
        # 5. 可视化演示
        demo_visualization()
        
        print("\n" + "=" * 50)
        print("所有演示完成!")
        print("生成的文件:")
        print("  - demo_training_curves.png")
        print("  - demo_confusion_matrix.png")
        print("=" * 50)
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()

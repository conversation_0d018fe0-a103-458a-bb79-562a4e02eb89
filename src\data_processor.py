"""
人体姿态数据处理模块
包含数据预处理、归一化、遮挡处理和数据增强
"""

import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
import cv2
from typing import List, Tuple, Dict, Optional
import json
import os
from sklearn.preprocessing import StandardScaler
import pickle


class PoseDataProcessor:
    """姿态数据处理器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.pose_keypoints = config['data']['pose_keypoints']
        self.sequence_length = config['data']['sequence_length']
        self.input_dim = config['data']['input_dim']
        self.confidence_threshold = config['data']['confidence_threshold']
        
        # COCO格式关键点定义
        self.keypoint_names = [
            'nose', 'left_eye', 'right_eye', 'left_ear', 'right_ear',
            'left_shoulder', 'right_shoulder', 'left_elbow', 'right_elbow',
            'left_wrist', 'right_wrist', 'left_hip', 'right_hip',
            'left_knee', 'right_knee', 'left_ankle', 'right_ankle'
        ]
        
        # 关键关节索引（用于归一化和意图识别）
        self.key_joints = {
            'shoulders': [5, 6],  # 肩膀
            'hips': [11, 12],     # 髋部
            'knees': [13, 14],    # 膝盖
            'ankles': [15, 16],   # 脚踝
            'wrists': [9, 10],    # 手腕
            'elbows': [7, 8]      # 肘部
        }
        
        # 左右对称关节对
        self.symmetric_pairs = [
            (1, 2), (3, 4), (5, 6), (7, 8), (9, 10), 
            (11, 12), (13, 14), (15, 16)
        ]
    
    def normalize_pose(self, pose_sequence: np.ndarray) -> np.ndarray:
        """
        姿态归一化：以髋部中心为原点，肩宽为尺度
        Args:
            pose_sequence: (T, N, 2) 时间序列姿态数据
        Returns:
            normalized_pose: 归一化后的姿态数据
        """
        T, N, D = pose_sequence.shape
        normalized_pose = pose_sequence.copy()
        
        for t in range(T):
            # 计算髋部中心
            hip_center = np.mean(pose_sequence[t, self.key_joints['hips'], :], axis=0)
            
            # 平移到髋部中心
            normalized_pose[t] = pose_sequence[t] - hip_center
            
            # 计算肩宽进行尺度归一化
            shoulder_width = np.linalg.norm(
                pose_sequence[t, self.key_joints['shoulders'][0], :] - 
                pose_sequence[t, self.key_joints['shoulders'][1], :]
            )
            
            # 避免除零
            if shoulder_width > 1e-6:
                normalized_pose[t] = normalized_pose[t] / shoulder_width
        
        return normalized_pose
    
    def handle_occlusion(self, pose_sequence: np.ndarray, 
                        confidence: np.ndarray) -> np.ndarray:
        """
        处理关节遮挡：使用时间插值和对称性补全
        Args:
            pose_sequence: (T, N, 2) 姿态序列
            confidence: (T, N) 置信度序列
        Returns:
            completed_pose: 补全后的姿态序列
        """
        T, N, D = pose_sequence.shape
        completed_pose = pose_sequence.copy()
        
        for t in range(T):
            for n in range(N):
                if confidence[t, n] < self.confidence_threshold:
                    # 方法1：时间插值
                    interpolated = self._temporal_interpolation(
                        pose_sequence, confidence, t, n
                    )
                    
                    if interpolated is not None:
                        completed_pose[t, n] = interpolated
                    else:
                        # 方法2：对称性补全
                        symmetric = self._symmetric_completion(
                            pose_sequence, confidence, t, n
                        )
                        if symmetric is not None:
                            completed_pose[t, n] = symmetric
        
        return completed_pose
    
    def _temporal_interpolation(self, pose_sequence: np.ndarray, 
                               confidence: np.ndarray, 
                               t: int, n: int) -> Optional[np.ndarray]:
        """时间插值补全"""
        T = pose_sequence.shape[0]
        
        # 寻找前后有效帧
        prev_valid = None
        next_valid = None
        
        # 向前搜索
        for i in range(t-1, -1, -1):
            if confidence[i, n] >= self.confidence_threshold:
                prev_valid = i
                break
        
        # 向后搜索
        for i in range(t+1, T):
            if confidence[i, n] >= self.confidence_threshold:
                next_valid = i
                break
        
        # 线性插值
        if prev_valid is not None and next_valid is not None:
            alpha = (t - prev_valid) / (next_valid - prev_valid)
            return (1 - alpha) * pose_sequence[prev_valid, n] + \
                   alpha * pose_sequence[next_valid, n]
        elif prev_valid is not None:
            return pose_sequence[prev_valid, n]
        elif next_valid is not None:
            return pose_sequence[next_valid, n]
        
        return None
    
    def _symmetric_completion(self, pose_sequence: np.ndarray, 
                             confidence: np.ndarray, 
                             t: int, n: int) -> Optional[np.ndarray]:
        """对称性补全"""
        # 查找对称关节
        symmetric_joint = None
        for left, right in self.symmetric_pairs:
            if n == left:
                symmetric_joint = right
                break
            elif n == right:
                symmetric_joint = left
                break
        
        if symmetric_joint is not None and \
           confidence[t, symmetric_joint] >= self.confidence_threshold:
            # 使用对称关节的镜像位置
            symmetric_pos = pose_sequence[t, symmetric_joint].copy()
            symmetric_pos[0] = -symmetric_pos[0]  # 水平翻转
            return symmetric_pos
        
        return None
    
    def extract_motion_features(self, pose_sequence: np.ndarray) -> np.ndarray:
        """
        提取运动特征：速度和加速度
        Args:
            pose_sequence: (T, N, 2) 姿态序列
        Returns:
            motion_features: (T, N, 6) 包含位置、速度、加速度的特征
        """
        T, N, D = pose_sequence.shape
        
        # 计算速度（一阶差分）
        velocity = np.zeros_like(pose_sequence)
        velocity[1:] = np.diff(pose_sequence, axis=0)
        velocity[0] = velocity[1]  # 第一帧使用第二帧的速度
        
        # 计算加速度（二阶差分）
        acceleration = np.zeros_like(pose_sequence)
        acceleration[1:] = np.diff(velocity, axis=0)
        acceleration[0] = acceleration[1]  # 第一帧使用第二帧的加速度
        
        # 合并特征
        motion_features = np.concatenate([
            pose_sequence, velocity, acceleration
        ], axis=-1)  # (T, N, 6)
        
        return motion_features
    
    def augment_data(self, pose_sequence: np.ndarray, 
                    label: int) -> List[Tuple[np.ndarray, int]]:
        """
        数据增强
        Args:
            pose_sequence: 原始姿态序列
            label: 标签
        Returns:
            augmented_data: 增强后的数据列表
        """
        if not self.config['augmentation']['enabled']:
            return [(pose_sequence, label)]
        
        augmented_data = [(pose_sequence, label)]
        
        # 水平翻转
        if self.config['augmentation']['horizontal_flip']:
            flipped_pose, flipped_label = self._horizontal_flip(pose_sequence, label)
            augmented_data.append((flipped_pose, flipped_label))
        
        # 添加噪声
        noise_scale = self.config['augmentation']['noise_scale']
        if noise_scale > 0:
            noisy_pose = pose_sequence + np.random.normal(0, noise_scale, pose_sequence.shape)
            augmented_data.append((noisy_pose, label))
        
        # 旋转（小角度）
        rotation_angle = self.config['augmentation']['rotation_angle']
        if rotation_angle > 0:
            rotated_pose = self._rotate_pose(pose_sequence, rotation_angle)
            augmented_data.append((rotated_pose, label))
        
        return augmented_data
    
    def _horizontal_flip(self, pose_sequence: np.ndarray, 
                        label: int) -> Tuple[np.ndarray, int]:
        """水平翻转"""
        flipped_pose = pose_sequence.copy()
        flipped_pose[:, :, 0] = -flipped_pose[:, :, 0]  # 翻转x坐标
        
        # 交换左右关节
        for left_idx, right_idx in self.symmetric_pairs:
            flipped_pose[:, [left_idx, right_idx]] = \
                flipped_pose[:, [right_idx, left_idx]]
        
        # 翻转标签（左转变右转，右转变左转）
        if label == 1:  # 左转
            flipped_label = 2  # 右转
        elif label == 2:  # 右转
            flipped_label = 1  # 左转
        else:
            flipped_label = label
        
        return flipped_pose, flipped_label
    
    def _rotate_pose(self, pose_sequence: np.ndarray, 
                    max_angle: float) -> np.ndarray:
        """小角度旋转"""
        angle = np.random.uniform(-max_angle, max_angle) * np.pi / 180
        cos_a, sin_a = np.cos(angle), np.sin(angle)
        
        rotation_matrix = np.array([
            [cos_a, -sin_a],
            [sin_a, cos_a]
        ])
        
        rotated_pose = pose_sequence.copy()
        for t in range(pose_sequence.shape[0]):
            rotated_pose[t] = pose_sequence[t] @ rotation_matrix.T
        
        return rotated_pose


class PoseIntentDataset(Dataset):
    """姿态意图识别数据集"""
    
    def __init__(self, data_path: str, config: Dict, mode: str = 'train'):
        self.config = config
        self.mode = mode
        self.processor = PoseDataProcessor(config)
        
        # 加载数据
        self.data, self.labels = self._load_data(data_path)
        
        # 数据增强（仅训练时）
        if mode == 'train':
            self._augment_dataset()
        
        print(f"{mode.upper()} 数据集加载完成: {len(self.data)} 个样本")
    
    def _load_data(self, data_path: str) -> Tuple[List[np.ndarray], List[int]]:
        """加载数据"""
        data = []
        labels = []
        
        if os.path.exists(data_path):
            with open(data_path, 'r', encoding='utf-8') as f:
                dataset = json.load(f)
            
            for item in dataset:
                pose_seq = np.array(item['pose_sequence'])  # (T, N, 2)
                confidence = np.array(item['confidence'])   # (T, N)
                label = item['label']  # 0: 直行, 1: 左转, 2: 右转, 3: 停止
                
                # 处理遮挡
                completed_pose = self.processor.handle_occlusion(pose_seq, confidence)
                
                # 归一化
                normalized_pose = self.processor.normalize_pose(completed_pose)
                
                # 提取运动特征
                motion_features = self.processor.extract_motion_features(normalized_pose)
                
                data.append(motion_features)
                labels.append(label)
        else:
            # 生成模拟数据用于测试
            print(f"数据文件 {data_path} 不存在，生成模拟数据...")
            data, labels = self._generate_dummy_data()
        
        return data, labels
    
    def _generate_dummy_data(self) -> Tuple[List[np.ndarray], List[int]]:
        """生成模拟数据用于测试"""
        data = []
        labels = []
        
        num_samples = 1000
        seq_len = self.config['data']['sequence_length']
        num_keypoints = self.config['data']['pose_keypoints']
        
        for i in range(num_samples):
            # 生成随机姿态序列（包含运动特征）
            pose_seq = np.random.randn(seq_len, num_keypoints, 6) * 0.1
            
            # 添加一些运动模式
            label = i % 4  # 循环生成4种标签
            
            if label == 1:  # 左转
                # 添加向左的运动趋势
                pose_seq[:, :, 0] += np.linspace(0, -0.2, seq_len).reshape(-1, 1)
                pose_seq[:, :, 2] += np.linspace(0, -0.1, seq_len).reshape(-1, 1)  # 速度
            elif label == 2:  # 右转
                # 添加向右的运动趋势
                pose_seq[:, :, 0] += np.linspace(0, 0.2, seq_len).reshape(-1, 1)
                pose_seq[:, :, 2] += np.linspace(0, 0.1, seq_len).reshape(-1, 1)  # 速度
            elif label == 3:  # 停止
                # 减少运动幅度
                pose_seq *= 0.3
            
            data.append(pose_seq)
            labels.append(label)
        
        return data, labels
    
    def _augment_dataset(self):
        """数据增强"""
        augmented_data = []
        augmented_labels = []
        
        for pose_seq, label in zip(self.data, self.labels):
            # 只使用位置信息进行增强，然后重新计算运动特征
            pose_only = pose_seq[:, :, :2]  # 只取位置信息
            
            aug_samples = self.processor.augment_data(pose_only, label)
            for aug_pose, aug_label in aug_samples:
                # 重新计算运动特征
                motion_features = self.processor.extract_motion_features(aug_pose)
                augmented_data.append(motion_features)
                augmented_labels.append(aug_label)
        
        self.data = augmented_data
        self.labels = augmented_labels
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        pose_seq = torch.FloatTensor(self.data[idx])
        label = torch.LongTensor([self.labels[idx]])
        
        return pose_seq, label


def create_data_loaders(config: Dict, data_dir: str) -> Tuple[DataLoader, DataLoader, DataLoader]:
    """创建数据加载器"""
    
    # 创建数据集
    train_dataset = PoseIntentDataset(
        os.path.join(data_dir, 'train.json'), 
        config, 
        mode='train'
    )
    
    val_dataset = PoseIntentDataset(
        os.path.join(data_dir, 'val.json'), 
        config, 
        mode='val'
    )
    
    test_dataset = PoseIntentDataset(
        os.path.join(data_dir, 'test.json'), 
        config, 
        mode='test'
    )
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['training']['batch_size'],
        shuffle=True,
        num_workers=4,
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config['training']['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config['training']['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    return train_loader, val_loader, test_loader


def load_config(config_path: str) -> Dict:
    """加载配置文件"""
    import yaml

    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)

    return config

"""
双层GRU时空特征网络 - 基于前8帧姿态预测第9帧运动意图
使用深度学习方法进行端到端训练
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import cv2
from ultralytics import YOLO
from torch.utils.data import Dataset, DataLoader
import os
from collections import deque
import pickle

class DualGRUIntentPredictor(nn.Module):
    """
    双层GRU时空特征网络
    输入: 8帧人体姿态序列 (8, 17, 2) - 8帧，17个关键点，x,y坐标
    输出: 运动意图分类 (前进/左转/右转/停止)
    """
    
    def __init__(self, input_size=34, hidden_size=128, num_classes=4, dropout=0.2):
        super(DualGRUIntentPredictor, self).__init__()
        
        # 输入特征维度: 17个关键点 * 2坐标 = 34
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_classes = num_classes
        
        # 输入预处理层
        self.input_projection = nn.Linear(input_size, hidden_size)
        self.input_norm = nn.LayerNorm(hidden_size)
        
        # 第一层GRU - 提取低级时空特征
        self.gru1 = nn.GRU(
            input_size=hidden_size,
            hidden_size=hidden_size,
            batch_first=True,
            dropout=dropout
        )
        
        # 第二层GRU - 提取高级时空特征
        self.gru2 = nn.GRU(
            input_size=hidden_size,
            hidden_size=hidden_size,
            batch_first=True,
            dropout=dropout
        )
        
        # 注意力机制 - 关注重要的时间步
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_size,
            num_heads=8,
            dropout=dropout,
            batch_first=True
        )
        
        # 分类头
        self.classifier = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size // 2, num_classes)
        )
        
        # 意图类别
        self.intent_classes = ['STRAIGHT', 'LEFT', 'RIGHT', 'STOP']
        
        print("双层GRU时空特征网络初始化完成")
        print(f"- 输入维度: {input_size}")
        print(f"- 隐藏维度: {hidden_size}")
        print(f"- 输出类别: {num_classes}")
        print(f"- 意图类别: {self.intent_classes}")
    
    def forward(self, x):
        """
        前向传播
        x: (batch_size, sequence_length=8, input_size=34)
        """
        batch_size, seq_len, _ = x.shape
        
        # 输入预处理
        x = self.input_projection(x)  # (batch, 8, hidden_size)
        x = self.input_norm(x)
        
        # 第一层GRU - 提取基础时空特征
        gru1_out, _ = self.gru1(x)  # (batch, 8, hidden_size)
        
        # 第二层GRU - 提取高级时空特征
        gru2_out, _ = self.gru2(gru1_out)  # (batch, 8, hidden_size)
        
        # 注意力机制 - 关注重要时间步
        attn_out, _ = self.attention(gru2_out, gru2_out, gru2_out)
        
        # 使用最后一个时间步的特征进行分类
        final_features = attn_out[:, -1, :]  # (batch, hidden_size)
        
        # 分类预测
        logits = self.classifier(final_features)  # (batch, num_classes)
        
        return logits
    
    def predict_intent(self, pose_sequence):
        """
        预测运动意图
        pose_sequence: (8, 17, 2) numpy array
        """
        self.eval()
        with torch.no_grad():
            # 预处理输入
            x = torch.FloatTensor(pose_sequence).unsqueeze(0)  # (1, 8, 17, 2)
            x = x.view(1, 8, -1)  # (1, 8, 34)
            
            # 前向传播
            logits = self.forward(x)
            probabilities = torch.softmax(logits, dim=1)
            predicted_class = torch.argmax(logits, dim=1).item()
            
            return predicted_class, probabilities[0].numpy()

class PoseSequenceDataset(Dataset):
    """
    姿态序列数据集
    每个样本包含8帧连续的姿态数据和对应的意图标签
    """
    
    def __init__(self, sequences, labels):
        self.sequences = sequences  # List of (8, 17, 2) arrays
        self.labels = labels        # List of intent labels
        
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        sequence = torch.FloatTensor(self.sequences[idx]).view(8, -1)  # (8, 34)
        label = torch.LongTensor([self.labels[idx]])
        return sequence, label

class GRUTrainer:
    """
    双层GRU训练器
    """
    
    def __init__(self, model, device='cuda' if torch.cuda.is_available() else 'cpu'):
        self.model = model.to(device)
        self.device = device
        self.pose_model = YOLO('yolov8n-pose.pt')
        
        print(f"训练设备: {device}")
    
    def extract_pose_sequences_from_video(self, video_path, intent_label_func):
        """
        从视频中提取8帧姿态序列
        intent_label_func: 函数，根据帧进度返回意图标签
        """
        print(f"处理视频: {video_path}")
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print("无法打开视频")
            return [], []
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        
        # 存储所有帧的姿态数据
        all_poses = []
        frame_count = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 姿态检测
            results = self.pose_model(frame, conf=0.3, verbose=False)
            
            if len(results) > 0 and results[0].keypoints is not None:
                keypoints_data = results[0].keypoints.data
                
                if len(keypoints_data) > 0:
                    keypoints = keypoints_data[0]
                    
                    if keypoints.shape[0] == 17:
                        xy = keypoints[:, :2].cpu().numpy()
                        conf = keypoints[:, 2].cpu().numpy()
                        
                        # 归一化坐标
                        height, width = frame.shape[:2]
                        normalized_xy = xy.copy()
                        normalized_xy[:, 0] /= width
                        normalized_xy[:, 1] /= height
                        
                        # 只保留高置信度的关键点
                        valid_pose = normalized_xy.copy()
                        for i in range(17):
                            if conf[i] < 0.3:
                                valid_pose[i] = [0.0, 0.0]  # 低置信度设为0
                        
                        all_poses.append(valid_pose)
            
            if frame_count % 100 == 0:
                progress = frame_count / total_frames * 100
                print(f"  进度: {progress:.1f}%")
        
        cap.release()
        
        # 生成8帧序列
        sequences = []
        labels = []
        
        for i in range(len(all_poses) - 8):
            # 取连续8帧作为输入序列
            sequence = np.array(all_poses[i:i+8])  # (8, 17, 2)
            
            # 第9帧的标签作为目标
            frame_progress = (i + 8) / len(all_poses)
            label = intent_label_func(frame_progress)
            
            sequences.append(sequence)
            labels.append(label)
        
        print(f"  提取序列: {len(sequences)}个")
        return sequences, labels
    
    def prepare_training_data(self):
        """
        准备训练数据
        """
        all_sequences = []
        all_labels = []
        
        # 处理left.mp4 - 前半段直行，后半段左转
        if os.path.exists('left.mp4'):
            left_sequences, left_labels = self.extract_pose_sequences_from_video(
                'left.mp4',
                lambda progress: 0 if progress < 0.5 else 1  # STRAIGHT -> LEFT
            )
            all_sequences.extend(left_sequences)
            all_labels.extend(left_labels)
        
        # 处理right.mp4 - 前半段直行，后半段右转
        if os.path.exists('right.mp4'):
            right_sequences, right_labels = self.extract_pose_sequences_from_video(
                'right.mp4',
                lambda progress: 0 if progress < 0.5 else 2  # STRAIGHT -> RIGHT
            )
            all_sequences.extend(right_sequences)
            all_labels.extend(right_labels)
        
        print(f"总训练序列: {len(all_sequences)}")
        
        # 检查标签分布
        unique_labels, counts = np.unique(all_labels, return_counts=True)
        print("标签分布:")
        for label, count in zip(unique_labels, counts):
            print(f"  {self.model.intent_classes[label]}: {count}个序列")
        
        return all_sequences, all_labels
    
    def train(self, epochs=50, batch_size=32, learning_rate=0.001):
        """
        训练模型
        """
        print("=== 开始训练双层GRU模型 ===")
        
        # 准备数据
        sequences, labels = self.prepare_training_data()
        
        if len(sequences) < 100:
            print("训练数据不足，请检查视频文件")
            return
        
        # 创建数据集
        dataset = PoseSequenceDataset(sequences, labels)
        
        # 划分训练测试集
        train_size = int(0.8 * len(dataset))
        test_size = len(dataset) - train_size
        train_dataset, test_dataset = torch.utils.data.random_split(
            dataset, [train_size, test_size]
        )
        
        # 创建数据加载器
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
        
        # 优化器和损失函数
        optimizer = optim.Adam(self.model.parameters(), lr=learning_rate)
        criterion = nn.CrossEntropyLoss()
        
        # 训练循环
        best_accuracy = 0.0
        
        for epoch in range(epochs):
            # 训练阶段
            self.model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0
            
            for sequences_batch, labels_batch in train_loader:
                sequences_batch = sequences_batch.to(self.device)
                labels_batch = labels_batch.squeeze().to(self.device)
                
                optimizer.zero_grad()
                outputs = self.model(sequences_batch)
                loss = criterion(outputs, labels_batch)
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                train_total += labels_batch.size(0)
                train_correct += (predicted == labels_batch).sum().item()
            
            # 测试阶段
            self.model.eval()
            test_correct = 0
            test_total = 0
            
            with torch.no_grad():
                for sequences_batch, labels_batch in test_loader:
                    sequences_batch = sequences_batch.to(self.device)
                    labels_batch = labels_batch.squeeze().to(self.device)
                    
                    outputs = self.model(sequences_batch)
                    _, predicted = torch.max(outputs.data, 1)
                    test_total += labels_batch.size(0)
                    test_correct += (predicted == labels_batch).sum().item()
            
            train_accuracy = 100 * train_correct / train_total
            test_accuracy = 100 * test_correct / test_total
            
            if test_accuracy > best_accuracy:
                best_accuracy = test_accuracy
                torch.save(self.model.state_dict(), 'best_dual_gru_model.pth')
            
            if epoch % 10 == 0:
                print(f'Epoch [{epoch}/{epochs}]')
                print(f'  训练损失: {train_loss/len(train_loader):.4f}')
                print(f'  训练准确率: {train_accuracy:.2f}%')
                print(f'  测试准确率: {test_accuracy:.2f}%')
        
        print(f"\n训练完成！最佳测试准确率: {best_accuracy:.2f}%")
        return best_accuracy

def main():
    """
    主函数 - 演示双层GRU时空特征网络
    """
    print("=== 双层GRU时空特征网络 ===")
    print("任务: 基于前8帧姿态预测第9帧运动意图")
    print("方法: 深度学习 + 双层GRU + 注意力机制")
    
    # 创建模型
    model = DualGRUIntentPredictor(
        input_size=34,      # 17个关键点 * 2坐标
        hidden_size=128,    # GRU隐藏维度
        num_classes=4,      # 4种意图类别
        dropout=0.2
    )
    
    # 创建训练器
    trainer = GRUTrainer(model)
    
    # 训练模型
    accuracy = trainer.train(epochs=100, batch_size=16, learning_rate=0.001)
    
    print(f"\n🎉 双层GRU模型训练完成!")
    print(f"特点:")
    print(f"- ✅ 时空特征网络")
    print(f"- ✅ 深度学习训练")
    print(f"- ✅ 8帧序列预测")
    print(f"- ✅ 端到端学习")
    print(f"- ✅ 测试准确率: {accuracy:.2f}%")

if __name__ == '__main__':
    main()

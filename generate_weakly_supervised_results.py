"""
生成弱监督学习的可视化结果
为left.mp4和right.mp4生成带有预测结果的视频
让用户直观看到弱监督学习的判断是否正确
"""

import cv2
import numpy as np
from ultralytics import YOLO
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
import os
import math
import pickle

class WeaklySupervisedVisualizer:
    """
    弱监督学习可视化器
    生成带有预测结果的视频
    """
    
    def __init__(self, window_size=15):
        self.window_size = window_size
        self.pose_model = YOLO('yolov8n-pose.pt')
        self.model = None
        self.scaler = None
        
        # 加载已训练的弱监督模型
        self.load_or_train_model()
        
        print("=== 弱监督学习可视化器 ===")
        print("功能: 生成带预测结果的视频")
        print("目标: 验证弱监督学习的判断准确性")
    
    def load_or_train_model(self):
        """
        加载或训练弱监督模型
        """
        model_path = 'weakly_supervised_model.pkl'
        
        if os.path.exists(model_path):
            print("加载已训练的弱监督模型...")
            with open(model_path, 'rb') as f:
                model_data = pickle.load(f)
            self.model = model_data['model']
            self.scaler = model_data['scaler']
            print("✅ 模型加载成功")
        else:
            print("训练新的弱监督模型...")
            self.train_weakly_supervised_model()
            print("✅ 模型训练完成")
    
    def train_weakly_supervised_model(self):
        """
        训练弱监督模型
        """
        # 视频级别标签
        video_labels = {
            'left.mp4': 0,   # 包含左转
            'right.mp4': 1,  # 包含右转
        }
        
        all_features = []
        all_labels = []
        
        for video_path, label in video_labels.items():
            if os.path.exists(video_path):
                features = self.extract_video_features(video_path)
                if len(features) > 0:
                    all_features.extend(features)
                    all_labels.extend([label] * len(features))
        
        if len(all_features) > 100:
            # 训练模型
            X = np.array(all_features)
            y = np.array(all_labels)
            
            self.scaler = StandardScaler()
            X_scaled = self.scaler.fit_transform(X)
            
            self.model = RandomForestClassifier(
                n_estimators=100, random_state=42, class_weight='balanced'
            )
            self.model.fit(X_scaled, y)
            
            # 保存模型
            model_data = {
                'model': self.model,
                'scaler': self.scaler
            }
            with open('weakly_supervised_model.pkl', 'wb') as f:
                pickle.dump(model_data, f)
    
    def extract_video_features(self, video_path):
        """
        提取视频特征
        """
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return []
        
        all_features = []
        pose_history = []
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            results = self.pose_model(frame, conf=0.3, verbose=False)
            
            if len(results) > 0 and results[0].keypoints is not None:
                keypoints_data = results[0].keypoints.data
                
                if len(keypoints_data) > 0:
                    keypoints = keypoints_data[0]
                    
                    if keypoints.shape[0] == 17:
                        xy = keypoints[:, :2].cpu().numpy()
                        
                        height, width = frame.shape[:2]
                        normalized_xy = xy.copy()
                        normalized_xy[:, 0] /= width
                        normalized_xy[:, 1] /= height
                        
                        pose_history.append(normalized_xy)
                        
                        if len(pose_history) >= self.window_size:
                            features = self.calculate_motion_features(pose_history[-self.window_size:])
                            if features is not None:
                                all_features.append(features)
        
        cap.release()
        return all_features
    
    def calculate_motion_features(self, pose_sequence):
        """
        计算运动特征
        """
        if len(pose_sequence) < self.window_size:
            return None
        
        features = []
        key_points = [5, 6, 11, 12, 15, 16]  # 肩膀、髋部、脚踝
        
        for point_idx in key_points:
            trajectory = np.array([pose[point_idx] for pose in pose_sequence])
            
            # 速度特征
            velocities = np.diff(trajectory, axis=0)
            speed_magnitudes = [np.linalg.norm(v) for v in velocities]
            
            if speed_magnitudes:
                features.extend([
                    np.mean(speed_magnitudes),
                    np.std(speed_magnitudes),
                    np.max(speed_magnitudes),
                ])
            else:
                features.extend([0, 0, 0])
            
            # 方向变化特征
            if len(velocities) > 1:
                angles = []
                for v in velocities:
                    if np.linalg.norm(v) > 1e-6:
                        angles.append(math.atan2(v[1], v[0]))
                
                if len(angles) > 1:
                    angle_changes = []
                    for i in range(1, len(angles)):
                        change = angles[i] - angles[i-1]
                        while change > math.pi:
                            change -= 2 * math.pi
                        while change < -math.pi:
                            change += 2 * math.pi
                        angle_changes.append(change)
                    
                    if angle_changes:
                        features.extend([
                            np.mean(angle_changes),
                            np.std(angle_changes),
                            np.sum(np.abs(angle_changes)),
                        ])
                    else:
                        features.extend([0, 0, 0])
                else:
                    features.extend([0, 0, 0])
            else:
                features.extend([0, 0, 0])
        
        return np.array(features)
    
    def generate_result_video(self, input_video, output_video):
        """
        生成带有弱监督学习预测结果的视频
        """
        print(f"生成结果视频: {input_video} -> {output_video}")
        
        cap = cv2.VideoCapture(input_video)
        if not cap.isOpened():
            print(f"无法打开视频: {input_video}")
            return False
        
        # 视频信息
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        # 创建输出视频
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        writer = cv2.VideoWriter(output_video, fourcc, fps, (width, height))
        
        pose_history = []
        frame_count = 0
        predictions_history = []
        
        # 标签名称
        label_names = ['包含左转', '包含右转']
        label_colors = [(255, 0, 0), (0, 0, 255)]  # 蓝色左转，红色右转
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            annotated_frame = frame.copy()
            
            # 姿态检测
            results = self.pose_model(frame, conf=0.3, verbose=False)
            current_prediction = None
            confidence = 0.0
            
            if len(results) > 0 and results[0].keypoints is not None:
                keypoints_data = results[0].keypoints.data
                
                if len(keypoints_data) > 0:
                    keypoints = keypoints_data[0]
                    
                    if keypoints.shape[0] == 17:
                        xy = keypoints[:, :2].cpu().numpy()
                        conf = keypoints[:, 2].cpu().numpy()
                        
                        # 归一化
                        normalized_xy = xy.copy()
                        normalized_xy[:, 0] /= width
                        normalized_xy[:, 1] /= height
                        
                        pose_history.append(normalized_xy)
                        
                        # 绘制关键点和骨架
                        for i, (x, y) in enumerate(xy):
                            if conf[i] > 0.3:
                                color = (0, 255, 0) if conf[i] > 0.7 else (0, 255, 255)
                                cv2.circle(annotated_frame, (int(x), int(y)), 6, color, -1)
                        
                        # 绘制骨架连线
                        connections = [
                            (5, 6), (5, 7), (7, 9), (6, 8), (8, 10),
                            (5, 11), (6, 12), (11, 12),
                            (11, 13), (13, 15), (12, 14), (14, 16)
                        ]
                        
                        for start_idx, end_idx in connections:
                            if conf[start_idx] > 0.3 and conf[end_idx] > 0.3:
                                start_point = (int(xy[start_idx][0]), int(xy[start_idx][1]))
                                end_point = (int(xy[end_idx][0]), int(xy[end_idx][1]))
                                cv2.line(annotated_frame, start_point, end_point, (255, 0, 0), 3)
                        
                        # 弱监督预测
                        if len(pose_history) >= self.window_size and self.model is not None:
                            features = self.calculate_motion_features(pose_history[-self.window_size:])
                            if features is not None:
                                features_scaled = self.scaler.transform([features])
                                prediction = self.model.predict(features_scaled)[0]
                                probabilities = self.model.predict_proba(features_scaled)[0]
                                
                                current_prediction = prediction
                                confidence = probabilities[prediction]
                                predictions_history.append(prediction)
            
            # 显示弱监督学习结果
            if current_prediction is not None:
                label_text = label_names[current_prediction]
                label_color = label_colors[current_prediction]
                
                # 背景框
                font_scale = 2.0
                thickness = 4
                text_size = cv2.getTextSize(label_text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, thickness)[0]
                
                box_margin = 20
                box_x1 = width - text_size[0] - box_margin * 2
                box_y1 = 20
                box_x2 = width - 20
                box_y2 = 20 + text_size[1] + box_margin * 2
                
                cv2.rectangle(annotated_frame, (box_x1, box_y1), (box_x2, box_y2), label_color, -1)
                cv2.rectangle(annotated_frame, (box_x1, box_y1), (box_x2, box_y2), (255, 255, 255), 3)
                
                text_x = box_x1 + box_margin
                text_y = box_y1 + text_size[1] + box_margin
                cv2.putText(annotated_frame, label_text, (text_x, text_y), 
                           cv2.FONT_HERSHEY_SIMPLEX, font_scale, (255, 255, 255), thickness)
                
                # 显示置信度
                conf_text = f"{confidence:.1%}"
                conf_size = cv2.getTextSize(conf_text, cv2.FONT_HERSHEY_SIMPLEX, 1.0, 2)[0]
                conf_y = box_y2 + conf_size[1] + 10
                cv2.putText(annotated_frame, conf_text, (text_x, conf_y), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1.0, label_color, 2)
            
            # 显示进度信息
            progress_text = f"Frame: {frame_count}/{total_frames}"
            cv2.putText(annotated_frame, progress_text, (20, height - 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255, 255, 255), 2)
            
            # 显示弱监督学习标识
            method_text = "Weakly Supervised Learning"
            cv2.putText(annotated_frame, method_text, (20, 40), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 255, 255), 3)
            
            writer.write(annotated_frame)
            
            if frame_count % 100 == 0:
                progress_pct = frame_count / total_frames * 100
                print(f"  进度: {progress_pct:.1f}%")
        
        cap.release()
        writer.release()
        
        # 统计整体预测结果
        if predictions_history:
            unique_preds, counts = np.unique(predictions_history, return_counts=True)
            print(f"\n{input_video} 弱监督学习预测统计:")
            for pred, count in zip(unique_preds, counts):
                percentage = count / len(predictions_history) * 100
                print(f"  {label_names[pred]}: {count}个片段 ({percentage:.1f}%)")
            
            overall_pred = np.bincount(predictions_history).argmax()
            overall_conf = np.max(np.bincount(predictions_history)) / len(predictions_history)
            print(f"  整体判断: {label_names[overall_pred]} (置信度: {overall_conf:.1%})")
        
        print(f"✅ 结果视频已生成: {output_video}")
        return True

def main():
    """
    生成弱监督学习结果视频
    """
    print("=== 生成弱监督学习结果视频 ===")
    print("目标: 让您直观看到弱监督学习的判断是否正确")
    
    visualizer = WeaklySupervisedVisualizer()
    
    # 生成left.mp4的结果
    if os.path.exists('left.mp4'):
        success = visualizer.generate_result_video('left.mp4', 'left_weakly_supervised_result.mp4')
        if success:
            print("✅ left.mp4 弱监督学习结果已生成")
    
    # 生成right.mp4的结果
    if os.path.exists('right.mp4'):
        success = visualizer.generate_result_video('right.mp4', 'right_weakly_supervised_result.mp4')
        if success:
            print("✅ right.mp4 弱监督学习结果已生成")
    
    print(f"\n🎉 弱监督学习结果视频生成完成!")
    print("生成的文件:")
    print("- left_weakly_supervised_result.mp4")
    print("- right_weakly_supervised_result.mp4")
    print("\n请查看视频，验证弱监督学习的判断是否正确！")
    print("视频特点:")
    print("- ✅ 显示人体骨架")
    print("- ✅ 右上角显示预测结果")
    print("- ✅ 显示置信度")
    print("- ✅ 标注弱监督学习方法")

if __name__ == '__main__':
    main()

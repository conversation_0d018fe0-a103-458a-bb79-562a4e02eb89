"""
基于时序分析的行走意图识别
使用前8帧预测第9帧的运动轨迹来判断意图
"""

import cv2
import numpy as np
import os
from collections import deque
from ultralytics import YOLO
import math

class TemporalIntentRecognizer:
    def __init__(self, sequence_length=8):
        # 时序参数
        self.sequence_length = sequence_length
        self.prediction_horizon = 1  # 预测下一帧
        
        # 意图类别
        self.intent_classes = ['STRAIGHT', 'LEFT', 'RIGHT', 'TURN_AROUND', 'STOP']
        self.intent_colors = [
            (0, 255, 0),    # STRAIGHT - 绿色
            (255, 0, 0),    # LEFT - 蓝色
            (0, 0, 255),    # RIGHT - 红色
            (255, 0, 255),  # TURN_AROUND - 紫色
            (0, 255, 255)   # STOP - 黄色
        ]
        
        # 时序数据缓冲区
        self.pose_sequence = deque(maxlen=sequence_length + 1)  # +1 for current frame
        self.body_center_sequence = deque(maxlen=sequence_length + 1)
        self.shoulder_angle_sequence = deque(maxlen=sequence_length + 1)
        self.hip_angle_sequence = deque(maxlen=sequence_length + 1)
        self.velocity_sequence = deque(maxlen=sequence_length)
        
        # 关键点索引
        self.keypoints_idx = {
            'left_shoulder': 5, 'right_shoulder': 6,
            'left_hip': 11, 'right_hip': 12,
            'left_knee': 13, 'right_knee': 14,
            'left_ankle': 15, 'right_ankle': 16
        }
        
        # 加载姿态检测模型
        print("加载YOLOv8姿态检测模型...")
        self.model = YOLO('yolov8n-pose.pt')
        print("✓ 模型加载成功")
    
    def extract_key_features(self, keypoints, confidence):
        """提取关键特征"""
        features = {}
        
        # 1. 身体中心点
        if (confidence[5] > 0.4 and confidence[6] > 0.4 and 
            confidence[11] > 0.4 and confidence[12] > 0.4):
            shoulder_center = (keypoints[5] + keypoints[6]) / 2
            hip_center = (keypoints[11] + keypoints[12]) / 2
            body_center = (shoulder_center + hip_center) / 2
            features['body_center'] = body_center
        
        # 2. 肩膀朝向角度
        if confidence[5] > 0.4 and confidence[6] > 0.4:
            shoulder_vector = keypoints[6] - keypoints[5]
            shoulder_angle = np.degrees(np.arctan2(shoulder_vector[1], shoulder_vector[0]))
            features['shoulder_angle'] = shoulder_angle
        
        # 3. 髋部朝向角度
        if confidence[11] > 0.4 and confidence[12] > 0.4:
            hip_vector = keypoints[12] - keypoints[11]
            hip_angle = np.degrees(np.arctan2(hip_vector[1], hip_vector[0]))
            features['hip_angle'] = hip_angle
        
        # 4. 腿部特征
        if (confidence[13] > 0.4 and confidence[14] > 0.4 and 
            confidence[15] > 0.4 and confidence[16] > 0.4):
            # 膝盖中心
            knee_center = (keypoints[13] + keypoints[14]) / 2
            # 脚踝中心
            ankle_center = (keypoints[15] + keypoints[16]) / 2
            # 腿部朝向
            leg_vector = ankle_center - knee_center
            leg_angle = np.degrees(np.arctan2(leg_vector[1], leg_vector[0]))
            features['leg_angle'] = leg_angle
            
            # 步幅宽度
            step_width = np.linalg.norm(keypoints[16] - keypoints[15])
            features['step_width'] = step_width
        
        return features
    
    def predict_next_position(self, sequence_data, method='linear_extrapolation'):
        """预测下一帧的位置"""
        if len(sequence_data) < 3:
            return None
        
        sequence = np.array(list(sequence_data))
        
        if method == 'linear_extrapolation':
            # 线性外推法
            if len(sequence) >= 2:
                velocity = sequence[-1] - sequence[-2]
                predicted = sequence[-1] + velocity
                return predicted
        
        elif method == 'polynomial_fit':
            # 多项式拟合
            if len(sequence) >= 4:
                t = np.arange(len(sequence))
                
                # 对x和y坐标分别进行二次多项式拟合
                if sequence.ndim == 2:  # 2D位置数据
                    poly_x = np.polyfit(t, sequence[:, 0], min(2, len(sequence)-1))
                    poly_y = np.polyfit(t, sequence[:, 1], min(2, len(sequence)-1))
                    
                    next_t = len(sequence)
                    pred_x = np.polyval(poly_x, next_t)
                    pred_y = np.polyval(poly_y, next_t)
                    
                    return np.array([pred_x, pred_y])
                else:  # 1D角度数据
                    poly = np.polyfit(t, sequence, min(2, len(sequence)-1))
                    next_t = len(sequence)
                    predicted = np.polyval(poly, next_t)
                    return predicted
        
        elif method == 'velocity_based':
            # 基于速度变化的预测
            if len(sequence) >= 3:
                # 计算最近的速度和加速度
                v1 = sequence[-1] - sequence[-2]
                v2 = sequence[-2] - sequence[-3]
                acceleration = v1 - v2
                
                # 预测下一帧的速度和位置
                next_velocity = v1 + acceleration
                predicted = sequence[-1] + next_velocity
                return predicted
        
        return None
    
    def analyze_trajectory_pattern(self):
        """分析轨迹模式"""
        if len(self.body_center_sequence) < self.sequence_length:
            return None, 0.0, {}
        
        # 获取历史序列（前8帧）
        historical_positions = np.array(list(self.body_center_sequence)[:-1])
        current_position = np.array(list(self.body_center_sequence)[-1])
        
        # 预测下一帧位置
        predicted_position = self.predict_next_position(
            list(self.body_center_sequence)[:-1], 
            method='velocity_based'
        )
        
        if predicted_position is None:
            return None, 0.0, {}
        
        # 分析轨迹特征
        analysis = {}
        
        # 1. 运动方向分析
        movement_vector = predicted_position - current_position
        movement_distance = np.linalg.norm(movement_vector)
        movement_angle = np.degrees(np.arctan2(movement_vector[1], movement_vector[0]))
        
        analysis['movement_vector'] = movement_vector
        analysis['movement_distance'] = movement_distance
        analysis['movement_angle'] = movement_angle
        analysis['predicted_position'] = predicted_position
        
        # 2. 轨迹曲率分析
        if len(historical_positions) >= 6:
            # 计算轨迹的曲率变化
            recent_trajectory = historical_positions[-6:]
            
            # 计算连续三点的角度变化
            angle_changes = []
            for i in range(len(recent_trajectory) - 2):
                p1, p2, p3 = recent_trajectory[i], recent_trajectory[i+1], recent_trajectory[i+2]
                
                v1 = p2 - p1
                v2 = p3 - p2
                
                if np.linalg.norm(v1) > 1e-6 and np.linalg.norm(v2) > 1e-6:
                    # 计算角度变化
                    cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
                    cos_angle = np.clip(cos_angle, -1, 1)
                    angle_change = np.degrees(np.arccos(cos_angle))
                    
                    # 判断转向方向
                    cross_product = np.cross(v1, v2)
                    if cross_product > 0:
                        angle_change = -angle_change  # 左转为负
                    
                    angle_changes.append(angle_change)
            
            if angle_changes:
                avg_curvature = np.mean(angle_changes)
                curvature_trend = np.sum(angle_changes)  # 累积曲率
                analysis['avg_curvature'] = avg_curvature
                analysis['curvature_trend'] = curvature_trend
        
        # 3. 速度变化分析
        velocities = []
        for i in range(1, len(historical_positions)):
            velocity = np.linalg.norm(historical_positions[i] - historical_positions[i-1])
            velocities.append(velocity)
        
        if velocities:
            avg_velocity = np.mean(velocities)
            velocity_variance = np.var(velocities)
            velocity_trend = velocities[-1] - velocities[0] if len(velocities) > 1 else 0
            
            analysis['avg_velocity'] = avg_velocity
            analysis['velocity_variance'] = velocity_variance
            analysis['velocity_trend'] = velocity_trend
        
        # 4. 角度变化分析
        if len(self.shoulder_angle_sequence) >= self.sequence_length:
            shoulder_angles = list(self.shoulder_angle_sequence)[:-1]
            angle_changes = []
            
            for i in range(1, len(shoulder_angles)):
                change = shoulder_angles[i] - shoulder_angles[i-1]
                # 处理角度跨越
                if change > 180:
                    change -= 360
                elif change < -180:
                    change += 360
                angle_changes.append(change)
            
            if angle_changes:
                avg_angle_change = np.mean(angle_changes)
                angle_change_trend = np.sum(angle_changes)
                analysis['avg_angle_change'] = avg_angle_change
                analysis['angle_change_trend'] = angle_change_trend
        
        # 基于时序分析进行意图分类
        intent, confidence = self.classify_intent_temporal(analysis)
        
        return intent, confidence, analysis
    
    def classify_intent_temporal(self, analysis):
        """基于时序分析的意图分类"""
        
        # 获取关键特征
        movement_distance = analysis.get('movement_distance', 0)
        movement_angle = analysis.get('movement_angle', 0)
        avg_velocity = analysis.get('avg_velocity', 0)
        velocity_variance = analysis.get('velocity_variance', 0)
        curvature_trend = analysis.get('curvature_trend', 0)
        angle_change_trend = analysis.get('angle_change_trend', 0)
        
        # 意图得分
        scores = [0, 0, 0, 0, 0]  # [STRAIGHT, LEFT, RIGHT, TURN_AROUND, STOP]
        
        # 1. 停止判断（最高优先级）
        if avg_velocity < 0.005 and movement_distance < 0.01:
            scores[4] += 80  # STOP
            return 4, 0.9
        
        # 2. 基于预测轨迹的方向判断
        # 标准化角度到[-180, 180]
        while movement_angle > 180:
            movement_angle -= 360
        while movement_angle < -180:
            movement_angle += 360
        
        # 方向得分
        if abs(movement_angle) < 20:  # 向前
            scores[0] += 40  # STRAIGHT
        elif -90 < movement_angle < -20:  # 向左
            scores[1] += 40  # LEFT
        elif 20 < movement_angle < 90:  # 向右
            scores[2] += 40  # RIGHT
        elif abs(movement_angle) > 120:  # 向后（可能转身）
            scores[3] += 30  # TURN_AROUND
        
        # 3. 基于轨迹曲率判断转向
        if abs(curvature_trend) > 15:  # 显著曲率变化
            if curvature_trend < 0:  # 左转
                scores[1] += 35
            else:  # 右转
                scores[2] += 35
        
        # 4. 基于身体角度变化判断
        if abs(angle_change_trend) > 20:  # 显著角度变化
            if angle_change_trend < 0:  # 身体向左转
                scores[1] += 25
            else:  # 身体向右转
                scores[2] += 25
        
        # 5. 基于运动距离判断
        if movement_distance > 0.02:  # 明显移动
            scores[0] += 20  # 倾向于直行
        elif movement_distance < 0.008:  # 移动很少
            scores[4] += 30  # 倾向于停止
        
        # 6. 转身检测（大幅度角度变化）
        if abs(angle_change_trend) > 40:
            scores[3] += 50  # TURN_AROUND
        
        # 7. 速度一致性分析
        if velocity_variance < 0.0001:  # 速度很稳定
            if avg_velocity > 0.01:
                scores[0] += 15  # 稳定直行
            else:
                scores[4] += 15  # 稳定停止
        
        # 选择最高得分的意图
        max_score = max(scores)
        if max_score > 30:  # 置信度阈值
            predicted_intent = scores.index(max_score)
            confidence = min(0.95, max_score / 100.0)
            return predicted_intent, confidence
        
        return None, 0.0
    
    def update_and_predict(self, keypoints, confidence):
        """更新时序数据并进行预测"""
        
        # 提取当前帧特征
        features = self.extract_key_features(keypoints, confidence)
        
        # 更新时序数据
        if 'body_center' in features:
            self.body_center_sequence.append(features['body_center'])
        
        if 'shoulder_angle' in features:
            self.shoulder_angle_sequence.append(features['shoulder_angle'])
        
        if 'hip_angle' in features:
            self.hip_angle_sequence.append(features['hip_angle'])
        
        # 计算速度
        if len(self.body_center_sequence) >= 2:
            velocity = np.linalg.norm(
                self.body_center_sequence[-1] - self.body_center_sequence[-2]
            )
            self.velocity_sequence.append(velocity)
        
        # 进行时序分析和预测
        if len(self.body_center_sequence) >= self.sequence_length + 1:
            intent, confidence, analysis = self.analyze_trajectory_pattern()
            return intent, confidence, analysis
        
        return None, 0.0, {}

def analyze_video_temporal(video_path='left.mp4', output_path='left_temporal_analysis.mp4'):
    """
    使用时序方法分析视频
    """
    print(f"开始时序意图识别: {video_path}")
    
    if not os.path.exists(video_path):
        print(f"错误: 视频文件不存在")
        return False
    
    # 创建时序识别器
    recognizer = TemporalIntentRecognizer(sequence_length=8)
    
    # 打开视频
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("错误: 无法打开视频")
        return False
    
    # 视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"视频: {width}x{height}, {fps}FPS, {total_frames}帧")
    print(f"时序分析: 使用前{recognizer.sequence_length}帧预测第{recognizer.sequence_length+1}帧")
    
    # 创建输出视频
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    frame_count = 0
    detection_count = 0
    prediction_count = 0
    intent_stats = {name: 0 for name in recognizer.intent_classes}
    intent_stats['ANALYZING'] = 0
    
    print("开始处理...")
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 姿态检测
            results = recognizer.model(frame, conf=0.3, verbose=False)
            
            annotated_frame = frame.copy()
            current_intent = None
            intent_confidence = 0.0
            analysis_info = {}
            
            # 处理检测结果
            if len(results) > 0 and results[0].keypoints is not None:
                keypoints_data = results[0].keypoints.data
                
                if len(keypoints_data) > 0:
                    detection_count += 1
                    keypoints = keypoints_data[0]
                    
                    if keypoints.shape[0] == 17:
                        xy = keypoints[:, :2].cpu().numpy()
                        conf = keypoints[:, 2].cpu().numpy()
                        
                        # 归一化关键点
                        normalized_xy = xy.copy()
                        normalized_xy[:, 0] /= width
                        normalized_xy[:, 1] /= height
                        
                        # 时序分析和预测
                        intent_id, confidence, analysis_info = recognizer.update_and_predict(
                            normalized_xy, conf
                        )
                        
                        if intent_id is not None:
                            current_intent = intent_id
                            intent_confidence = confidence
                            prediction_count += 1
                        
                        # 绘制关键点和骨架
                        for i, (x, y) in enumerate(xy):
                            if conf[i] > 0.3:
                                color = (0, 255, 0) if conf[i] > 0.7 else (0, 255, 255)
                                cv2.circle(annotated_frame, (int(x), int(y)), 6, color, -1)
                        
                        # 绘制骨架
                        connections = [
                            (5, 6), (5, 7), (7, 9), (6, 8), (8, 10),
                            (5, 11), (6, 12), (11, 12),
                            (11, 13), (13, 15), (12, 14), (14, 16)
                        ]
                        
                        for start_idx, end_idx in connections:
                            if conf[start_idx] > 0.3 and conf[end_idx] > 0.3:
                                start_point = (int(xy[start_idx][0]), int(xy[start_idx][1]))
                                end_point = (int(xy[end_idx][0]), int(xy[end_idx][1]))
                                cv2.line(annotated_frame, start_point, end_point, (255, 0, 0), 3)
                        
                        # 绘制历史轨迹和预测
                        if len(recognizer.body_center_sequence) >= 2:
                            # 历史轨迹（蓝色）
                            for i in range(1, len(recognizer.body_center_sequence)):
                                prev_center = recognizer.body_center_sequence[i-1] * np.array([width, height])
                                curr_center = recognizer.body_center_sequence[i] * np.array([width, height])
                                cv2.line(annotated_frame,
                                        (int(prev_center[0]), int(prev_center[1])),
                                        (int(curr_center[0]), int(curr_center[1])),
                                        (255, 255, 0), 3)
                            
                            # 预测位置（红色圆圈）
                            if 'predicted_position' in analysis_info:
                                pred_pos = analysis_info['predicted_position'] * np.array([width, height])
                                cv2.circle(annotated_frame, (int(pred_pos[0]), int(pred_pos[1])), 
                                         12, (0, 0, 255), 3)
                                cv2.putText(annotated_frame, 'PRED', 
                                           (int(pred_pos[0])+15, int(pred_pos[1])), 
                                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
            
            # 更新统计
            if current_intent is not None:
                intent_stats[recognizer.intent_classes[current_intent]] += 1
            else:
                intent_stats['ANALYZING'] += 1
            
            # 绘制信息面板
            panel_width = 750
            panel_height = 320
            
            # 半透明背景
            overlay = annotated_frame.copy()
            cv2.rectangle(overlay, (10, 10), (panel_width, panel_height), (0, 0, 0), -1)
            cv2.addWeighted(overlay, 0.85, annotated_frame, 0.15, 0, annotated_frame)
            
            # 白色边框
            cv2.rectangle(annotated_frame, (10, 10), (panel_width, panel_height), (255, 255, 255), 3)
            
            # 显示信息
            y = 40
            line_height = 25
            
            # 标题
            cv2.putText(annotated_frame, "Temporal Intent Recognition (8-frame prediction)", 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
            y += line_height + 5
            
            # 帧信息
            cv2.putText(annotated_frame, f'Frame: {frame_count}/{total_frames} ({frame_count/fps:.1f}s)', 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            y += line_height
            
            # 时序状态
            sequence_status = f"Sequence: {len(recognizer.body_center_sequence)}/{recognizer.sequence_length+1}"
            cv2.putText(annotated_frame, sequence_status, 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            y += line_height
            
            # 意图显示
            if current_intent is not None:
                intent_text = f'Predicted Intent: {recognizer.intent_classes[current_intent]}'
                confidence_text = f'Confidence: {intent_confidence:.2f}'
                intent_color = recognizer.intent_colors[current_intent]
                
                cv2.putText(annotated_frame, intent_text, 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.0, intent_color, 3)
                y += line_height + 5
                
                cv2.putText(annotated_frame, confidence_text, 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, intent_color, 2)
                y += line_height
                
                # 显示分析特征
                if analysis_info:
                    if 'movement_distance' in analysis_info:
                        cv2.putText(annotated_frame, f"Move Dist: {analysis_info['movement_distance']:.4f}", 
                                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
                        y += 18
                    
                    if 'curvature_trend' in analysis_info:
                        cv2.putText(annotated_frame, f"Curvature: {analysis_info['curvature_trend']:.2f}", 
                                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
                        y += 18
                    
                    if 'avg_velocity' in analysis_info:
                        cv2.putText(annotated_frame, f"Avg Velocity: {analysis_info['avg_velocity']:.4f}", 
                                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
                
                # 右上角大字显示
                intent_display = recognizer.intent_classes[current_intent]
                text_size = cv2.getTextSize(intent_display, cv2.FONT_HERSHEY_SIMPLEX, 2.0, 4)[0]
                
                cv2.rectangle(annotated_frame, 
                             (width - text_size[0] - 40, 20), 
                             (width - 20, 80), 
                             intent_color, -1)
                cv2.rectangle(annotated_frame, 
                             (width - text_size[0] - 40, 20), 
                             (width - 20, 80), 
                             (255, 255, 255), 3)
                cv2.putText(annotated_frame, intent_display, 
                           (width - text_size[0] - 30, 60), 
                           cv2.FONT_HERSHEY_SIMPLEX, 2.0, (255, 255, 255), 4)
            else:
                cv2.putText(annotated_frame, 'Status: Building sequence...', 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (128, 128, 128), 3)
            
            # 写入视频
            writer.write(annotated_frame)
            
            # 显示进度
            if frame_count % 100 == 0:
                progress = frame_count / total_frames * 100
                current_intent_name = recognizer.intent_classes[current_intent] if current_intent is not None else 'ANALYZING'
                print(f"进度: {progress:.1f}% - 当前意图: {current_intent_name}")
    
    except Exception as e:
        print(f"处理错误: {e}")
        return False
    
    finally:
        cap.release()
        writer.release()
    
    print(f"\n=== 时序意图识别完成 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测帧数: {detection_count}")
    print(f"预测帧数: {prediction_count}")
    print(f"检测率: {detection_count/frame_count*100:.1f}%")
    print(f"预测率: {prediction_count/frame_count*100:.1f}%")
    print(f"\n意图分布:")
    for intent, count in intent_stats.items():
        percentage = count / frame_count * 100 if frame_count > 0 else 0
        print(f"  {intent}: {count} 帧 ({percentage:.1f}%)")
    print(f"输出视频: {output_path}")
    
    return True

if __name__ == '__main__':
    success = analyze_video_temporal()
    if success:
        print("\n🎉 时序意图识别完成！")
        print("特点:")
        print("- 基于前8帧预测第9帧轨迹")
        print("- 时序模式分析")
        print("- 轨迹预测可视化")
        print("- 更高的准确率")
    else:
        print("\n❌ 时序意图识别失败！")

"""
人体姿态 + 意图标注工具
为训练意图识别模型准备带标签的数据
"""

import cv2
import numpy as np
import os
import json
from ultralytics import YOLO
from datetime import datetime
import glob

class PoseIntentAnnotator:
    def __init__(self):
        # 5种意图类别
        self.intent_classes = {
            0: "straight",      # 直行
            1: "left",          # 左转
            2: "right",         # 右转
            3: "turn_around",   # 转身
            4: "stop"           # 停止
        }
        
        self.intent_names = {
            0: "直行",
            1: "左转", 
            2: "右转",
            3: "转身",
            4: "停止"
        }
        
        # COCO关键点定义
        self.coco_keypoints = [
            "nose", "left_eye", "right_eye", "left_ear", "right_ear",
            "left_shoulder", "right_shoulder", "left_elbow", "right_elbow",
            "left_wrist", "right_wrist", "left_hip", "right_hip",
            "left_knee", "right_knee", "left_ankle", "right_ankle"
        ]
        
        # 加载YOLOv8姿态检测模型
        print("加载YOLOv8n-pose模型...")
        try:
            self.model = YOLO('yolov8n-pose.pt')
            print("✓ 模型加载成功")
        except Exception as e:
            print(f"✗ 模型加载失败: {e}")
            raise e
    
    def annotate_with_intent(self, image_path, intent_label, output_dir=None, visualize=True):
        """
        标注图片的姿态关键点并添加意图标签
        
        Args:
            image_path: 图片路径
            intent_label: 意图标签 (0-4 或字符串)
            output_dir: 输出目录
            visualize: 是否生成可视化图片
        
        Returns:
            dict: 包含姿态和意图的标注数据
        """
        if not os.path.exists(image_path):
            print(f"错误: 图片文件不存在 - {image_path}")
            return None
        
        # 处理意图标签
        if isinstance(intent_label, str):
            # 如果是字符串，转换为数字
            intent_mapping = {v: k for k, v in self.intent_classes.items()}
            if intent_label in intent_mapping:
                intent_id = intent_mapping[intent_label]
            else:
                print(f"错误: 未知的意图标签 - {intent_label}")
                return None
        else:
            intent_id = intent_label
        
        if intent_id not in self.intent_classes:
            print(f"错误: 意图标签必须在 0-4 之间，当前: {intent_id}")
            return None
        
        # 读取图片
        image = cv2.imread(image_path)
        if image is None:
            print(f"错误: 无法读取图片 - {image_path}")
            return None
        
        height, width = image.shape[:2]
        
        # 姿态检测
        results = self.model(image, conf=0.3, verbose=False)
        
        # 准备数据
        image_info = {
            "id": 1,
            "width": width,
            "height": height,
            "file_name": os.path.basename(image_path),
            "intent_label": intent_id,
            "intent_name": self.intent_classes[intent_id],
            "intent_chinese": self.intent_names[intent_id]
        }
        
        annotations = []
        
        if len(results) > 0 and results[0].keypoints is not None:
            keypoints_data = results[0].keypoints.data
            
            for person_id, keypoints in enumerate(keypoints_data):
                if keypoints.shape[0] == 17:
                    xy = keypoints[:, :2].cpu().numpy()
                    conf = keypoints[:, 2].cpu().numpy()
                    
                    # 转换为COCO格式
                    coco_keypoints = []
                    num_keypoints = 0
                    
                    for i in range(17):
                        x, y = xy[i]
                        visibility = 2 if conf[i] > 0.5 else 1 if conf[i] > 0.3 else 0
                        coco_keypoints.extend([float(x), float(y), int(visibility)])
                        if visibility > 0:
                            num_keypoints += 1
                    
                    # 计算边界框
                    visible_points = xy[conf > 0.3]
                    if len(visible_points) > 0:
                        x_min, y_min = np.min(visible_points, axis=0)
                        x_max, y_max = np.max(visible_points, axis=0)
                        bbox_width = x_max - x_min
                        bbox_height = y_max - y_min
                        area = bbox_width * bbox_height
                        
                        annotation = {
                            "id": person_id + 1,
                            "image_id": 1,
                            "category_id": 1,
                            "bbox": [float(x_min), float(y_min), float(bbox_width), float(bbox_height)],
                            "area": float(area),
                            "iscrowd": 0,
                            "keypoints": coco_keypoints,
                            "num_keypoints": num_keypoints,
                            "intent_label": intent_id,
                            "intent_name": self.intent_classes[intent_id]
                        }
                        
                        annotations.append(annotation)
        
        # 构建完整数据
        pose_intent_data = {
            "info": {
                "description": "Human Pose Keypoints with Intent Labels Dataset",
                "version": "1.0",
                "year": datetime.now().year,
                "contributor": "YOLOv8n-pose + Intent Annotation",
                "date_created": datetime.now().isoformat()
            },
            "images": [image_info],
            "annotations": annotations,
            "intent_classes": self.intent_classes,
            "categories": [
                {
                    "id": 1,
                    "name": "person",
                    "supercategory": "person",
                    "keypoints": self.coco_keypoints
                }
            ]
        }
        
        # 保存结果
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            
            base_name = os.path.splitext(os.path.basename(image_path))[0]
            json_path = os.path.join(output_dir, f"{base_name}_intent_{self.intent_classes[intent_id]}.json")
            
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(pose_intent_data, f, indent=2, ensure_ascii=False)
            
            print(f"✓ 保存标注数据: {json_path}")
            
            # 生成可视化图片
            if visualize:
                vis_image = self.visualize_pose_intent(image, annotations, intent_id)
                vis_path = os.path.join(output_dir, f"{base_name}_intent_{self.intent_classes[intent_id]}_vis.jpg")
                cv2.imwrite(vis_path, vis_image)
                print(f"✓ 保存可视化图片: {vis_path}")
        
        return pose_intent_data
    
    def visualize_pose_intent(self, image, annotations, intent_id):
        """
        可视化姿态关键点和意图标签
        """
        vis_image = image.copy()
        
        # 意图颜色
        intent_colors = {
            0: (0, 255, 0),    # 直行 - 绿色
            1: (255, 0, 0),    # 左转 - 蓝色
            2: (0, 0, 255),    # 右转 - 红色
            3: (255, 0, 255),  # 转身 - 紫色
            4: (0, 255, 255)   # 停止 - 黄色
        }
        
        intent_color = intent_colors.get(intent_id, (255, 255, 255))
        
        # 关键点颜色
        keypoint_colors = [
            (255, 0, 0), (255, 85, 0), (255, 170, 0), (255, 255, 0),
            (170, 255, 0), (85, 255, 0), (0, 255, 0), (0, 255, 85),
            (0, 255, 170), (0, 255, 255), (0, 170, 255), (0, 85, 255),
            (0, 0, 255), (85, 0, 255), (170, 0, 255), (255, 0, 255),
            (255, 0, 170)
        ]
        
        for ann in annotations:
            keypoints = ann['keypoints']
            
            # 绘制关键点
            for i in range(0, len(keypoints), 3):
                x, y, v = keypoints[i], keypoints[i+1], keypoints[i+2]
                if v > 0:
                    color = keypoint_colors[i//3 % len(keypoint_colors)]
                    cv2.circle(vis_image, (int(x), int(y)), 6, color, -1)
                    cv2.circle(vis_image, (int(x), int(y)), 6, (255, 255, 255), 2)
            
            # 绘制骨架 - 包含备用连接方案
            skeleton = [
                # 头部连接 (主要)
                (0, 1), (0, 2),  # 鼻子到眼睛
                (1, 3), (2, 4),  # 眼睛到耳朵
                # 头部连接 (备用) - 如果鼻子/眼睛不可见，耳朵连到肩膀
                (3, 5), (4, 6),  # 耳朵到肩膀
                # 上身连接
                (5, 6),          # 肩膀连接
                (5, 7), (7, 9),  # 左臂
                (6, 8), (8, 10), # 右臂
                # 躯干连接
                (5, 11), (6, 12), # 肩膀到髋部
                (11, 12),         # 髋部连接
                # 下身连接
                (11, 13), (13, 15), # 左腿
                (12, 14), (14, 16)  # 右腿
            ]
            
            for start_idx, end_idx in skeleton:
                start_pt_idx = start_idx * 3
                end_pt_idx = end_idx * 3
                
                if (start_pt_idx < len(keypoints) and end_pt_idx < len(keypoints) and
                    keypoints[start_pt_idx + 2] > 0 and keypoints[end_pt_idx + 2] > 0):
                    
                    start_pt = (int(keypoints[start_pt_idx]), int(keypoints[start_pt_idx + 1]))
                    end_pt = (int(keypoints[end_pt_idx]), int(keypoints[end_pt_idx + 1]))
                    cv2.line(vis_image, start_pt, end_pt, intent_color, 3)
            
            # 绘制边界框
            bbox = ann['bbox']
            x, y, w, h = bbox
            cv2.rectangle(vis_image, (int(x), int(y)), (int(x+w), int(y+h)), intent_color, 3)
        
        # 添加意图标签文字
        intent_text = f"Intent: {self.intent_names[intent_id]} ({self.intent_classes[intent_id]})"
        
        # 文字背景
        text_size = cv2.getTextSize(intent_text, cv2.FONT_HERSHEY_SIMPLEX, 1.2, 3)[0]
        cv2.rectangle(vis_image, (10, 10), (text_size[0] + 20, text_size[1] + 30), intent_color, -1)
        cv2.rectangle(vis_image, (10, 10), (text_size[0] + 20, text_size[1] + 30), (255, 255, 255), 3)
        
        # 文字
        cv2.putText(vis_image, intent_text, (20, text_size[1] + 20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 3)
        
        return vis_image
    
    def interactive_annotation(self, image_path, output_dir="intent_annotations"):
        """
        交互式标注 - 用户选择意图标签
        """
        print(f"\n正在标注图片: {os.path.basename(image_path)}")
        print("请选择意图标签:")
        for i, (class_id, class_name) in enumerate(self.intent_classes.items()):
            print(f"  {class_id}: {class_name} ({self.intent_names[class_id]})")
        
        while True:
            try:
                choice = input("请输入意图标签 (0-4): ").strip()
                intent_id = int(choice)
                if intent_id in self.intent_classes:
                    break
                else:
                    print("请输入 0-4 之间的数字")
            except ValueError:
                print("请输入有效的数字")
        
        result = self.annotate_with_intent(image_path, intent_id, output_dir)
        
        if result:
            print(f"✓ 标注完成! 意图: {self.intent_names[intent_id]}")
            return result
        else:
            print("✗ 标注失败!")
            return None

def main():
    """
    主函数
    """
    annotator = PoseIntentAnnotator()
    
    print("人体姿态 + 意图标注工具")
    print("支持的意图类别:")
    for class_id, class_name in annotator.intent_classes.items():
        print(f"  {class_id}: {class_name} ({annotator.intent_names[class_id]})")
    
    print("\n操作选项:")
    print("1. 单张图片交互式标注")
    print("2. 单张图片指定标签标注")
    print("3. 批量图片标注")
    
    choice = input("请选择操作 (1/2/3): ").strip()
    
    if choice == "1":
        image_path = input("请输入图片路径: ").strip()
        annotator.interactive_annotation(image_path)
    
    elif choice == "2":
        image_path = input("请输入图片路径: ").strip()
        print("意图标签: 0=直行, 1=左转, 2=右转, 3=转身, 4=停止")
        intent_label = int(input("请输入意图标签: ").strip())
        output_dir = input("输出目录 (默认: intent_annotations): ").strip() or "intent_annotations"
        
        result = annotator.annotate_with_intent(image_path, intent_label, output_dir)
        if result:
            print("✓ 标注完成!")
    
    elif choice == "3":
        print("批量标注功能待实现...")
    
    else:
        print("无效选择!")

if __name__ == '__main__':
    main()

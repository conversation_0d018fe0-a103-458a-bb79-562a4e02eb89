"""
基于实际数据的背部视角识别
根据调试结果重新设计逻辑
"""

import cv2
import numpy as np
import os
from collections import deque
from ultralytics import YOLO

class DataDrivenBackViewRecognizer:
    def __init__(self):
        # 意图类别
        self.intent_classes = ['STRAIGHT', 'LEFT', 'RIGHT', 'TURN_AROUND', 'STOP']
        self.intent_colors = [
            (0, 255, 0),    # STRAIGHT - 绿色
            (255, 0, 0),    # LEFT - 蓝色
            (0, 0, 255),    # RIGHT - 红色
            (255, 0, 255),  # TURN_AROUND - 紫色
            (0, 255, 255)   # STOP - 黄色
        ]
        
        # 基于实际调试数据的阈值
        self.movement_threshold = 0.000005
        
        # 根据调试结果调整的阈值
        # 前半段: X移动很小(-0.0001), X稳定性差(0.0057)
        # 后半段: X移动明显(0.0006), 累积X移动大(0.1056)
        
        self.straight_x_movement_threshold = 0.0003    # X方向移动阈值
        self.straight_x_stability_threshold = 0.008    # X方向稳定性阈值
        self.turn_x_movement_threshold = 0.0004        # 转弯X移动阈值
        self.turn_cumulative_threshold = 0.02          # 累积转弯阈值
        
        # 时序数据缓冲区
        self.body_center_sequence = deque(maxlen=20)
        
        # 视频信息
        self.total_frames = 0
        
        # 加载姿态检测模型
        print("加载YOLOv8姿态检测模型...")
        self.model = YOLO('yolov8n-pose.pt')
        print("✓ 模型加载成功")
        print("基于实际数据的逻辑:")
        print(f"  直行X移动阈值: ±{self.straight_x_movement_threshold:.6f}")
        print(f"  转弯X移动阈值: ±{self.turn_x_movement_threshold:.6f}")
        print(f"  累积转弯阈值: ±{self.turn_cumulative_threshold:.6f}")
    
    def set_video_info(self, total_frames):
        """设置视频总帧数"""
        self.total_frames = total_frames
    
    def extract_features(self, keypoints, confidence):
        """提取特征"""
        features = {}
        
        if all(confidence[i] > 0.4 for i in [5, 6, 11, 12]):
            shoulder_center = (keypoints[5] + keypoints[6]) / 2
            hip_center = (keypoints[11] + keypoints[12]) / 2
            body_center = (shoulder_center + hip_center) / 2
            
            features['body_center'] = body_center
        
        return features
    
    def analyze_movement_pattern_data_driven(self):
        """基于实际数据的移动模式分析"""
        if len(self.body_center_sequence) < 12:
            return None, {}
        
        centers = np.array(list(self.body_center_sequence))
        
        # 计算移动向量
        movements = []
        for i in range(1, len(centers)):
            movement = centers[i] - centers[i-1]
            movements.append(movement)
        
        if not movements:
            return None, {}
        
        movements = np.array(movements)
        
        # 关键分析指标（基于调试数据）
        # 1. 最近的移动模式（更重要）
        recent_movements = movements[-10:] if len(movements) >= 10 else movements
        
        # 2. X方向分析
        recent_x_movements = recent_movements[:, 0]
        avg_x_movement = np.mean(recent_x_movements)
        x_movement_std = np.std(recent_x_movements)
        
        # 3. 累积X移动
        cumulative_x = np.sum(movements[:, 0])
        
        # 4. 总体移动距离
        total_movement = np.sum([np.linalg.norm(m) for m in movements])
        
        analysis = {
            'avg_x_movement': avg_x_movement,
            'x_movement_std': x_movement_std,
            'cumulative_x': cumulative_x,
            'total_movement': total_movement,
            'num_movements': len(movements)
        }
        
        # 判断逻辑（基于实际数据模式）
        intent = None
        confidence = 0.0
        reason = ""
        
        # 1. 检查是否在移动
        if total_movement < self.movement_threshold * len(movements):
            intent = 'STOP'
            confidence = 0.8
            reason = "insufficient_movement"
        
        # 2. 基于实际数据的模式识别
        # 调试显示：前半段X移动小且相对稳定，后半段X移动大且向正方向
        
        # 检查是否为稳定直行（类似前半段模式）
        elif (abs(avg_x_movement) < self.straight_x_movement_threshold and
              x_movement_std < self.straight_x_stability_threshold):
            intent = 'STRAIGHT'
            confidence = min(0.9, (1 - abs(avg_x_movement) / self.straight_x_movement_threshold) * 0.5 +
                           (1 - x_movement_std / self.straight_x_stability_threshold) * 0.5)
            reason = f"stable_movement (avg_x={avg_x_movement:.6f}, std={x_movement_std:.6f})"
        
        # 检查右转（类似后半段模式：X正方向移动）
        elif (avg_x_movement > self.turn_x_movement_threshold or 
              cumulative_x > self.turn_cumulative_threshold):
            intent = 'RIGHT'
            confidence = min(0.9, abs(avg_x_movement) / 0.002 + abs(cumulative_x) / 0.05)
            reason = f"right_turn (avg_x={avg_x_movement:.6f}, cum_x={cumulative_x:.6f})"
        
        # 检查左转（X负方向移动）
        elif (avg_x_movement < -self.turn_x_movement_threshold or 
              cumulative_x < -self.turn_cumulative_threshold):
            intent = 'LEFT'
            confidence = min(0.9, abs(avg_x_movement) / 0.002 + abs(cumulative_x) / 0.05)
            reason = f"left_turn (avg_x={avg_x_movement:.6f}, cum_x={cumulative_x:.6f})"
        
        # 默认情况
        else:
            intent = 'STRAIGHT'
            confidence = 0.6
            reason = f"default_straight (avg_x={avg_x_movement:.6f})"
        
        analysis['intent'] = intent
        analysis['confidence'] = confidence
        analysis['reason'] = reason
        
        return intent, analysis
    
    def classify_intent_data_driven(self, frame_num):
        """基于数据驱动的意图分类"""
        
        # 分析移动模式
        intent, analysis = self.analyze_movement_pattern_data_driven()
        
        if intent is None:
            return None, 0.0, {'reason': 'insufficient_data'}
        
        # 转换为数字ID
        intent_id = None
        if intent == 'STRAIGHT':
            intent_id = 0
        elif intent == 'LEFT':
            intent_id = 1
        elif intent == 'RIGHT':
            intent_id = 2
        elif intent == 'STOP':
            intent_id = 4
        
        return intent_id, analysis['confidence'], analysis
    
    def update_and_classify(self, keypoints, confidence, frame_num):
        """更新数据并分类"""
        
        # 提取特征
        features = self.extract_features(keypoints, confidence)
        
        # 更新历史数据
        if 'body_center' in features:
            self.body_center_sequence.append(features['body_center'])
        
        # 进行分类
        if len(self.body_center_sequence) >= 12:
            
            intent, confidence, debug_info = self.classify_intent_data_driven(frame_num)
            
            # 添加进度信息
            progress = frame_num / self.total_frames if self.total_frames > 0 else 0
            debug_info['progress'] = progress
            debug_info['frame'] = frame_num
            
            return intent, confidence, debug_info
        
        return None, 0.0, {'reason': 'insufficient_data', 'frame': frame_num}

def analyze_video_data_driven_back_view(video_path='left.mp4', output_path='left_data_driven_back_view_analysis.mp4'):
    """
    使用数据驱动的背部视角方法分析视频
    """
    print(f"开始数据驱动背部视角识别: {video_path}")
    print("基于实际调试数据设计的逻辑")
    
    if not os.path.exists(video_path):
        print(f"错误: 视频文件不存在")
        return False
    
    # 创建识别器
    recognizer = DataDrivenBackViewRecognizer()
    
    # 打开视频
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("错误: 无法打开视频")
        return False
    
    # 视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    recognizer.set_video_info(total_frames)
    
    print(f"视频: {width}x{height}, {fps}FPS, {total_frames}帧")
    print(f"根据调试数据，预期模式:")
    print(f"  前半段: 相对稳定的移动（可能是直行）")
    print(f"  后半段: X正方向移动增强（可能是右转）")
    
    # 创建输出视频
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    frame_count = 0
    detection_count = 0
    analysis_count = 0
    intent_stats = {name: 0 for name in recognizer.intent_classes}
    intent_stats['ANALYZING'] = 0
    
    # 分段统计
    segment_stats = {'first_half': {}, 'second_half': {}}
    
    print("开始处理...")
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 姿态检测
            results = recognizer.model(frame, conf=0.3, verbose=False)
            
            annotated_frame = frame.copy()
            current_intent = None
            intent_confidence = 0.0
            debug_info = {}
            
            # 处理检测结果
            if len(results) > 0 and results[0].keypoints is not None:
                keypoints_data = results[0].keypoints.data
                
                if len(keypoints_data) > 0:
                    detection_count += 1
                    keypoints = keypoints_data[0]
                    
                    if keypoints.shape[0] == 17:
                        xy = keypoints[:, :2].cpu().numpy()
                        conf = keypoints[:, 2].cpu().numpy()
                        
                        # 归一化关键点
                        normalized_xy = xy.copy()
                        normalized_xy[:, 0] /= width
                        normalized_xy[:, 1] /= height
                        
                        # 数据驱动分析
                        intent_id, confidence, debug_info = recognizer.update_and_classify(
                            normalized_xy, conf, frame_count
                        )
                        
                        if intent_id is not None:
                            current_intent = intent_id
                            intent_confidence = confidence
                            analysis_count += 1
                            
                            # 分段统计
                            segment = 'first_half' if frame_count <= total_frames // 2 else 'second_half'
                            intent_name = recognizer.intent_classes[intent_id]
                            segment_stats[segment][intent_name] = segment_stats[segment].get(intent_name, 0) + 1
                        
                        # 绘制关键点
                        for i, (x, y) in enumerate(xy):
                            if conf[i] > 0.3:
                                if i in [5, 6, 11, 12]:  # 肩膀和髋部
                                    color = (0, 255, 255)  # 黄色
                                    radius = 8
                                else:
                                    color = (0, 255, 0) if conf[i] > 0.7 else (0, 255, 255)
                                    radius = 6
                                cv2.circle(annotated_frame, (int(x), int(y)), radius, color, -1)
                        
                        # 绘制骨架
                        connections = [
                            (5, 6), (5, 7), (7, 9), (6, 8), (8, 10),
                            (5, 11), (6, 12), (11, 12),
                            (11, 13), (13, 15), (12, 14), (14, 16)
                        ]
                        
                        for start_idx, end_idx in connections:
                            if conf[start_idx] > 0.3 and conf[end_idx] > 0.3:
                                start_point = (int(xy[start_idx][0]), int(xy[start_idx][1]))
                                end_point = (int(xy[end_idx][0]), int(xy[end_idx][1]))
                                cv2.line(annotated_frame, start_point, end_point, (255, 0, 0), 3)
                        
                        # 绘制移动轨迹
                        if len(recognizer.body_center_sequence) >= 2:
                            trajectory_points = []
                            for center in recognizer.body_center_sequence:
                                point = center * np.array([width, height])
                                trajectory_points.append((int(point[0]), int(point[1])))
                            
                            # 绘制轨迹线
                            for i in range(1, len(trajectory_points)):
                                cv2.line(annotated_frame, trajectory_points[i-1], trajectory_points[i], (255, 255, 0), 6)
                            
                            # 标记起点和当前点
                            if len(trajectory_points) >= 2:
                                cv2.circle(annotated_frame, trajectory_points[0], 12, (0, 255, 0), -1)  # 起点绿色
                                cv2.circle(annotated_frame, trajectory_points[-1], 12, (0, 0, 255), -1)  # 当前点红色
            
            # 更新统计
            if current_intent is not None:
                intent_stats[recognizer.intent_classes[current_intent]] += 1
            else:
                intent_stats['ANALYZING'] += 1
            
            # 绘制信息面板
            panel_width = 950
            panel_height = 400
            
            # 半透明背景
            overlay = annotated_frame.copy()
            cv2.rectangle(overlay, (10, 10), (panel_width, panel_height), (0, 0, 0), -1)
            cv2.addWeighted(overlay, 0.85, annotated_frame, 0.15, 0, annotated_frame)
            
            # 白色边框
            cv2.rectangle(annotated_frame, (10, 10), (panel_width, panel_height), (255, 255, 255), 3)
            
            # 显示信息
            y = 40
            line_height = 25
            
            # 标题
            cv2.putText(annotated_frame, "Data-Driven Back-View Recognition", 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (255, 255, 255), 2)
            y += line_height + 5
            
            # 进度信息
            progress = frame_count / total_frames * 100
            half = "前半段" if frame_count <= total_frames // 2 else "后半段"
            cv2.putText(annotated_frame, f'进度: {progress:.1f}% ({half})', 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            y += line_height
            
            # 进度条
            bar_width = 500
            bar_height = 15
            cv2.rectangle(annotated_frame, (25, y), (25 + bar_width, y + bar_height), (100, 100, 100), -1)
            cv2.rectangle(annotated_frame, (25, y), (25 + int(bar_width * progress / 100), y + bar_height), (0, 255, 0), -1)
            
            # 中点标记
            mid_x = 25 + bar_width // 2
            cv2.line(annotated_frame, (mid_x, y), (mid_x, y + bar_height), (255, 255, 255), 3)
            
            y += line_height + 10
            
            # 意图显示
            if current_intent is not None:
                intent_text = f'检测意图: {recognizer.intent_classes[current_intent]}'
                confidence_text = f'置信度: {intent_confidence:.2f}'
                intent_color = recognizer.intent_colors[current_intent]
                
                cv2.putText(annotated_frame, intent_text, 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.2, intent_color, 3)
                y += line_height + 5
                
                cv2.putText(annotated_frame, confidence_text, 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, intent_color, 2)
                y += line_height
                
                # 显示分析详情
                if debug_info:
                    reason = debug_info.get('reason', 'unknown')
                    cv2.putText(annotated_frame, f"分析: {reason}", 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (200, 200, 200), 2)
                    y += line_height
                    
                    # 显示关键参数
                    if 'avg_x_movement' in debug_info:
                        cv2.putText(annotated_frame, f"平均X移动: {debug_info['avg_x_movement']:.6f}", 
                                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
                        y += 18
                    
                    if 'cumulative_x' in debug_info:
                        cv2.putText(annotated_frame, f"累积X移动: {debug_info['cumulative_x']:.6f}", 
                                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
                
                # 右上角大字显示
                intent_display = recognizer.intent_classes[current_intent]
                text_size = cv2.getTextSize(intent_display, cv2.FONT_HERSHEY_SIMPLEX, 2.5, 5)[0]
                
                cv2.rectangle(annotated_frame, 
                             (width - text_size[0] - 40, 20), 
                             (width - 20, 100), 
                             intent_color, -1)
                cv2.rectangle(annotated_frame, 
                             (width - text_size[0] - 40, 20), 
                             (width - 20, 100), 
                             (255, 255, 255), 3)
                cv2.putText(annotated_frame, intent_display, 
                           (width - text_size[0] - 30, 70), 
                           cv2.FONT_HERSHEY_SIMPLEX, 2.5, (255, 255, 255), 5)
            else:
                cv2.putText(annotated_frame, 'Status: 构建移动数据...', 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (128, 128, 128), 3)
            
            # 写入视频
            writer.write(annotated_frame)
            
            # 显示进度
            if frame_count % 100 == 0:
                progress_pct = frame_count / total_frames * 100
                current_intent_name = recognizer.intent_classes[current_intent] if current_intent is not None else 'ANALYZING'
                print(f"进度: {progress_pct:.1f}% - {half} - 检测: {current_intent_name}")
    
    except Exception as e:
        print(f"处理错误: {e}")
        return False
    
    finally:
        cap.release()
        writer.release()
    
    # 分段分析
    print(f"\n=== 数据驱动的分段分析 ===")
    
    for segment_name, stats in segment_stats.items():
        if stats:
            total_frames_in_segment = sum(stats.values())
            main_intent = max(stats.keys(), key=lambda x: stats[x])
            main_intent_ratio = stats[main_intent] / total_frames_in_segment
            
            segment_display = "前半段" if segment_name == 'first_half' else "后半段"
            print(f"{segment_display}: 主要意图 = {main_intent} ({main_intent_ratio:.1%})")
            
            for intent, count in stats.items():
                ratio = count / total_frames_in_segment
                print(f"  {intent}: {count}帧 ({ratio:.1%})")
    
    print(f"\n=== 数据驱动背部视角识别完成 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测帧数: {detection_count}")
    print(f"分析帧数: {analysis_count}")
    print(f"检测率: {detection_count/frame_count*100:.1f}%")
    print(f"分析率: {analysis_count/frame_count*100:.1f}%")
    print(f"\n总体意图分布:")
    for intent, count in intent_stats.items():
        percentage = count / frame_count * 100 if frame_count > 0 else 0
        print(f"  {intent}: {count} 帧 ({percentage:.1f}%)")
    print(f"输出视频: {output_path}")
    
    return True

if __name__ == '__main__':
    success = analyze_video_data_driven_back_view()
    if success:
        print("\n🎉 数据驱动背部视角识别完成！")
        print("特点:")
        print("- ✅ 基于实际调试数据设计")
        print("- ✅ 针对真实移动模式优化")
        print("- ✅ 数据驱动的阈值设置")
        print("- ✅ 符合实际观察的分类逻辑")
    else:
        print("\n❌ 数据驱动背部视角识别失败！")

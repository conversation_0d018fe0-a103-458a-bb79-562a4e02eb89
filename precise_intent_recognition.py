"""
精准的行走意图识别
- 直走：基于腿部关节交替运动模式
- 转弯：基于身体整体角度变化
- 分段分析：识别意图变化
"""

import cv2
import numpy as np
import os
from collections import deque
from ultralytics import YOLO
import math

class PreciseIntentRecognizer:
    def __init__(self, sequence_length=8):
        self.sequence_length = sequence_length
        
        # 意图类别
        self.intent_classes = ['STRAIGHT', 'LEFT', 'RIGHT', 'TURN_AROUND', 'STOP']
        self.intent_colors = [
            (0, 255, 0),    # STRAIGHT - 绿色
            (255, 0, 0),    # LEFT - 蓝色
            (0, 0, 255),    # RIGHT - 红色
            (255, 0, 255),  # TURN_AROUND - 紫色
            (0, 255, 255)   # STOP - 黄色
        ]
        
        # 时序数据缓冲区
        self.body_center_sequence = deque(maxlen=sequence_length + 5)
        self.body_angle_sequence = deque(maxlen=sequence_length + 5)
        self.leg_phase_sequence = deque(maxlen=sequence_length + 5)
        self.step_frequency_sequence = deque(maxlen=sequence_length)
        
        # 腿部运动分析
        self.left_knee_height_sequence = deque(maxlen=sequence_length + 5)
        self.right_knee_height_sequence = deque(maxlen=sequence_length + 5)
        self.left_ankle_height_sequence = deque(maxlen=sequence_length + 5)
        self.right_ankle_height_sequence = deque(maxlen=sequence_length + 5)
        
        # 关键点索引
        self.keypoints_idx = {
            'left_shoulder': 5, 'right_shoulder': 6,
            'left_hip': 11, 'right_hip': 12,
            'left_knee': 13, 'right_knee': 14,
            'left_ankle': 15, 'right_ankle': 16
        }
        
        # 加载姿态检测模型
        print("加载YOLOv8姿态检测模型...")
        self.model = YOLO('yolov8n-pose.pt')
        print("✓ 模型加载成功")
    
    def analyze_leg_walking_pattern(self, keypoints, confidence):
        """分析腿部行走模式"""
        leg_features = {}
        
        # 检查腿部关键点可见性
        required_points = [11, 12, 13, 14, 15, 16]  # 髋部、膝盖、脚踝
        if not all(confidence[i] > 0.4 for i in required_points):
            return leg_features
        
        # 1. 膝盖高度分析（检测抬腿动作）
        left_knee_y = keypoints[13][1]
        right_knee_y = keypoints[14][1]
        left_ankle_y = keypoints[15][1]
        right_ankle_y = keypoints[16][1]
        
        # 膝盖相对于脚踝的高度差（抬腿程度）
        left_knee_lift = left_ankle_y - left_knee_y  # 正值表示膝盖高于脚踝
        right_knee_lift = right_ankle_y - right_knee_y
        
        leg_features['left_knee_lift'] = left_knee_lift
        leg_features['right_knee_lift'] = right_knee_lift
        leg_features['knee_lift_diff'] = abs(left_knee_lift - right_knee_lift)
        
        # 2. 步态相位分析
        # 当一条腿抬起时，另一条腿应该着地
        if left_knee_lift > right_knee_lift + 10:  # 左腿抬起
            leg_features['gait_phase'] = 'left_swing'
        elif right_knee_lift > left_knee_lift + 10:  # 右腿抬起
            leg_features['gait_phase'] = 'right_swing'
        else:
            leg_features['gait_phase'] = 'double_support'  # 双腿支撑
        
        # 3. 步幅分析
        step_width = np.linalg.norm(keypoints[16] - keypoints[15])  # 双脚距离
        leg_features['step_width'] = step_width
        
        # 4. 腿部对称性
        left_hip = keypoints[11]
        right_hip = keypoints[12]
        left_knee = keypoints[13]
        right_knee = keypoints[14]
        
        # 计算大腿角度
        left_thigh_vector = left_knee - left_hip
        right_thigh_vector = right_knee - right_hip
        
        left_thigh_angle = np.degrees(np.arctan2(left_thigh_vector[1], left_thigh_vector[0]))
        right_thigh_angle = np.degrees(np.arctan2(right_thigh_vector[1], right_thigh_vector[0]))
        
        leg_features['left_thigh_angle'] = left_thigh_angle
        leg_features['right_thigh_angle'] = right_thigh_angle
        leg_features['thigh_angle_diff'] = abs(left_thigh_angle - right_thigh_angle)
        
        return leg_features
    
    def analyze_body_orientation_change(self, keypoints, confidence):
        """分析身体整体朝向变化"""
        body_features = {}
        
        # 检查上身关键点可见性
        if not all(confidence[i] > 0.4 for i in [5, 6, 11, 12]):
            return body_features
        
        # 1. 身体中心线角度
        shoulder_center = (keypoints[5] + keypoints[6]) / 2
        hip_center = (keypoints[11] + keypoints[12]) / 2
        body_center = (shoulder_center + hip_center) / 2
        
        # 身体朝向向量（从髋部指向肩膀）
        body_vector = shoulder_center - hip_center
        body_angle = np.degrees(np.arctan2(body_vector[1], body_vector[0]))
        
        body_features['body_center'] = body_center
        body_features['body_angle'] = body_angle
        
        # 2. 肩膀线角度
        shoulder_vector = keypoints[6] - keypoints[5]  # 右肩 - 左肩
        shoulder_angle = np.degrees(np.arctan2(shoulder_vector[1], shoulder_vector[0]))
        body_features['shoulder_angle'] = shoulder_angle
        
        # 3. 髋部线角度
        hip_vector = keypoints[12] - keypoints[11]  # 右髋 - 左髋
        hip_angle = np.degrees(np.arctan2(hip_vector[1], hip_vector[0]))
        body_features['hip_angle'] = hip_angle
        
        # 4. 身体扭转程度
        angle_diff = abs(shoulder_angle - hip_angle)
        if angle_diff > 180:
            angle_diff = 360 - angle_diff
        body_features['body_twist'] = angle_diff
        
        return body_features
    
    def detect_walking_pattern(self):
        """检测行走模式"""
        if len(self.leg_phase_sequence) < 6:
            return None, 0.0
        
        # 分析最近的步态相位变化
        recent_phases = list(self.leg_phase_sequence)[-6:]
        
        # 统计步态相位
        phase_changes = 0
        left_swing_count = 0
        right_swing_count = 0
        
        for i in range(1, len(recent_phases)):
            if recent_phases[i] != recent_phases[i-1]:
                phase_changes += 1
            
            if recent_phases[i] == 'left_swing':
                left_swing_count += 1
            elif recent_phases[i] == 'right_swing':
                right_swing_count += 1
        
        # 判断是否在行走 (降低阈值)
        is_walking = phase_changes >= 1 or (left_swing_count > 0 and right_swing_count > 0)
        
        # 分析膝盖抬起的交替模式
        if len(self.left_knee_height_sequence) >= 6 and len(self.right_knee_height_sequence) >= 6:
            left_lifts = list(self.left_knee_height_sequence)[-6:]
            right_lifts = list(self.right_knee_height_sequence)[-6:]
            
            # 检测交替抬腿模式
            alternating_pattern = 0
            for i in range(len(left_lifts)):
                if left_lifts[i] > 15 and right_lifts[i] < 10:  # 左腿抬起，右腿着地
                    alternating_pattern += 1
                elif right_lifts[i] > 15 and left_lifts[i] < 10:  # 右腿抬起，左腿着地
                    alternating_pattern += 1
            
            # 如果有明显的交替模式，认为在行走 (降低阈值)
            if alternating_pattern >= 1:
                is_walking = True
        
        walking_confidence = min(0.9, (phase_changes + alternating_pattern) / 8.0)
        
        return is_walking, walking_confidence
    
    def detect_turning_direction(self):
        """检测转向方向"""
        if len(self.body_angle_sequence) < self.sequence_length:
            return None, 0.0
        
        # 分析身体角度的变化趋势
        angles = list(self.body_angle_sequence)
        
        # 计算角度变化的累积趋势
        angle_changes = []
        for i in range(1, len(angles)):
            change = angles[i] - angles[i-1]
            
            # 处理角度跨越问题
            if change > 180:
                change -= 360
            elif change < -180:
                change += 360
            
            angle_changes.append(change)
        
        # 计算总体趋势
        if len(angle_changes) >= 4:
            # 使用滑动窗口分析趋势
            recent_changes = angle_changes[-6:] if len(angle_changes) >= 6 else angle_changes
            total_change = sum(recent_changes)
            avg_change = total_change / len(recent_changes)
            
            # 分析身体中心的移动轨迹
            trajectory_turn = 0
            if len(self.body_center_sequence) >= self.sequence_length:
                centers = np.array(list(self.body_center_sequence))
                
                # 计算轨迹的弯曲方向
                if len(centers) >= 6:
                    start_segment = centers[-6:-3]
                    end_segment = centers[-3:]
                    
                    if len(start_segment) >= 2 and len(end_segment) >= 2:
                        start_direction = start_segment[-1] - start_segment[0]
                        end_direction = end_segment[-1] - end_segment[0]
                        
                        # 计算方向变化（叉积）
                        cross_product = np.cross(start_direction, end_direction)
                        trajectory_turn = cross_product
            
            # 综合判断转向 (大幅降低阈值)
            angle_threshold = 0.08  # 从3.0降低到0.08
            trajectory_threshold = 0.0001  # 从0.0005降低到0.0001
            
            # 左转判断 (降低阈值)
            if avg_change < -angle_threshold or total_change < -angle_threshold * 3:
                confidence = min(0.9, abs(avg_change) / 1.0 + abs(trajectory_turn) * 5000)
                return 'LEFT', confidence

            # 右转判断 (降低阈值)
            elif avg_change > angle_threshold or total_change > angle_threshold * 3:
                confidence = min(0.9, abs(avg_change) / 1.0 + abs(trajectory_turn) * 5000)
                return 'RIGHT', confidence
        
        return None, 0.0
    
    def classify_intent_precise(self):
        """精准的意图分类"""
        
        # 1. 首先检测是否在行走
        is_walking, walking_confidence = self.detect_walking_pattern()
        
        if not is_walking or walking_confidence < 0.3:
            return 4, 0.8  # STOP
        
        # 2. 如果在行走，检测转向
        turn_direction, turn_confidence = self.detect_turning_direction()
        
        if turn_direction and turn_confidence > 0.4:
            if turn_direction == 'LEFT':
                return 1, turn_confidence  # LEFT
            elif turn_direction == 'RIGHT':
                return 2, turn_confidence  # RIGHT
        
        # 3. 如果在行走但没有明显转向，判断为直走
        if is_walking and walking_confidence > 0.5:
            return 0, walking_confidence  # STRAIGHT
        
        return None, 0.0
    
    def update_and_analyze(self, keypoints, confidence):
        """更新数据并进行分析"""
        
        # 分析腿部运动
        leg_features = self.analyze_leg_walking_pattern(keypoints, confidence)
        
        # 分析身体朝向
        body_features = self.analyze_body_orientation_change(keypoints, confidence)
        
        # 更新时序数据
        if 'gait_phase' in leg_features:
            self.leg_phase_sequence.append(leg_features['gait_phase'])
        
        if 'left_knee_lift' in leg_features:
            self.left_knee_height_sequence.append(leg_features['left_knee_lift'])
        
        if 'right_knee_lift' in leg_features:
            self.right_knee_height_sequence.append(leg_features['right_knee_lift'])
        
        if 'body_center' in body_features:
            self.body_center_sequence.append(body_features['body_center'])
        
        if 'body_angle' in body_features:
            self.body_angle_sequence.append(body_features['body_angle'])
        
        # 进行意图分类
        if (len(self.leg_phase_sequence) >= 6 and 
            len(self.body_angle_sequence) >= self.sequence_length):
            
            intent, confidence = self.classify_intent_precise()
            
            # 返回调试信息
            debug_info = {
                'leg_features': leg_features,
                'body_features': body_features,
                'walking_detected': self.detect_walking_pattern()[0],
                'walking_confidence': self.detect_walking_pattern()[1],
                'turn_direction': self.detect_turning_direction()[0],
                'turn_confidence': self.detect_turning_direction()[1]
            }
            
            return intent, confidence, debug_info
        
        return None, 0.0, {}

def analyze_video_precise(video_path='left.mp4', output_path='left_precise_analysis.mp4'):
    """
    使用精准方法分析视频
    """
    print(f"开始精准意图识别: {video_path}")
    print("特点:")
    print("- 直走：基于腿部交替运动模式")
    print("- 转弯：基于身体整体角度变化")
    print("- 分段分析：识别意图变化")
    
    if not os.path.exists(video_path):
        print(f"错误: 视频文件不存在")
        return False
    
    # 创建精准识别器
    recognizer = PreciseIntentRecognizer(sequence_length=8)
    
    # 打开视频
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("错误: 无法打开视频")
        return False
    
    # 视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"视频: {width}x{height}, {fps}FPS, {total_frames}帧")
    
    # 创建输出视频
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    frame_count = 0
    detection_count = 0
    analysis_count = 0
    intent_stats = {name: 0 for name in recognizer.intent_classes}
    intent_stats['ANALYZING'] = 0
    
    # 用于分段分析
    intent_timeline = []
    
    print("开始处理...")
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 姿态检测
            results = recognizer.model(frame, conf=0.3, verbose=False)
            
            annotated_frame = frame.copy()
            current_intent = None
            intent_confidence = 0.0
            debug_info = {}
            
            # 处理检测结果
            if len(results) > 0 and results[0].keypoints is not None:
                keypoints_data = results[0].keypoints.data
                
                if len(keypoints_data) > 0:
                    detection_count += 1
                    keypoints = keypoints_data[0]
                    
                    if keypoints.shape[0] == 17:
                        xy = keypoints[:, :2].cpu().numpy()
                        conf = keypoints[:, 2].cpu().numpy()
                        
                        # 归一化关键点
                        normalized_xy = xy.copy()
                        normalized_xy[:, 0] /= width
                        normalized_xy[:, 1] /= height
                        
                        # 精准分析
                        intent_id, confidence, debug_info = recognizer.update_and_analyze(
                            normalized_xy, conf
                        )
                        
                        if intent_id is not None:
                            current_intent = intent_id
                            intent_confidence = confidence
                            analysis_count += 1
                            
                            # 记录时间线
                            intent_timeline.append({
                                'frame': frame_count,
                                'time': frame_count / fps,
                                'intent': recognizer.intent_classes[intent_id],
                                'confidence': confidence
                            })
                        
                        # 绘制关键点（突出腿部）
                        for i, (x, y) in enumerate(xy):
                            if conf[i] > 0.3:
                                # 腿部关键点用特殊颜色
                                if i in [11, 12, 13, 14, 15, 16]:  # 髋部、膝盖、脚踝
                                    color = (0, 255, 255)  # 黄色
                                    radius = 8
                                else:
                                    color = (0, 255, 0) if conf[i] > 0.7 else (0, 255, 255)
                                    radius = 6
                                cv2.circle(annotated_frame, (int(x), int(y)), radius, color, -1)
                        
                        # 绘制骨架（突出腿部连接）
                        connections = [
                            (5, 6), (5, 7), (7, 9), (6, 8), (8, 10),
                            (5, 11), (6, 12), (11, 12),
                            (11, 13), (13, 15), (12, 14), (14, 16)  # 腿部连接
                        ]
                        
                        for start_idx, end_idx in connections:
                            if conf[start_idx] > 0.3 and conf[end_idx] > 0.3:
                                start_point = (int(xy[start_idx][0]), int(xy[start_idx][1]))
                                end_point = (int(xy[end_idx][0]), int(xy[end_idx][1]))
                                
                                # 腿部连接用粗线
                                if start_idx in [11, 12, 13, 14, 15, 16] and end_idx in [11, 12, 13, 14, 15, 16]:
                                    cv2.line(annotated_frame, start_point, end_point, (0, 255, 255), 5)
                                else:
                                    cv2.line(annotated_frame, start_point, end_point, (255, 0, 0), 3)
                        
                        # 绘制身体中心轨迹
                        if len(recognizer.body_center_sequence) >= 2:
                            for i in range(1, len(recognizer.body_center_sequence)):
                                prev_center = recognizer.body_center_sequence[i-1] * np.array([width, height])
                                curr_center = recognizer.body_center_sequence[i] * np.array([width, height])
                                cv2.line(annotated_frame,
                                        (int(prev_center[0]), int(prev_center[1])),
                                        (int(curr_center[0]), int(curr_center[1])),
                                        (255, 255, 0), 4)
            
            # 更新统计
            if current_intent is not None:
                intent_stats[recognizer.intent_classes[current_intent]] += 1
            else:
                intent_stats['ANALYZING'] += 1
            
            # 绘制信息面板
            panel_width = 800
            panel_height = 350
            
            # 半透明背景
            overlay = annotated_frame.copy()
            cv2.rectangle(overlay, (10, 10), (panel_width, panel_height), (0, 0, 0), -1)
            cv2.addWeighted(overlay, 0.85, annotated_frame, 0.15, 0, annotated_frame)
            
            # 白色边框
            cv2.rectangle(annotated_frame, (10, 10), (panel_width, panel_height), (255, 255, 255), 3)
            
            # 显示信息
            y = 40
            line_height = 25
            
            # 标题
            cv2.putText(annotated_frame, "Precise Intent Recognition", 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (255, 255, 255), 2)
            y += line_height + 5
            
            # 帧信息
            cv2.putText(annotated_frame, f'Frame: {frame_count}/{total_frames} ({frame_count/fps:.1f}s)', 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            y += line_height
            
            # 意图显示
            if current_intent is not None:
                intent_text = f'Intent: {recognizer.intent_classes[current_intent]}'
                confidence_text = f'Confidence: {intent_confidence:.2f}'
                intent_color = recognizer.intent_colors[current_intent]
                
                cv2.putText(annotated_frame, intent_text, 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.2, intent_color, 3)
                y += line_height + 5
                
                cv2.putText(annotated_frame, confidence_text, 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, intent_color, 2)
                y += line_height
                
                # 显示分析特征
                if debug_info:
                    if debug_info.get('walking_detected'):
                        walking_conf = debug_info.get('walking_confidence', 0)
                        cv2.putText(annotated_frame, f"Walking: YES ({walking_conf:.2f})", 
                                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                    else:
                        cv2.putText(annotated_frame, "Walking: NO", 
                                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
                    y += line_height
                    
                    turn_dir = debug_info.get('turn_direction')
                    turn_conf = debug_info.get('turn_confidence', 0)
                    if turn_dir:
                        cv2.putText(annotated_frame, f"Turn: {turn_dir} ({turn_conf:.2f})", 
                                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
                    else:
                        cv2.putText(annotated_frame, "Turn: NONE", 
                                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (128, 128, 128), 2)
                    y += line_height
                    
                    # 显示腿部特征
                    leg_features = debug_info.get('leg_features', {})
                    if 'gait_phase' in leg_features:
                        cv2.putText(annotated_frame, f"Gait: {leg_features['gait_phase']}", 
                                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
                        y += 18
                
                # 右上角大字显示
                intent_display = recognizer.intent_classes[current_intent]
                text_size = cv2.getTextSize(intent_display, cv2.FONT_HERSHEY_SIMPLEX, 2.5, 5)[0]
                
                cv2.rectangle(annotated_frame, 
                             (width - text_size[0] - 40, 20), 
                             (width - 20, 100), 
                             intent_color, -1)
                cv2.rectangle(annotated_frame, 
                             (width - text_size[0] - 40, 20), 
                             (width - 20, 100), 
                             (255, 255, 255), 3)
                cv2.putText(annotated_frame, intent_display, 
                           (width - text_size[0] - 30, 70), 
                           cv2.FONT_HERSHEY_SIMPLEX, 2.5, (255, 255, 255), 5)
            else:
                cv2.putText(annotated_frame, 'Status: Building analysis...', 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (128, 128, 128), 3)
            
            # 写入视频
            writer.write(annotated_frame)
            
            # 显示进度
            if frame_count % 100 == 0:
                progress = frame_count / total_frames * 100
                current_intent_name = recognizer.intent_classes[current_intent] if current_intent is not None else 'ANALYZING'
                print(f"进度: {progress:.1f}% - 当前意图: {current_intent_name}")
    
    except Exception as e:
        print(f"处理错误: {e}")
        return False
    
    finally:
        cap.release()
        writer.release()
    
    # 分段分析
    print(f"\n=== 分段意图分析 ===")
    if intent_timeline:
        # 按时间段分析意图变化
        segments = []
        current_segment = {'intent': intent_timeline[0]['intent'], 'start_time': 0, 'frames': []}
        
        for entry in intent_timeline:
            if entry['intent'] != current_segment['intent']:
                # 结束当前段，开始新段
                current_segment['end_time'] = entry['time']
                current_segment['duration'] = current_segment['end_time'] - current_segment['start_time']
                segments.append(current_segment)
                
                current_segment = {
                    'intent': entry['intent'], 
                    'start_time': entry['time'], 
                    'frames': []
                }
            
            current_segment['frames'].append(entry)
        
        # 添加最后一段
        if intent_timeline:
            current_segment['end_time'] = intent_timeline[-1]['time']
            current_segment['duration'] = current_segment['end_time'] - current_segment['start_time']
            segments.append(current_segment)
        
        # 打印分段结果
        for i, segment in enumerate(segments):
            if segment['duration'] > 1.0:  # 只显示持续1秒以上的段
                print(f"段 {i+1}: {segment['intent']} "
                      f"({segment['start_time']:.1f}s - {segment['end_time']:.1f}s, "
                      f"持续 {segment['duration']:.1f}s)")
    
    print(f"\n=== 精准意图识别完成 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测帧数: {detection_count}")
    print(f"分析帧数: {analysis_count}")
    print(f"检测率: {detection_count/frame_count*100:.1f}%")
    print(f"分析率: {analysis_count/frame_count*100:.1f}%")
    print(f"\n意图分布:")
    for intent, count in intent_stats.items():
        percentage = count / frame_count * 100 if frame_count > 0 else 0
        print(f"  {intent}: {count} 帧 ({percentage:.1f}%)")
    print(f"输出视频: {output_path}")
    
    return True

if __name__ == '__main__':
    success = analyze_video_precise()
    if success:
        print("\n🎉 精准意图识别完成！")
        print("改进特点:")
        print("- ✅ 基于腿部交替运动检测直走")
        print("- ✅ 基于身体整体角度变化检测转弯")
        print("- ✅ 分段分析识别意图变化")
        print("- ✅ 降低阈值提高敏感度")
    else:
        print("\n❌ 精准意图识别失败！")

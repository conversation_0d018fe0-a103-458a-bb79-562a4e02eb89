"""
高级意图分析器 - 基于用户建议的专业逻辑
包含5种状态：直走、左转弯、右转弯、转身、站立
"""

import cv2
import numpy as np
import os
from collections import deque
from ultralytics import YOLO
import math

class AdvancedIntentAnalyzer:
    def __init__(self):
        # 5种意图状态
        self.intent_classes = ['straight', 'left', 'right', 'turn_around', 'stop']
        self.intent_names = ['STRAIGHT', 'LEFT', 'RIGHT', 'TURN AROUND', 'STOP']
        self.intent_colors = [
            (0, 255, 0),    # 直走 - 绿色
            (255, 0, 0),    # 左转弯 - 蓝色
            (0, 0, 255),    # 右转弯 - 红色
            (255, 0, 255),  # 转身 - 紫色
            (0, 255, 255)   # 站立 - 黄色
        ]
        
        # 历史数据缓冲区
        self.pose_history = deque(maxlen=20)
        self.angle_history = deque(maxlen=15)
        self.leg_motion_history = deque(maxlen=10)
        self.face_direction_history = deque(maxlen=8)
        
        # 关键点索引
        self.keypoints_idx = {
            'nose': 0, 'left_eye': 1, 'right_eye': 2, 'left_ear': 3, 'right_ear': 4,
            'left_shoulder': 5, 'right_shoulder': 6, 'left_elbow': 7, 'right_elbow': 8,
            'left_wrist': 9, 'right_wrist': 10, 'left_hip': 11, 'right_hip': 12,
            'left_knee': 13, 'right_knee': 14, 'left_ankle': 15, 'right_ankle': 16
        }
    
    def calculate_angle(self, p1, p2, p3):
        """计算三点之间的角度"""
        v1 = p1 - p2
        v2 = p3 - p2
        
        cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2) + 1e-6)
        cos_angle = np.clip(cos_angle, -1.0, 1.0)
        angle = np.arccos(cos_angle)
        return np.degrees(angle)
    
    def analyze_body_angles(self, keypoints, confidence):
        """分析身体角度变化"""
        angles = {}
        
        # 获取关键点
        left_shoulder = keypoints[self.keypoints_idx['left_shoulder']]
        right_shoulder = keypoints[self.keypoints_idx['right_shoulder']]
        left_hip = keypoints[self.keypoints_idx['left_hip']]
        right_hip = keypoints[self.keypoints_idx['right_hip']]
        left_ankle = keypoints[self.keypoints_idx['left_ankle']]
        right_ankle = keypoints[self.keypoints_idx['right_ankle']]
        
        # 检查关键点可见性
        required_points = ['left_shoulder', 'right_shoulder', 'left_hip', 'right_hip']
        if all(confidence[self.keypoints_idx[point]] > 0.4 for point in required_points):
            
            # 1. 肩膀角度（身体朝向）
            shoulder_vector = right_shoulder - left_shoulder
            shoulder_angle = np.degrees(np.arctan2(shoulder_vector[1], shoulder_vector[0]))
            angles['shoulder_angle'] = shoulder_angle
            
            # 2. 髋部角度
            hip_vector = right_hip - left_hip
            hip_angle = np.degrees(np.arctan2(hip_vector[1], hip_vector[0]))
            angles['hip_angle'] = hip_angle
            
            # 3. 身体中轴角度（肩膀中心到髋部中心）
            shoulder_center = (left_shoulder + right_shoulder) / 2
            hip_center = (left_hip + right_hip) / 2
            body_vector = hip_center - shoulder_center
            body_angle = np.degrees(np.arctan2(body_vector[1], body_vector[0]))
            angles['body_angle'] = body_angle
            
            # 4. 脚踝角度（如果可见）
            if (confidence[self.keypoints_idx['left_ankle']] > 0.4 and 
                confidence[self.keypoints_idx['right_ankle']] > 0.4):
                ankle_vector = right_ankle - left_ankle
                ankle_angle = np.degrees(np.arctan2(ankle_vector[1], ankle_vector[0]))
                angles['ankle_angle'] = ankle_angle
        
        return angles
    
    def analyze_leg_motion(self, keypoints, confidence):
        """分析腿部运动频率"""
        leg_motion = {}
        
        # 获取腿部关键点
        left_knee = keypoints[self.keypoints_idx['left_knee']]
        right_knee = keypoints[self.keypoints_idx['right_knee']]
        left_ankle = keypoints[self.keypoints_idx['left_ankle']]
        right_ankle = keypoints[self.keypoints_idx['right_ankle']]
        
        # 检查腿部关键点可见性
        leg_points = ['left_knee', 'right_knee', 'left_ankle', 'right_ankle']
        visible_legs = sum(1 for point in leg_points if confidence[self.keypoints_idx[point]] > 0.4)
        
        if visible_legs >= 2:
            # 计算膝盖高度变化（步行时膝盖会上下移动）
            if confidence[self.keypoints_idx['left_knee']] > 0.4:
                leg_motion['left_knee_y'] = left_knee[1]
            if confidence[self.keypoints_idx['right_knee']] > 0.4:
                leg_motion['right_knee_y'] = right_knee[1]
            
            # 计算脚踝距离变化（步行时双脚距离会变化）
            if (confidence[self.keypoints_idx['left_ankle']] > 0.4 and 
                confidence[self.keypoints_idx['right_ankle']] > 0.4):
                ankle_distance = np.linalg.norm(right_ankle - left_ankle)
                leg_motion['ankle_distance'] = ankle_distance
        
        return leg_motion
    
    def analyze_face_direction(self, keypoints, confidence):
        """分析面部朝向"""
        face_direction = None
        
        # 获取面部关键点
        nose = keypoints[self.keypoints_idx['nose']]
        left_eye = keypoints[self.keypoints_idx['left_eye']]
        right_eye = keypoints[self.keypoints_idx['right_eye']]
        left_ear = keypoints[self.keypoints_idx['left_ear']]
        right_ear = keypoints[self.keypoints_idx['right_ear']]
        
        # 检查面部关键点可见性
        face_points = ['nose', 'left_eye', 'right_eye']
        visible_face = sum(1 for point in face_points if confidence[self.keypoints_idx[point]] > 0.4)
        
        if visible_face >= 2:
            # 如果能看到鼻子和眼睛，说明是正面或侧面
            if confidence[self.keypoints_idx['nose']] > 0.4:
                if (confidence[self.keypoints_idx['left_eye']] > 0.4 and 
                    confidence[self.keypoints_idx['right_eye']] > 0.4):
                    face_direction = 'front'  # 正面
                elif (confidence[self.keypoints_idx['left_eye']] > 0.4 or 
                      confidence[self.keypoints_idx['right_eye']] > 0.4):
                    face_direction = 'side'   # 侧面
            else:
                face_direction = 'back'   # 背面
        else:
            # 如果面部关键点不可见，可能是背面
            face_direction = 'back'
        
        return face_direction
    
    def determine_intent(self):
        """综合判断意图"""
        if len(self.pose_history) < 5:
            return None, 0.0
        
        # 获取最近的数据
        recent_angles = list(self.angle_history)[-8:] if len(self.angle_history) >= 8 else list(self.angle_history)
        recent_leg_motion = list(self.leg_motion_history)[-5:] if len(self.leg_motion_history) >= 5 else list(self.leg_motion_history)
        recent_face_direction = list(self.face_direction_history)[-3:] if len(self.face_direction_history) >= 3 else list(self.face_direction_history)
        
        if not recent_angles:
            return None, 0.0
        
        # 1. 判断是否站立（基于腿部运动）
        is_standing = self._is_standing(recent_leg_motion)
        if is_standing:
            return 4, 0.9  # stop
        
        # 2. 判断是否转身（基于面部朝向变化和身体角度大幅变化）
        is_turning_around = self._is_turning_around(recent_angles, recent_face_direction)
        if is_turning_around:
            return 3, 0.85  # turn_around
        
        # 3. 判断转弯方向（基于身体角度变化）
        turn_direction = self._analyze_turn_direction(recent_angles)
        if turn_direction == 'left':
            return 1, 0.8  # left
        elif turn_direction == 'right':
            return 2, 0.8  # right
        
        # 4. 默认为直走
        return 0, 0.7  # straight
    
    def _is_standing(self, leg_motion_data):
        """判断是否站立"""
        if len(leg_motion_data) < 3:
            return False
        
        # 检查膝盖高度变化
        left_knee_changes = []
        right_knee_changes = []
        ankle_distance_changes = []
        
        for i in range(1, len(leg_motion_data)):
            prev_data = leg_motion_data[i-1]
            curr_data = leg_motion_data[i]
            
            if 'left_knee_y' in prev_data and 'left_knee_y' in curr_data:
                left_knee_changes.append(abs(curr_data['left_knee_y'] - prev_data['left_knee_y']))
            
            if 'right_knee_y' in prev_data and 'right_knee_y' in curr_data:
                right_knee_changes.append(abs(curr_data['right_knee_y'] - prev_data['right_knee_y']))
            
            if 'ankle_distance' in prev_data and 'ankle_distance' in curr_data:
                ankle_distance_changes.append(abs(curr_data['ankle_distance'] - prev_data['ankle_distance']))
        
        # 如果腿部运动很小，判断为站立
        avg_knee_change = np.mean(left_knee_changes + right_knee_changes) if (left_knee_changes + right_knee_changes) else 0
        avg_ankle_change = np.mean(ankle_distance_changes) if ankle_distance_changes else 0
        
        return avg_knee_change < 5 and avg_ankle_change < 10  # 像素阈值
    
    def _is_turning_around(self, angle_data, face_direction_data):
        """判断是否转身"""
        if len(angle_data) < 5:
            return False
        
        # 检查身体角度大幅变化
        shoulder_angle_changes = []
        body_angle_changes = []
        
        for i in range(1, len(angle_data)):
            prev_angles = angle_data[i-1]
            curr_angles = angle_data[i]
            
            if 'shoulder_angle' in prev_angles and 'shoulder_angle' in curr_angles:
                angle_diff = abs(curr_angles['shoulder_angle'] - prev_angles['shoulder_angle'])
                # 处理角度跨越180度的情况
                if angle_diff > 180:
                    angle_diff = 360 - angle_diff
                shoulder_angle_changes.append(angle_diff)
            
            if 'body_angle' in prev_angles and 'body_angle' in curr_angles:
                angle_diff = abs(curr_angles['body_angle'] - prev_angles['body_angle'])
                if angle_diff > 180:
                    angle_diff = 360 - angle_diff
                body_angle_changes.append(angle_diff)
        
        # 检查面部朝向变化
        face_direction_changed = False
        if len(face_direction_data) >= 2:
            if 'front' in face_direction_data and 'back' in face_direction_data:
                face_direction_changed = True
        
        # 如果身体角度大幅变化或面部朝向改变，判断为转身
        avg_shoulder_change = np.mean(shoulder_angle_changes) if shoulder_angle_changes else 0
        avg_body_change = np.mean(body_angle_changes) if body_angle_changes else 0
        
        return (avg_shoulder_change > 30 or avg_body_change > 25 or face_direction_changed)
    
    def _analyze_turn_direction(self, angle_data):
        """分析转弯方向"""
        if len(angle_data) < 3:
            return None
        
        # 分析肩膀和髋部角度变化趋势
        shoulder_trend = 0
        hip_trend = 0
        
        for i in range(1, len(angle_data)):
            prev_angles = angle_data[i-1]
            curr_angles = angle_data[i]
            
            if 'shoulder_angle' in prev_angles and 'shoulder_angle' in curr_angles:
                angle_diff = curr_angles['shoulder_angle'] - prev_angles['shoulder_angle']
                # 处理角度跨越的情况
                if angle_diff > 180:
                    angle_diff -= 360
                elif angle_diff < -180:
                    angle_diff += 360
                shoulder_trend += angle_diff
            
            if 'hip_angle' in prev_angles and 'hip_angle' in curr_angles:
                angle_diff = curr_angles['hip_angle'] - prev_angles['hip_angle']
                if angle_diff > 180:
                    angle_diff -= 360
                elif angle_diff < -180:
                    angle_diff += 360
                hip_trend += angle_diff
        
        # 综合判断转向
        total_trend = shoulder_trend + hip_trend
        
        if total_trend > 15:  # 向右转
            return 'right'
        elif total_trend < -15:  # 向左转
            return 'left'
        else:
            return None
    
    def update_analysis(self, keypoints, confidence):
        """更新分析数据"""
        # 添加当前姿态到历史
        self.pose_history.append(keypoints.copy())
        
        # 分析身体角度
        angles = self.analyze_body_angles(keypoints, confidence)
        if angles:
            self.angle_history.append(angles)
        
        # 分析腿部运动
        leg_motion = self.analyze_leg_motion(keypoints, confidence)
        if leg_motion:
            self.leg_motion_history.append(leg_motion)
        
        # 分析面部朝向
        face_direction = self.analyze_face_direction(keypoints, confidence)
        if face_direction:
            self.face_direction_history.append(face_direction)
        
        # 判断意图
        intent_id, confidence_score = self.determine_intent()
        
        return intent_id, confidence_score

def analyze_video_advanced(video_path='left.mp4', output_path='left_advanced_analysis.mp4'):
    """
    使用高级逻辑分析视频
    """
    print(f"开始高级意图分析: {video_path}")
    
    # 检查视频文件
    if not os.path.exists(video_path):
        print(f"错误: 视频文件不存在")
        return False
    
    # 加载模型
    print("加载YOLOv8模型...")
    try:
        model = YOLO('yolov8n-pose.pt')
        print("✓ 模型加载成功")
    except Exception as e:
        print(f"✗ 模型加载失败: {e}")
        return False
    
    # 打开视频
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("错误: 无法打开视频")
        return False
    
    # 视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"视频: {width}x{height}, {fps}FPS, {total_frames}帧")
    
    # 创建输出视频
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    # 创建分析器
    analyzer = AdvancedIntentAnalyzer()
    
    frame_count = 0
    detection_count = 0
    intent_stats = {name: 0 for name in analyzer.intent_names}
    intent_stats['未检测'] = 0
    
    print("开始处理...")
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 姿态检测
            results = model(frame, conf=0.3, verbose=False)
            
            annotated_frame = frame.copy()
            
            current_intent = None
            intent_confidence = 0.0
            person_detected = False
            
            # 处理检测结果
            if len(results) > 0 and results[0].keypoints is not None:
                keypoints_data = results[0].keypoints.data
                
                if len(keypoints_data) > 0:
                    person_detected = True
                    detection_count += 1
                    
                    keypoints = keypoints_data[0]
                    if keypoints.shape[0] == 17:
                        xy = keypoints[:, :2].cpu().numpy()
                        conf = keypoints[:, 2].cpu().numpy()
                        
                        # 更新分析
                        intent_id, intent_confidence = analyzer.update_analysis(xy, conf)
                        current_intent = intent_id
                        
                        # 绘制关键点
                        for i, (x, y) in enumerate(xy):
                            if conf[i] > 0.3:
                                color = (0, 255, 0) if conf[i] > 0.7 else (0, 255, 255)
                                cv2.circle(annotated_frame, (int(x), int(y)), 6, color, -1)
                        
                        # 绘制骨架
                        connections = [
                            (5, 6), (5, 7), (7, 9), (6, 8), (8, 10),
                            (5, 11), (6, 12), (11, 12),
                            (11, 13), (13, 15), (12, 14), (14, 16)
                        ]
                        
                        for start_idx, end_idx in connections:
                            if conf[start_idx] > 0.3 and conf[end_idx] > 0.3:
                                start_point = (int(xy[start_idx][0]), int(xy[start_idx][1]))
                                end_point = (int(xy[end_idx][0]), int(xy[end_idx][1]))
                                cv2.line(annotated_frame, start_point, end_point, (255, 0, 0), 3)
            
            # 更新统计
            if current_intent is not None:
                intent_stats[analyzer.intent_names[current_intent]] += 1
            else:
                intent_stats['未检测'] += 1
            
            # 绘制信息面板
            panel_width = 600
            panel_height = 200
            
            # 半透明背景
            overlay = annotated_frame.copy()
            cv2.rectangle(overlay, (10, 10), (panel_width, panel_height), (0, 0, 0), -1)
            cv2.addWeighted(overlay, 0.85, annotated_frame, 0.15, 0, annotated_frame)
            
            # 白色边框
            cv2.rectangle(annotated_frame, (10, 10), (panel_width, panel_height), (255, 255, 255), 3)
            
            # 显示信息
            y = 50
            line_height = 35
            
            # 帧信息
            cv2.putText(annotated_frame, f'Frame: {frame_count}/{total_frames} ({frame_count/fps:.1f}s)', 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
            y += line_height
            
            # 检测状态
            if person_detected:
                cv2.putText(annotated_frame, 'Person: DETECTED', 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
                y += line_height
                
                # 意图显示
                if current_intent is not None:
                    intent_text = f'Intent: {analyzer.intent_names[current_intent]}'
                    confidence_text = f'Confidence: {intent_confidence:.2f}'
                    intent_color = analyzer.intent_colors[current_intent]
                    
                    # 显示意图
                    cv2.putText(annotated_frame, intent_text, 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.2, intent_color, 3)
                    y += line_height
                    
                    # 显示置信度
                    cv2.putText(annotated_frame, confidence_text, 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, intent_color, 2)
                    
                    # 右上角大字显示
                    intent_display = analyzer.intent_names[current_intent]
                    text_size = cv2.getTextSize(intent_display, cv2.FONT_HERSHEY_SIMPLEX, 2.5, 5)[0]
                    
                    # 背景矩形
                    cv2.rectangle(annotated_frame, 
                                 (width - text_size[0] - 40, 20), 
                                 (width - 20, 100), 
                                 intent_color, -1)
                    
                    # 白色边框
                    cv2.rectangle(annotated_frame, 
                                 (width - text_size[0] - 40, 20), 
                                 (width - 20, 100), 
                                 (255, 255, 255), 3)
                    
                    # 意图文字
                    cv2.putText(annotated_frame, intent_display, 
                               (width - text_size[0] - 30, 70), 
                               cv2.FONT_HERSHEY_SIMPLEX, 2.5, (255, 255, 255), 5)
                else:
                    cv2.putText(annotated_frame, 'Intent: ANALYZING...', 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (128, 128, 128), 3)
            else:
                cv2.putText(annotated_frame, 'Person: NOT DETECTED', 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
            
            # 写入视频
            writer.write(annotated_frame)
            
            # 显示进度
            if frame_count % 100 == 0:
                progress = frame_count / total_frames * 100
                current_intent_name = analyzer.intent_names[current_intent] if current_intent is not None else '未检测'
                print(f"处理进度: {progress:.1f}% - 当前意图: {current_intent_name}")
    
    except Exception as e:
        print(f"处理错误: {e}")
        return False
    
    finally:
        cap.release()
        writer.release()
    
    print(f"\n=== 高级分析完成 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测帧数: {detection_count}")
    print(f"检测率: {detection_count/frame_count*100:.1f}%")
    print(f"\n意图分布:")
    for intent, count in intent_stats.items():
        percentage = count / frame_count * 100 if frame_count > 0 else 0
        print(f"  {intent}: {count} 帧 ({percentage:.1f}%)")
    print(f"输出视频: {output_path}")
    
    return True

if __name__ == '__main__':
    success = analyze_video_advanced()
    if success:
        print("\n🎉 高级意图分析完成！")
        print("请查看 left_advanced_analysis.mp4")
    else:
        print("\n❌ 高级意图分析失败！")

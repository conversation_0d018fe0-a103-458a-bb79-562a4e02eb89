"""
调试背部视角的移动数据
查看实际的X、Y移动模式
"""

import cv2
import numpy as np
import os
from collections import deque
from ultralytics import YOLO
import json
import matplotlib.pyplot as plt

class BackViewMovementDebugger:
    def __init__(self):
        self.model = YOLO('yolov8n-pose.pt')
        self.movement_data = []
        
    def analyze_frame(self, frame, frame_num):
        """分析单帧的移动数据"""
        results = self.model(frame, conf=0.3, verbose=False)
        
        if len(results) > 0 and results[0].keypoints is not None:
            keypoints_data = results[0].keypoints.data
            
            if len(keypoints_data) > 0:
                keypoints = keypoints_data[0]
                
                if keypoints.shape[0] == 17:
                    xy = keypoints[:, :2].cpu().numpy()
                    conf = keypoints[:, 2].cpu().numpy()
                    
                    height, width = frame.shape[:2]
                    
                    # 归一化
                    normalized_xy = xy.copy()
                    normalized_xy[:, 0] /= width
                    normalized_xy[:, 1] /= height
                    
                    # 提取身体中心
                    if all(conf[i] > 0.4 for i in [5, 6, 11, 12]):
                        shoulder_center = (normalized_xy[5] + normalized_xy[6]) / 2
                        hip_center = (normalized_xy[11] + normalized_xy[12]) / 2
                        body_center = (shoulder_center + hip_center) / 2
                        
                        frame_data = {
                            'frame': frame_num,
                            'time': frame_num / 30.0,  # 假设30fps
                            'body_center_x': float(body_center[0]),
                            'body_center_y': float(body_center[1]),
                            'shoulder_center_x': float(shoulder_center[0]),
                            'shoulder_center_y': float(shoulder_center[1]),
                            'hip_center_x': float(hip_center[0]),
                            'hip_center_y': float(hip_center[1])
                        }
                        
                        self.movement_data.append(frame_data)
                        return frame_data
        
        return None

def debug_back_view_movement(video_path='left.mp4', sample_frames=300):
    """调试背部视角的移动数据"""
    print(f"调试背部视角移动数据: {video_path}")
    print(f"采样帧数: {sample_frames}")
    
    if not os.path.exists(video_path):
        print(f"错误: 视频文件不存在")
        return False
    
    debugger = BackViewMovementDebugger()
    
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("错误: 无法打开视频")
        return False
    
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    
    # 计算采样间隔
    sample_interval = max(1, total_frames // sample_frames)
    
    frame_count = 0
    sampled_count = 0
    
    print("开始采样分析...")
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 按间隔采样
            if frame_count % sample_interval == 0:
                data = debugger.analyze_frame(frame, frame_count)
                if data:
                    sampled_count += 1
                    
                    if sampled_count % 50 == 0:
                        print(f"已采样 {sampled_count} 帧...")
    
    except Exception as e:
        print(f"处理错误: {e}")
        return False
    
    finally:
        cap.release()
    
    # 分析数据
    print(f"\n=== 背部视角移动数据分析 ===")
    print(f"总帧数: {frame_count}")
    print(f"采样帧数: {sampled_count}")
    print(f"中点帧: {total_frames // 2}")
    
    if len(debugger.movement_data) == 0:
        print("没有有效的数据")
        return False
    
    # 保存原始数据
    with open('back_view_movement_data.json', 'w') as f:
        json.dump(debugger.movement_data, f, indent=2)
    
    print(f"原始数据已保存: back_view_movement_data.json")
    
    # 分析移动模式
    data = debugger.movement_data
    
    # 计算移动向量
    movements = []
    for i in range(1, len(data)):
        prev_center = np.array([data[i-1]['body_center_x'], data[i-1]['body_center_y']])
        curr_center = np.array([data[i]['body_center_x'], data[i]['body_center_y']])
        movement = curr_center - prev_center
        
        movements.append({
            'frame': data[i]['frame'],
            'time': data[i]['time'],
            'movement_x': movement[0],
            'movement_y': movement[1],
            'movement_magnitude': np.linalg.norm(movement),
            'is_first_half': data[i]['frame'] <= total_frames // 2
        })
    
    # 分段分析
    first_half_movements = [m for m in movements if m['is_first_half']]
    second_half_movements = [m for m in movements if not m['is_first_half']]
    
    def analyze_segment(segment_movements, name):
        if not segment_movements:
            return
        
        x_movements = [m['movement_x'] for m in segment_movements]
        y_movements = [m['movement_y'] for m in segment_movements]
        magnitudes = [m['movement_magnitude'] for m in segment_movements]
        
        print(f"\n📊 {name}移动分析:")
        print(f"  X方向移动: 平均={np.mean(x_movements):.6f}, 标准差={np.std(x_movements):.6f}")
        print(f"  Y方向移动: 平均={np.mean(y_movements):.6f}, 标准差={np.std(y_movements):.6f}")
        print(f"  移动幅度: 平均={np.mean(magnitudes):.6f}, 最大={np.max(magnitudes):.6f}")
        print(f"  X/Y比值: {abs(np.mean(x_movements)) / abs(np.mean(y_movements)):.3f}" if abs(np.mean(y_movements)) > 1e-6 else "N/A")
        
        # 累积移动
        cumulative_x = sum(x_movements)
        cumulative_y = sum(y_movements)
        print(f"  累积X移动: {cumulative_x:.6f}")
        print(f"  累积Y移动: {cumulative_y:.6f}")
        
        # 移动方向分析
        left_movements = len([x for x in x_movements if x < -0.001])
        right_movements = len([x for x in x_movements if x > 0.001])
        forward_movements = len([y for y in y_movements if y > 0.001])
        backward_movements = len([y for y in y_movements if y < -0.001])
        
        print(f"  方向统计: 左移{left_movements}次, 右移{right_movements}次, 前移{forward_movements}次, 后移{backward_movements}次")
        
        return {
            'avg_x': np.mean(x_movements),
            'avg_y': np.mean(y_movements),
            'std_x': np.std(x_movements),
            'std_y': np.std(y_movements),
            'cumulative_x': cumulative_x,
            'cumulative_y': cumulative_y
        }
    
    first_half_stats = analyze_segment(first_half_movements, "前半段")
    second_half_stats = analyze_segment(second_half_movements, "后半段")
    
    # 对比分析
    print(f"\n🔍 前后半段对比:")
    if first_half_stats and second_half_stats:
        x_change = second_half_stats['avg_x'] - first_half_stats['avg_x']
        y_change = second_half_stats['avg_y'] - first_half_stats['avg_y']
        
        print(f"  X方向变化: {x_change:.6f} ({'左转增强' if x_change < 0 else '右转增强' if x_change > 0 else '无变化'})")
        print(f"  Y方向变化: {y_change:.6f} ({'减速' if y_change < 0 else '加速' if y_change > 0 else '无变化'})")
        
        # 判断是否符合"前半段直行，后半段左转"
        print(f"\n✅ 模式验证:")
        
        # 前半段应该X移动很小，Y移动稳定
        first_half_straight = (abs(first_half_stats['avg_x']) < 0.002 and 
                              first_half_stats['std_x'] < 0.005 and
                              first_half_stats['avg_y'] > 0)
        
        # 后半段应该有明显的左移（X负方向）
        second_half_left = (second_half_stats['avg_x'] < -0.002 or 
                           second_half_stats['cumulative_x'] < -0.01)
        
        print(f"  前半段直行特征: {'✓' if first_half_straight else '✗'}")
        print(f"    - X移动小: {abs(first_half_stats['avg_x']) < 0.002} (实际: {first_half_stats['avg_x']:.6f})")
        print(f"    - X稳定: {first_half_stats['std_x'] < 0.005} (实际: {first_half_stats['std_x']:.6f})")
        print(f"    - 向前移动: {first_half_stats['avg_y'] > 0} (实际: {first_half_stats['avg_y']:.6f})")
        
        print(f"  后半段左转特征: {'✓' if second_half_left else '✗'}")
        print(f"    - 平均左移: {second_half_stats['avg_x'] < -0.002} (实际: {second_half_stats['avg_x']:.6f})")
        print(f"    - 累积左移: {second_half_stats['cumulative_x'] < -0.01} (实际: {second_half_stats['cumulative_x']:.6f})")
    
    # 建议阈值
    print(f"\n💡 建议的检测阈值:")
    if first_half_stats and second_half_stats:
        # 基于实际数据建议阈值
        straight_x_threshold = max(abs(first_half_stats['avg_x']) * 2, 0.001)
        straight_x_std_threshold = first_half_stats['std_x'] * 1.5
        left_turn_threshold = min(second_half_stats['avg_x'] * 0.8, -0.001)
        
        print(f"  直行X移动阈值: ±{straight_x_threshold:.6f}")
        print(f"  直行X稳定性阈值: {straight_x_std_threshold:.6f}")
        print(f"  左转X移动阈值: {left_turn_threshold:.6f}")
    
    # 生成可视化图表
    try:
        plt.figure(figsize=(15, 10))
        
        # 子图1: X方向移动
        plt.subplot(2, 3, 1)
        frames = [d['frame'] for d in data]
        x_positions = [d['body_center_x'] for d in data]
        plt.plot(frames, x_positions, 'b-', linewidth=2)
        plt.axvline(x=total_frames//2, color='r', linestyle='--', label='中点')
        plt.title('X位置变化')
        plt.xlabel('帧数')
        plt.ylabel('X坐标')
        plt.legend()
        plt.grid(True)
        
        # 子图2: Y方向移动
        plt.subplot(2, 3, 2)
        y_positions = [d['body_center_y'] for d in data]
        plt.plot(frames, y_positions, 'g-', linewidth=2)
        plt.axvline(x=total_frames//2, color='r', linestyle='--', label='中点')
        plt.title('Y位置变化')
        plt.xlabel('帧数')
        plt.ylabel('Y坐标')
        plt.legend()
        plt.grid(True)
        
        # 子图3: X方向移动速度
        plt.subplot(2, 3, 3)
        movement_frames = [m['frame'] for m in movements]
        x_velocities = [m['movement_x'] for m in movements]
        plt.plot(movement_frames, x_velocities, 'r-', linewidth=1)
        plt.axhline(y=0, color='k', linestyle='-', alpha=0.3)
        plt.axvline(x=total_frames//2, color='r', linestyle='--', label='中点')
        plt.title('X方向移动速度')
        plt.xlabel('帧数')
        plt.ylabel('X移动量')
        plt.legend()
        plt.grid(True)
        
        # 子图4: Y方向移动速度
        plt.subplot(2, 3, 4)
        y_velocities = [m['movement_y'] for m in movements]
        plt.plot(movement_frames, y_velocities, 'g-', linewidth=1)
        plt.axhline(y=0, color='k', linestyle='-', alpha=0.3)
        plt.axvline(x=total_frames//2, color='r', linestyle='--', label='中点')
        plt.title('Y方向移动速度')
        plt.xlabel('帧数')
        plt.ylabel('Y移动量')
        plt.legend()
        plt.grid(True)
        
        # 子图5: 移动轨迹
        plt.subplot(2, 3, 5)
        plt.plot(x_positions, y_positions, 'b-', linewidth=2, marker='o', markersize=2)
        plt.scatter(x_positions[0], y_positions[0], color='green', s=100, label='起点')
        plt.scatter(x_positions[-1], y_positions[-1], color='red', s=100, label='终点')
        mid_idx = len(x_positions) // 2
        plt.scatter(x_positions[mid_idx], y_positions[mid_idx], color='orange', s=100, label='中点')
        plt.title('移动轨迹')
        plt.xlabel('X坐标')
        plt.ylabel('Y坐标')
        plt.legend()
        plt.grid(True)
        plt.axis('equal')
        
        # 子图6: 累积移动
        plt.subplot(2, 3, 6)
        cumulative_x = np.cumsum([m['movement_x'] for m in movements])
        cumulative_y = np.cumsum([m['movement_y'] for m in movements])
        plt.plot(movement_frames, cumulative_x, 'r-', linewidth=2, label='累积X移动')
        plt.plot(movement_frames, cumulative_y, 'g-', linewidth=2, label='累积Y移动')
        plt.axvline(x=total_frames//2, color='r', linestyle='--', alpha=0.5)
        plt.title('累积移动')
        plt.xlabel('帧数')
        plt.ylabel('累积移动量')
        plt.legend()
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig('back_view_movement_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"可视化图表已保存: back_view_movement_analysis.png")
        
    except Exception as e:
        print(f"生成图表失败: {e}")
    
    return True

if __name__ == '__main__':
    success = debug_back_view_movement()
    if success:
        print("\n🎉 背部视角移动数据调试完成！")
        print("请查看:")
        print("- back_view_movement_data.json (原始数据)")
        print("- back_view_movement_analysis.png (可视化图表)")
        print("- 上面的统计分析结果")
    else:
        print("\n❌ 背部视角移动数据调试失败！")

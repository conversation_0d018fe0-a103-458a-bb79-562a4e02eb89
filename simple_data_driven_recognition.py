"""
基于实际数据的简单意图识别
根据调试结果设计的阈值
"""

import cv2
import numpy as np
import os
from collections import deque
from ultralytics import YOLO

class DataDrivenRecognizer:
    def __init__(self, sequence_length=8):
        self.sequence_length = sequence_length
        
        # 意图类别
        self.intent_classes = ['STRAIGHT', 'LEFT', 'RIGHT', 'TURN_AROUND', 'STOP']
        self.intent_colors = [
            (0, 255, 0),    # STRAIGHT - 绿色
            (255, 0, 0),    # LEFT - 蓝色
            (0, 0, 255),    # RIGHT - 红色
            (255, 0, 255),  # TURN_AROUND - 紫色
            (0, 255, 255)   # STOP - 黄色
        ]
        
        # 基于调试数据的阈值
        self.movement_threshold = 0.000001  # 极低的移动阈值
        self.angle_change_threshold = 0.05  # 基于实际数据的角度变化阈值
        self.significant_angle_threshold = 0.1  # 显著角度变化
        
        # 时序数据缓冲区
        self.body_center_sequence = deque(maxlen=sequence_length + 5)
        self.body_angle_sequence = deque(maxlen=sequence_length + 5)
        
        # 加载姿态检测模型
        print("加载YOLOv8姿态检测模型...")
        self.model = YOLO('yolov8n-pose.pt')
        print("✓ 模型加载成功")
        print(f"使用阈值: 移动={self.movement_threshold}, 角度变化={self.angle_change_threshold}°")
    
    def extract_features(self, keypoints, confidence):
        """提取基本特征"""
        features = {}
        
        # 身体中心和角度
        if all(confidence[i] > 0.4 for i in [5, 6, 11, 12]):
            shoulder_center = (keypoints[5] + keypoints[6]) / 2
            hip_center = (keypoints[11] + keypoints[12]) / 2
            body_center = (shoulder_center + hip_center) / 2
            
            # 身体朝向角度
            body_vector = shoulder_center - hip_center
            body_angle = np.degrees(np.arctan2(body_vector[1], body_vector[0]))
            
            features['body_center'] = body_center
            features['body_angle'] = body_angle
        
        return features
    
    def analyze_movement(self):
        """分析移动模式"""
        if len(self.body_center_sequence) < 3:
            return None, 0.0, {}
        
        centers = np.array(list(self.body_center_sequence))
        
        # 计算移动距离
        movements = []
        for i in range(1, len(centers)):
            movement = np.linalg.norm(centers[i] - centers[i-1])
            movements.append(movement)
        
        avg_movement = np.mean(movements) if movements else 0
        max_movement = np.max(movements) if movements else 0
        
        # 判断是否在移动
        is_moving = avg_movement > self.movement_threshold
        
        analysis = {
            'avg_movement': avg_movement,
            'max_movement': max_movement,
            'is_moving': is_moving
        }
        
        return is_moving, avg_movement, analysis
    
    def analyze_direction_change(self):
        """分析方向变化"""
        if len(self.body_angle_sequence) < self.sequence_length:
            return None, 0.0, {}
        
        angles = list(self.body_angle_sequence)
        
        # 计算角度变化
        angle_changes = []
        for i in range(1, len(angles)):
            change = angles[i] - angles[i-1]
            
            # 处理角度跨越
            if change > 180:
                change -= 360
            elif change < -180:
                change += 360
            
            angle_changes.append(change)
        
        if not angle_changes:
            return None, 0.0, {}
        
        # 分析变化趋势
        total_change = sum(angle_changes)
        avg_change = np.mean(angle_changes)
        abs_changes = [abs(c) for c in angle_changes]
        max_abs_change = max(abs_changes)
        
        analysis = {
            'total_change': total_change,
            'avg_change': avg_change,
            'max_abs_change': max_abs_change,
            'angle_changes': angle_changes
        }
        
        # 判断转向
        direction = None
        confidence = 0.0
        
        # 基于累积变化判断
        if abs(total_change) > self.significant_angle_threshold:
            if total_change < 0:
                direction = 'LEFT'
            else:
                direction = 'RIGHT'
            confidence = min(0.9, abs(total_change) / 1.0)
        
        # 基于平均变化判断
        elif abs(avg_change) > self.angle_change_threshold:
            if avg_change < 0:
                direction = 'LEFT'
            else:
                direction = 'RIGHT'
            confidence = min(0.8, abs(avg_change) / 0.2)
        
        return direction, confidence, analysis
    
    def classify_intent_simple(self):
        """简单的意图分类"""
        
        # 1. 分析移动
        is_moving, movement_strength, movement_analysis = self.analyze_movement()
        
        if not is_moving:
            return 4, 0.8, {'reason': 'not_moving', 'movement_analysis': movement_analysis}
        
        # 2. 分析方向变化
        direction, direction_confidence, direction_analysis = self.analyze_direction_change()
        
        if direction and direction_confidence > 0.3:
            if direction == 'LEFT':
                return 1, direction_confidence, {
                    'reason': 'left_turn', 
                    'movement_analysis': movement_analysis,
                    'direction_analysis': direction_analysis
                }
            elif direction == 'RIGHT':
                return 2, direction_confidence, {
                    'reason': 'right_turn',
                    'movement_analysis': movement_analysis, 
                    'direction_analysis': direction_analysis
                }
        
        # 3. 默认为直行（如果在移动但没有明显转向）
        if is_moving:
            return 0, 0.7, {
                'reason': 'straight_walking',
                'movement_analysis': movement_analysis,
                'direction_analysis': direction_analysis
            }
        
        return None, 0.0, {}
    
    def update_and_classify(self, keypoints, confidence):
        """更新数据并分类"""
        
        # 提取特征
        features = self.extract_features(keypoints, confidence)
        
        # 更新历史数据
        if 'body_center' in features:
            self.body_center_sequence.append(features['body_center'])
        
        if 'body_angle' in features:
            self.body_angle_sequence.append(features['body_angle'])
        
        # 进行分类
        if (len(self.body_center_sequence) >= 3 and 
            len(self.body_angle_sequence) >= self.sequence_length):
            
            intent, confidence, debug_info = self.classify_intent_simple()
            return intent, confidence, debug_info
        
        return None, 0.0, {'reason': 'insufficient_data'}

def analyze_video_data_driven(video_path='left.mp4', output_path='left_data_driven_analysis.mp4'):
    """
    使用数据驱动的方法分析视频
    """
    print(f"开始数据驱动意图识别: {video_path}")
    print("特点: 基于实际调试数据设计的阈值")
    
    if not os.path.exists(video_path):
        print(f"错误: 视频文件不存在")
        return False
    
    # 创建识别器
    recognizer = DataDrivenRecognizer(sequence_length=8)
    
    # 打开视频
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("错误: 无法打开视频")
        return False
    
    # 视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"视频: {width}x{height}, {fps}FPS, {total_frames}帧")
    
    # 创建输出视频
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    frame_count = 0
    detection_count = 0
    analysis_count = 0
    intent_stats = {name: 0 for name in recognizer.intent_classes}
    intent_stats['ANALYZING'] = 0
    
    # 分段统计
    segment_size = total_frames // 4  # 分为4段
    segment_stats = []
    
    print("开始处理...")
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 姿态检测
            results = recognizer.model(frame, conf=0.3, verbose=False)
            
            annotated_frame = frame.copy()
            current_intent = None
            intent_confidence = 0.0
            debug_info = {}
            
            # 处理检测结果
            if len(results) > 0 and results[0].keypoints is not None:
                keypoints_data = results[0].keypoints.data
                
                if len(keypoints_data) > 0:
                    detection_count += 1
                    keypoints = keypoints_data[0]
                    
                    if keypoints.shape[0] == 17:
                        xy = keypoints[:, :2].cpu().numpy()
                        conf = keypoints[:, 2].cpu().numpy()
                        
                        # 归一化关键点
                        normalized_xy = xy.copy()
                        normalized_xy[:, 0] /= width
                        normalized_xy[:, 1] /= height
                        
                        # 数据驱动分析
                        intent_id, confidence, debug_info = recognizer.update_and_classify(
                            normalized_xy, conf
                        )
                        
                        if intent_id is not None:
                            current_intent = intent_id
                            intent_confidence = confidence
                            analysis_count += 1
                        
                        # 绘制关键点
                        for i, (x, y) in enumerate(xy):
                            if conf[i] > 0.3:
                                color = (0, 255, 0) if conf[i] > 0.7 else (0, 255, 255)
                                cv2.circle(annotated_frame, (int(x), int(y)), 6, color, -1)
                        
                        # 绘制骨架
                        connections = [
                            (5, 6), (5, 7), (7, 9), (6, 8), (8, 10),
                            (5, 11), (6, 12), (11, 12),
                            (11, 13), (13, 15), (12, 14), (14, 16)
                        ]
                        
                        for start_idx, end_idx in connections:
                            if conf[start_idx] > 0.3 and conf[end_idx] > 0.3:
                                start_point = (int(xy[start_idx][0]), int(xy[start_idx][1]))
                                end_point = (int(xy[end_idx][0]), int(xy[end_idx][1]))
                                cv2.line(annotated_frame, start_point, end_point, (255, 0, 0), 3)
                        
                        # 绘制身体中心轨迹
                        if len(recognizer.body_center_sequence) >= 2:
                            for i in range(1, len(recognizer.body_center_sequence)):
                                prev_center = recognizer.body_center_sequence[i-1] * np.array([width, height])
                                curr_center = recognizer.body_center_sequence[i] * np.array([width, height])
                                cv2.line(annotated_frame,
                                        (int(prev_center[0]), int(prev_center[1])),
                                        (int(curr_center[0]), int(curr_center[1])),
                                        (255, 255, 0), 4)
            
            # 更新统计
            if current_intent is not None:
                intent_stats[recognizer.intent_classes[current_intent]] += 1
            else:
                intent_stats['ANALYZING'] += 1
            
            # 绘制信息面板
            panel_width = 800
            panel_height = 300
            
            # 半透明背景
            overlay = annotated_frame.copy()
            cv2.rectangle(overlay, (10, 10), (panel_width, panel_height), (0, 0, 0), -1)
            cv2.addWeighted(overlay, 0.85, annotated_frame, 0.15, 0, annotated_frame)
            
            # 白色边框
            cv2.rectangle(annotated_frame, (10, 10), (panel_width, panel_height), (255, 255, 255), 3)
            
            # 显示信息
            y = 40
            line_height = 25
            
            # 标题
            cv2.putText(annotated_frame, "Data-Driven Intent Recognition", 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (255, 255, 255), 2)
            y += line_height + 5
            
            # 帧信息
            cv2.putText(annotated_frame, f'Frame: {frame_count}/{total_frames} ({frame_count/fps:.1f}s)', 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            y += line_height
            
            # 意图显示
            if current_intent is not None:
                intent_text = f'Intent: {recognizer.intent_classes[current_intent]}'
                confidence_text = f'Confidence: {intent_confidence:.2f}'
                intent_color = recognizer.intent_colors[current_intent]
                
                cv2.putText(annotated_frame, intent_text, 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.2, intent_color, 3)
                y += line_height + 5
                
                cv2.putText(annotated_frame, confidence_text, 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, intent_color, 2)
                y += line_height
                
                # 显示调试信息
                if debug_info:
                    reason = debug_info.get('reason', 'unknown')
                    cv2.putText(annotated_frame, f"Reason: {reason}", 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (200, 200, 200), 2)
                    y += line_height
                    
                    # 显示移动分析
                    movement_analysis = debug_info.get('movement_analysis', {})
                    if 'avg_movement' in movement_analysis:
                        cv2.putText(annotated_frame, f"Movement: {movement_analysis['avg_movement']:.6f}", 
                                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
                        y += 18
                    
                    # 显示角度分析
                    direction_analysis = debug_info.get('direction_analysis', {})
                    if 'avg_change' in direction_analysis:
                        cv2.putText(annotated_frame, f"Angle Change: {direction_analysis['avg_change']:.4f}°", 
                                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
                
                # 右上角大字显示
                intent_display = recognizer.intent_classes[current_intent]
                text_size = cv2.getTextSize(intent_display, cv2.FONT_HERSHEY_SIMPLEX, 2.5, 5)[0]
                
                cv2.rectangle(annotated_frame, 
                             (width - text_size[0] - 40, 20), 
                             (width - 20, 100), 
                             intent_color, -1)
                cv2.rectangle(annotated_frame, 
                             (width - text_size[0] - 40, 20), 
                             (width - 20, 100), 
                             (255, 255, 255), 3)
                cv2.putText(annotated_frame, intent_display, 
                           (width - text_size[0] - 30, 70), 
                           cv2.FONT_HERSHEY_SIMPLEX, 2.5, (255, 255, 255), 5)
            else:
                cv2.putText(annotated_frame, 'Status: Building data...', 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (128, 128, 128), 3)
            
            # 写入视频
            writer.write(annotated_frame)
            
            # 显示进度
            if frame_count % 100 == 0:
                progress = frame_count / total_frames * 100
                current_intent_name = recognizer.intent_classes[current_intent] if current_intent is not None else 'ANALYZING'
                print(f"进度: {progress:.1f}% - 当前意图: {current_intent_name}")
    
    except Exception as e:
        print(f"处理错误: {e}")
        return False
    
    finally:
        cap.release()
        writer.release()
    
    print(f"\n=== 数据驱动意图识别完成 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测帧数: {detection_count}")
    print(f"分析帧数: {analysis_count}")
    print(f"检测率: {detection_count/frame_count*100:.1f}%")
    print(f"分析率: {analysis_count/frame_count*100:.1f}%")
    print(f"\n意图分布:")
    for intent, count in intent_stats.items():
        percentage = count / frame_count * 100 if frame_count > 0 else 0
        print(f"  {intent}: {count} 帧 ({percentage:.1f}%)")
    print(f"输出视频: {output_path}")
    
    return True

if __name__ == '__main__':
    success = analyze_video_data_driven()
    if success:
        print("\n🎉 数据驱动意图识别完成！")
        print("特点:")
        print("- ✅ 基于实际调试数据的阈值")
        print("- ✅ 极低的移动检测阈值")
        print("- ✅ 精确的角度变化阈值")
        print("- ✅ 简化的分类逻辑")
    else:
        print("\n❌ 数据驱动意图识别失败！")

import sys
import os

print("Python executable:", sys.executable)
print("Python version:", sys.version)
print("Current working directory:", os.getcwd())

try:
    import torch
    print("PyTorch version:", torch.__version__)
    print("CUDA available:", torch.cuda.is_available())
except ImportError:
    print("PyTorch not installed")

try:
    import cv2
    print("OpenCV version:", cv2.__version__)
except ImportError:
    print("OpenCV not installed")

try:
    from ultralytics import YOLO
    print("Ultralytics installed successfully")
except ImportError:
    print("Ultralytics not installed")

try:
    import numpy as np
    print("NumPy version:", np.__version__)
except ImportError:
    print("NumPy not installed")

# 快速开始指南

## 系统概述

这是一个基于双层GRU的人体姿态意图识别系统，专为跟随机器人设计。系统能够：

- 实时检测人体姿态（使用YOLOv8n-pose）
- 处理关节遮挡情况
- 识别行人意图：直行、左转、右转、停止
- 提供实时推理和可视化

## 核心技术

- **双层GRU架构**：第一层负责姿态补全，第二层负责意图识别
- **遮挡处理**：时间插值 + 对称性补全
- **注意力机制**：关注关键时间步
- **数据增强**：水平翻转、噪声添加、旋转

## 安装步骤

### 1. 环境要求
- Python 3.8+
- PyTorch 1.12+
- CUDA（推荐，用于GPU加速）

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 验证安装
```bash
python test_system.py
```

## 使用流程

### 方式一：使用脚本（推荐新手）

```bash
# 1. 创建示例数据
python main.py --mode create_data

# 2. 训练模型
python main.py --mode train

# 3. 实时推理
python main.py --mode inference --inference_mode camera
```

### 方式二：使用示例脚本

```bash
# 运行完整示例
bash run_examples.sh

# 或者运行演示
python demo.py
```

## 详细使用说明

### 1. 数据准备

#### 使用示例数据
```bash
python main.py --mode create_data --data_dir data
```

#### 使用自定义数据
准备JSON格式数据，包含：
- `pose_sequence`: 姿态序列 (T, 17, 2)
- `confidence`: 置信度序列 (T, 17)
- `label`: 意图标签 (0-3)

### 2. 模型训练

```bash
python main.py --mode train --data_dir data
```

训练完成后会生成：
- `checkpoints/best_model.pth`: 最佳模型
- `results/training_history.png`: 训练曲线
- `results/confusion_matrix.png`: 混淆矩阵

### 3. 实时推理

#### 摄像头推理
```bash
python main.py --mode inference --inference_mode camera
```

#### 视频文件推理
```bash
python main.py --mode inference --inference_mode video --input video.mp4 --output result.mp4
```

## 配置说明

主要配置在 `config.yaml` 中：

```yaml
# 数据配置
data:
  sequence_length: 30    # 时间序列长度
  num_classes: 4         # 意图类别数
  confidence_threshold: 0.3  # 置信度阈值

# 模型配置
model:
  pose_completion:
    hidden_size: 128     # 第一层GRU隐藏层大小
  intent_recognition:
    hidden_size: 64      # 第二层GRU隐藏层大小

# 训练配置
training:
  batch_size: 32
  learning_rate: 0.001
  num_epochs: 100
```

## 性能优化

### 提升精度
1. 增加训练数据量
2. 调整序列长度
3. 使用更大的模型
4. 改善数据质量

### 提升速度
1. 使用GPU加速
2. 减小批次大小
3. 使用更小的模型
4. 优化数据加载

### 减少内存使用
1. 减小批次大小
2. 减少序列长度
3. 使用梯度累积
4. 启用混合精度训练

## 常见问题

### Q: 训练时显存不足怎么办？
A: 减小 `batch_size` 或 `sequence_length`

### Q: 检测不到人体怎么办？
A: 调整YOLOv8的 `confidence` 阈值

### Q: 预测结果不稳定怎么办？
A: 增加 `sequence_length` 或改善训练数据

### Q: 如何添加新的意图类别？
A: 修改 `num_classes` 和 `class_names`，准备对应数据

## 代码示例

### 基本使用
```python
from src.inference import RealTimeInference
from src.data_processor import load_config

# 加载配置和模型
config = load_config('config.yaml')
inference = RealTimeInference('checkpoints/best_model.pth', config, device)

# 处理单帧
annotated_frame, info = inference.process_frame(frame)
print(f"预测意图: {info['predicted_class']}")
```

### 自定义训练
```python
from src.trainer import Trainer
from src.model import create_model

# 创建模型和训练器
model = create_model(config)
trainer = Trainer(model, config, device)

# 训练
history = trainer.train(train_loader, val_loader)
```

## 文件结构

```
├── main.py              # 主程序
├── demo.py              # 演示脚本
├── test_system.py       # 测试脚本
├── config.yaml          # 配置文件
├── src/                 # 源代码
│   ├── data_processor.py
│   ├── model.py
│   ├── trainer.py
│   └── inference.py
├── data/                # 数据目录
├── checkpoints/         # 模型保存
└── results/             # 结果输出
```

## 技术支持

1. 运行 `python test_system.py` 检查系统状态
2. 查看 `README.md` 获取详细文档
3. 运行 `python demo.py` 查看功能演示

## 下一步

1. 尝试使用自己的数据训练模型
2. 调整配置参数优化性能
3. 集成到您的机器人系统中
4. 根据需要扩展功能

祝您使用愉快！🚀

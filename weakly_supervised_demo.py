"""
弱监督学习示例 - 只需要视频级别的粗粒度标签
不需要逐帧标注，只需要知道视频包含什么类型的运动
"""

import cv2
import numpy as np
from ultralytics import YOLO
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import os
import math

class WeaklySupervisedLearner:
    """
    弱监督学习器
    只需要视频级别的标签，不需要逐帧标注
    """
    
    def __init__(self, window_size=15):
        self.window_size = window_size
        self.pose_model = YOLO('yolov8n-pose.pt')
        self.model = None
        self.scaler = None
        
        print("=== 弱监督学习器 ===")
        print("特点:")
        print("- ✅ 只需要视频级别标签")
        print("- ✅ 不需要逐帧标注")
        print("- ✅ 标注成本低")
        print("- ✅ 适合大规模数据")
    
    def extract_video_features(self, video_path):
        """
        从整个视频中提取特征序列
        """
        print(f"提取视频特征: {video_path}")
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return []
        
        all_features = []
        pose_history = []
        frame_count = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 姿态检测
            results = self.pose_model(frame, conf=0.3, verbose=False)
            
            if len(results) > 0 and results[0].keypoints is not None:
                keypoints_data = results[0].keypoints.data
                
                if len(keypoints_data) > 0:
                    keypoints = keypoints_data[0]
                    
                    if keypoints.shape[0] == 17:
                        xy = keypoints[:, :2].cpu().numpy()
                        conf = keypoints[:, 2].cpu().numpy()
                        
                        # 归一化
                        height, width = frame.shape[:2]
                        normalized_xy = xy.copy()
                        normalized_xy[:, 0] /= width
                        normalized_xy[:, 1] /= height
                        
                        pose_history.append(normalized_xy)
                        
                        # 滑动窗口提取特征
                        if len(pose_history) >= self.window_size:
                            features = self.calculate_motion_features(pose_history[-self.window_size:])
                            if features is not None:
                                all_features.append(features)
            
            if frame_count % 100 == 0:
                print(f"  处理进度: {frame_count}帧")
        
        cap.release()
        print(f"  提取特征: {len(all_features)}个")
        return np.array(all_features)
    
    def calculate_motion_features(self, pose_sequence):
        """
        计算运动特征
        """
        if len(pose_sequence) < self.window_size:
            return None
        
        features = []
        
        # 关键点：肩膀、髋部、脚踝
        key_points = [5, 6, 11, 12, 15, 16]
        
        for point_idx in key_points:
            trajectory = np.array([pose[point_idx] for pose in pose_sequence])
            
            # 速度特征
            velocities = np.diff(trajectory, axis=0)
            speed_magnitudes = [np.linalg.norm(v) for v in velocities]
            
            if speed_magnitudes:
                features.extend([
                    np.mean(speed_magnitudes),
                    np.std(speed_magnitudes),
                    np.max(speed_magnitudes),
                ])
            else:
                features.extend([0, 0, 0])
            
            # 方向变化特征
            if len(velocities) > 1:
                angles = []
                for v in velocities:
                    if np.linalg.norm(v) > 1e-6:
                        angles.append(math.atan2(v[1], v[0]))
                
                if len(angles) > 1:
                    angle_changes = []
                    for i in range(1, len(angles)):
                        change = angles[i] - angles[i-1]
                        # 处理角度环绕
                        while change > math.pi:
                            change -= 2 * math.pi
                        while change < -math.pi:
                            change += 2 * math.pi
                        angle_changes.append(change)
                    
                    if angle_changes:
                        features.extend([
                            np.mean(angle_changes),
                            np.std(angle_changes),
                            np.sum(np.abs(angle_changes)),
                        ])
                    else:
                        features.extend([0, 0, 0])
                else:
                    features.extend([0, 0, 0])
            else:
                features.extend([0, 0, 0])
        
        return np.array(features)
    
    def prepare_weakly_supervised_data(self):
        """
        准备弱监督训练数据
        只需要视频级别的标签
        """
        print("\n=== 准备弱监督训练数据 ===")
        
        # 视频级别的标签（粗粒度标签）
        video_labels = {
            'left.mp4': 'contains_left_turn',      # 包含左转
            'right.mp4': 'contains_right_turn',    # 包含右转
        }
        
        all_features = []
        all_labels = []
        
        for video_path, video_label in video_labels.items():
            if os.path.exists(video_path):
                print(f"处理视频: {video_path} -> {video_label}")
                
                # 提取视频特征
                features = self.extract_video_features(video_path)
                
                if len(features) > 0:
                    # 为该视频的所有特征分配相同的标签
                    video_label_encoded = self.encode_video_label(video_label)
                    labels = [video_label_encoded] * len(features)
                    
                    all_features.extend(features)
                    all_labels.extend(labels)
                    
                    print(f"  添加 {len(features)} 个特征，标签: {video_label}")
        
        print(f"\n总特征数量: {len(all_features)}")
        print(f"标签分布:")
        unique_labels, counts = np.unique(all_labels, return_counts=True)
        label_names = ['包含左转', '包含右转']
        for label, count in zip(unique_labels, counts):
            print(f"  {label_names[label]}: {count}个特征")
        
        return np.array(all_features), np.array(all_labels)
    
    def encode_video_label(self, video_label):
        """
        编码视频级别标签
        """
        label_mapping = {
            'contains_left_turn': 0,
            'contains_right_turn': 1,
        }
        return label_mapping[video_label]
    
    def train_weakly_supervised_model(self, features, labels):
        """
        训练弱监督模型
        """
        print("\n=== 训练弱监督模型 ===")
        
        # 特征标准化
        self.scaler = StandardScaler()
        features_scaled = self.scaler.fit_transform(features)
        
        # 划分训练测试集
        X_train, X_test, y_train, y_test = train_test_split(
            features_scaled, labels, test_size=0.2, random_state=42, stratify=labels
        )
        
        # 训练随机森林
        self.model = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            random_state=42,
            class_weight='balanced'
        )
        
        self.model.fit(X_train, y_train)
        
        # 评估模型
        train_score = self.model.score(X_train, y_train)
        test_score = self.model.score(X_test, y_test)
        
        print(f"训练准确率: {train_score:.3f}")
        print(f"测试准确率: {test_score:.3f}")
        
        return test_score
    
    def analyze_feature_importance(self):
        """
        分析特征重要性
        """
        if self.model is None:
            return
        
        print("\n=== 特征重要性分析 ===")
        
        importances = self.model.feature_importances_
        
        # 获取最重要的特征
        top_indices = np.argsort(importances)[-10:][::-1]
        
        print("最重要的10个特征:")
        for i, idx in enumerate(top_indices):
            print(f"  {i+1}. 特征 {idx}: {importances[idx]:.4f}")
    
    def predict_video_type(self, video_path):
        """
        预测视频类型
        """
        if self.model is None or self.scaler is None:
            print("模型未训练")
            return None
        
        print(f"\n预测视频类型: {video_path}")
        
        # 提取特征
        features = self.extract_video_features(video_path)
        
        if len(features) == 0:
            print("无法提取特征")
            return None
        
        # 标准化特征
        features_scaled = self.scaler.transform(features)
        
        # 预测
        predictions = self.model.predict(features_scaled)
        probabilities = self.model.predict_proba(features_scaled)
        
        # 统计预测结果
        unique_preds, counts = np.unique(predictions, return_counts=True)
        
        print("预测结果统计:")
        label_names = ['包含左转', '包含右转']
        for pred, count in zip(unique_preds, counts):
            percentage = count / len(predictions) * 100
            print(f"  {label_names[pred]}: {count}个片段 ({percentage:.1f}%)")
        
        # 整体预测（多数投票）
        overall_prediction = np.bincount(predictions).argmax()
        confidence = np.max(np.bincount(predictions)) / len(predictions)
        
        print(f"\n整体预测: {label_names[overall_prediction]}")
        print(f"置信度: {confidence:.1%}")
        
        return overall_prediction, confidence

def main():
    """
    弱监督学习演示
    """
    print("=== 弱监督学习演示 ===")
    print("特点: 只需要视频级别的粗粒度标签")
    print("标签示例:")
    print("- left.mp4 -> '包含左转'")
    print("- right.mp4 -> '包含右转'")
    print("- 不需要逐帧标注具体在哪一帧转向")
    
    # 创建弱监督学习器
    learner = WeaklySupervisedLearner(window_size=15)
    
    # 准备训练数据
    features, labels = learner.prepare_weakly_supervised_data()
    
    if len(features) > 100:
        # 训练模型
        accuracy = learner.train_weakly_supervised_model(features, labels)
        
        # 分析特征重要性
        learner.analyze_feature_importance()
        
        # 测试预测
        if os.path.exists('left.mp4'):
            learner.predict_video_type('left.mp4')
        
        if os.path.exists('right.mp4'):
            learner.predict_video_type('right.mp4')
        
        print(f"\n🎉 弱监督学习完成!")
        print(f"测试准确率: {accuracy:.3f}")
        print("优势:")
        print("- ✅ 标注成本低（只需视频级别标签）")
        print("- ✅ 适合大规模数据")
        print("- ✅ 实用性强")
        print("- ✅ 可扩展性好")
    else:
        print("❌ 训练数据不足")

if __name__ == '__main__':
    main()

# 人体姿态意图识别系统配置文件
project:
  name: "human_intent_recognition"
  version: "1.0.0"
  description: "基于双层GRU的人体姿态意图识别系统"

# 数据配置
data:
  pose_keypoints: 17  # COCO格式关键点数量
  sequence_length: 30  # 时间序列长度（帧数）
  input_dim: 2  # 坐标维度 (x, y)
  num_classes: 4  # 意图类别数量 [直行, 左转, 右转, 停止]
  confidence_threshold: 0.3  # 关键点置信度阈值
  
  # 数据集划分
  train_ratio: 0.7
  val_ratio: 0.15
  test_ratio: 0.15

# 模型配置
model:
  # 第一层GRU (姿态补全和特征提取)
  pose_completion:
    hidden_size: 128
    num_layers: 2
    dropout: 0.2
    bidirectional: true
  
  # 第二层GRU (意图识别)
  intent_recognition:
    hidden_size: 64
    num_layers: 2
    dropout: 0.3
    bidirectional: false
  
  # 注意力机制
  attention:
    enabled: true
    attention_dim: 64
  
  # 损失权重
  pose_loss_weight: 0.3
  intent_loss_weight: 1.0

# 训练配置
training:
  batch_size: 32
  learning_rate: 0.001
  num_epochs: 100
  weight_decay: 0.0001
  
  # 学习率调度器
  scheduler:
    type: "StepLR"
    step_size: 30
    gamma: 0.1
  
  # 早停
  early_stopping:
    patience: 15
    min_delta: 0.001
  
  # 梯度裁剪
  grad_clip_norm: 1.0

# YOLOv8配置
yolo:
  model_path: "yolov8n-pose.pt"
  confidence: 0.5
  device: "cuda"  # 或 "cpu"
  imgsz: 640

# 数据增强
augmentation:
  enabled: true
  horizontal_flip: true
  noise_scale: 0.02
  rotation_angle: 5  # 度

# 日志配置
logging:
  level: "INFO"
  save_dir: "logs"
  tensorboard_dir: "runs"
  
# 输出目录
output:
  model_dir: "checkpoints"
  results_dir: "results"
  
# 类别标签
class_names: ["直行", "左转", "右转", "停止"]

"""
English Intent Analyzer - Professional logic with English display
5 states: STRAIGHT, LEFT, RIGHT, TURN AROUND, STOP
"""

import cv2
import numpy as np
import os
from collections import deque
from ultralytics import <PERSON><PERSON><PERSON>

def analyze_video_english(video_path='left.mp4', output_path='left_english_analysis.mp4'):
    """
    Analyze video with English intent display
    """
    print(f"Starting English intent analysis: {video_path}")
    
    # Check video file
    if not os.path.exists(video_path):
        print(f"Error: Video file not found")
        return False
    
    # Load model
    print("Loading YOLOv8 model...")
    try:
        model = YOLO('yolov8n-pose.pt')
        print("✓ Model loaded successfully")
    except Exception as e:
        print(f"✗ Model loading failed: {e}")
        return False
    
    # Open video
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("Error: Cannot open video")
        return False
    
    # Video info
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"Video: {width}x{height}, {fps}FPS, {total_frames} frames")
    
    # Create output video
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    # Intent settings
    intent_names = ['STRAIGHT', 'LEFT', 'RIGHT', 'TURN AROUND', 'STOP']
    intent_colors = [
        (0, 255, 0),    # STRAIGHT - Green
        (255, 0, 0),    # LEFT - Blue
        (0, 0, 255),    # RIGHT - Red
        (255, 0, 255),  # TURN AROUND - Purple
        (0, 255, 255)   # STOP - Yellow
    ]
    
    # History buffers
    pose_history = deque(maxlen=15)
    angle_history = deque(maxlen=10)
    leg_motion_history = deque(maxlen=8)
    face_direction_history = deque(maxlen=5)
    intent_history = deque(maxlen=6)
    
    # Keypoint indices
    keypoints_idx = {
        'nose': 0, 'left_eye': 1, 'right_eye': 2, 'left_ear': 3, 'right_ear': 4,
        'left_shoulder': 5, 'right_shoulder': 6, 'left_elbow': 7, 'right_elbow': 8,
        'left_wrist': 9, 'right_wrist': 10, 'left_hip': 11, 'right_hip': 12,
        'left_knee': 13, 'right_knee': 14, 'left_ankle': 15, 'right_ankle': 16
    }
    
    frame_count = 0
    detection_count = 0
    intent_stats = {name: 0 for name in intent_names}
    intent_stats['NOT_DETECTED'] = 0
    
    print("Processing frames...")
    
    def calculate_angle_change(history_data, key):
        """Calculate angle change from history"""
        if len(history_data) < 3:
            return 0
        
        changes = []
        for i in range(1, len(history_data)):
            if key in history_data[i-1] and key in history_data[i]:
                prev_angle = history_data[i-1][key]
                curr_angle = history_data[i][key]
                diff = curr_angle - prev_angle
                # Handle angle wrapping
                if diff > 180:
                    diff -= 360
                elif diff < -180:
                    diff += 360
                changes.append(diff)
        
        return np.mean(changes) if changes else 0
    
    def analyze_intent(pose_hist, angle_hist, leg_hist, face_hist):
        """Analyze intent based on all data"""
        if len(pose_hist) < 5:
            return None, 0.0
        
        # 1. Check if standing (based on leg motion)
        if len(leg_hist) >= 3:
            leg_motion_low = True
            for i in range(1, len(leg_hist)):
                prev_data = leg_hist[i-1]
                curr_data = leg_hist[i]
                
                # Check knee movement
                if 'left_knee_y' in prev_data and 'left_knee_y' in curr_data:
                    if abs(curr_data['left_knee_y'] - prev_data['left_knee_y']) > 8:
                        leg_motion_low = False
                        break
                
                if 'right_knee_y' in prev_data and 'right_knee_y' in curr_data:
                    if abs(curr_data['right_knee_y'] - prev_data['right_knee_y']) > 8:
                        leg_motion_low = False
                        break
            
            if leg_motion_low:
                return 4, 0.9  # STOP
        
        # 2. Check for turn around (large angle changes + face direction change)
        if len(angle_hist) >= 5:
            shoulder_change = abs(calculate_angle_change(angle_hist, 'shoulder_angle'))
            body_change = abs(calculate_angle_change(angle_hist, 'body_angle'))
            
            # Check face direction change
            face_changed = False
            if len(face_hist) >= 3:
                if 'front' in face_hist and 'back' in face_hist:
                    face_changed = True
            
            if shoulder_change > 25 or body_change > 20 or face_changed:
                return 3, 0.85  # TURN AROUND
        
        # 3. Check turn direction
        if len(angle_hist) >= 4:
            shoulder_trend = calculate_angle_change(angle_hist, 'shoulder_angle')
            hip_trend = calculate_angle_change(angle_hist, 'hip_angle')
            total_trend = shoulder_trend + hip_trend
            
            if total_trend > 12:
                return 2, 0.8  # RIGHT
            elif total_trend < -12:
                return 1, 0.8  # LEFT
        
        # 4. Default to straight
        return 0, 0.7  # STRAIGHT
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # Pose detection
            results = model(frame, conf=0.3, verbose=False)
            
            annotated_frame = frame.copy()
            
            current_intent = None
            intent_confidence = 0.0
            person_detected = False
            visible_keypoints = 0
            
            # Process detection results
            if len(results) > 0 and results[0].keypoints is not None:
                keypoints_data = results[0].keypoints.data
                
                if len(keypoints_data) > 0:
                    person_detected = True
                    detection_count += 1
                    
                    keypoints = keypoints_data[0]
                    if keypoints.shape[0] == 17:
                        xy = keypoints[:, :2].cpu().numpy()
                        conf = keypoints[:, 2].cpu().numpy()
                        
                        visible_keypoints = np.sum(conf > 0.3)
                        
                        # Add to pose history
                        pose_history.append(xy.copy())
                        
                        # Analyze body angles
                        angles = {}
                        if (conf[keypoints_idx['left_shoulder']] > 0.4 and 
                            conf[keypoints_idx['right_shoulder']] > 0.4 and
                            conf[keypoints_idx['left_hip']] > 0.4 and 
                            conf[keypoints_idx['right_hip']] > 0.4):
                            
                            left_shoulder = xy[keypoints_idx['left_shoulder']]
                            right_shoulder = xy[keypoints_idx['right_shoulder']]
                            left_hip = xy[keypoints_idx['left_hip']]
                            right_hip = xy[keypoints_idx['right_hip']]
                            
                            # Shoulder angle
                            shoulder_vector = right_shoulder - left_shoulder
                            shoulder_angle = np.degrees(np.arctan2(shoulder_vector[1], shoulder_vector[0]))
                            angles['shoulder_angle'] = shoulder_angle
                            
                            # Hip angle
                            hip_vector = right_hip - left_hip
                            hip_angle = np.degrees(np.arctan2(hip_vector[1], hip_vector[0]))
                            angles['hip_angle'] = hip_angle
                            
                            # Body angle
                            shoulder_center = (left_shoulder + right_shoulder) / 2
                            hip_center = (left_hip + right_hip) / 2
                            body_vector = hip_center - shoulder_center
                            body_angle = np.degrees(np.arctan2(body_vector[1], body_vector[0]))
                            angles['body_angle'] = body_angle
                        
                        if angles:
                            angle_history.append(angles)
                        
                        # Analyze leg motion
                        leg_motion = {}
                        if conf[keypoints_idx['left_knee']] > 0.4:
                            leg_motion['left_knee_y'] = xy[keypoints_idx['left_knee']][1]
                        if conf[keypoints_idx['right_knee']] > 0.4:
                            leg_motion['right_knee_y'] = xy[keypoints_idx['right_knee']][1]
                        
                        if leg_motion:
                            leg_motion_history.append(leg_motion)
                        
                        # Analyze face direction
                        face_direction = None
                        nose_visible = conf[keypoints_idx['nose']] > 0.4
                        left_eye_visible = conf[keypoints_idx['left_eye']] > 0.4
                        right_eye_visible = conf[keypoints_idx['right_eye']] > 0.4
                        
                        if nose_visible and left_eye_visible and right_eye_visible:
                            face_direction = 'front'
                        elif nose_visible and (left_eye_visible or right_eye_visible):
                            face_direction = 'side'
                        else:
                            face_direction = 'back'
                        
                        if face_direction:
                            face_direction_history.append(face_direction)
                        
                        # Determine intent
                        intent_id, confidence_score = analyze_intent(
                            pose_history, angle_history, leg_motion_history, face_direction_history
                        )
                        
                        if intent_id is not None:
                            intent_history.append(intent_id)
                            
                            # Smooth prediction with voting
                            if len(intent_history) >= 4:
                                intent_counts = np.bincount(list(intent_history), minlength=5)
                                current_intent = np.argmax(intent_counts)
                                intent_confidence = confidence_score
                        
                        # Draw keypoints
                        for i, (x, y) in enumerate(xy):
                            if conf[i] > 0.3:
                                color = (0, 255, 0) if conf[i] > 0.7 else (0, 255, 255)
                                cv2.circle(annotated_frame, (int(x), int(y)), 6, color, -1)
                        
                        # Draw skeleton
                        connections = [
                            (5, 6), (5, 7), (7, 9), (6, 8), (8, 10),
                            (5, 11), (6, 12), (11, 12),
                            (11, 13), (13, 15), (12, 14), (14, 16)
                        ]
                        
                        for start_idx, end_idx in connections:
                            if conf[start_idx] > 0.3 and conf[end_idx] > 0.3:
                                start_point = (int(xy[start_idx][0]), int(xy[start_idx][1]))
                                end_point = (int(xy[end_idx][0]), int(xy[end_idx][1]))
                                cv2.line(annotated_frame, start_point, end_point, (255, 0, 0), 3)
            
            # Update statistics
            if current_intent is not None:
                intent_stats[intent_names[current_intent]] += 1
            else:
                intent_stats['NOT_DETECTED'] += 1
            
            # Draw info panel
            panel_width = 650
            panel_height = 180
            
            # Semi-transparent background
            overlay = annotated_frame.copy()
            cv2.rectangle(overlay, (10, 10), (panel_width, panel_height), (0, 0, 0), -1)
            cv2.addWeighted(overlay, 0.8, annotated_frame, 0.2, 0, annotated_frame)
            
            # White border
            cv2.rectangle(annotated_frame, (10, 10), (panel_width, panel_height), (255, 255, 255), 3)
            
            # Display info
            y = 45
            line_height = 32
            
            # Frame info
            cv2.putText(annotated_frame, f'Frame: {frame_count}/{total_frames} ({frame_count/fps:.1f}s)', 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
            y += line_height
            
            # Detection status
            if person_detected:
                cv2.putText(annotated_frame, f'Person: DETECTED ({visible_keypoints}/17 points)', 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
                y += line_height
                
                # Intent display
                if current_intent is not None:
                    intent_text = f'INTENT: {intent_names[current_intent]}'
                    confidence_text = f'Confidence: {intent_confidence:.2f}'
                    intent_color = intent_colors[current_intent]
                    
                    # Display intent
                    cv2.putText(annotated_frame, intent_text, 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.2, intent_color, 3)
                    y += line_height
                    
                    # Display confidence
                    cv2.putText(annotated_frame, confidence_text, 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, intent_color, 2)
                    
                    # Large display in top-right corner
                    intent_display = intent_names[current_intent]
                    text_size = cv2.getTextSize(intent_display, cv2.FONT_HERSHEY_SIMPLEX, 2.2, 4)[0]
                    
                    # Background rectangle
                    cv2.rectangle(annotated_frame, 
                                 (width - text_size[0] - 40, 20), 
                                 (width - 20, 90), 
                                 intent_color, -1)
                    
                    # White border
                    cv2.rectangle(annotated_frame, 
                                 (width - text_size[0] - 40, 20), 
                                 (width - 20, 90), 
                                 (255, 255, 255), 3)
                    
                    # Intent text
                    cv2.putText(annotated_frame, intent_display, 
                               (width - text_size[0] - 30, 65), 
                               cv2.FONT_HERSHEY_SIMPLEX, 2.2, (255, 255, 255), 4)
                else:
                    cv2.putText(annotated_frame, 'INTENT: ANALYZING...', 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (128, 128, 128), 3)
            else:
                cv2.putText(annotated_frame, 'Person: NOT DETECTED', 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
            
            # Write frame
            writer.write(annotated_frame)
            
            # Show progress
            if frame_count % 100 == 0:
                progress = frame_count / total_frames * 100
                current_intent_name = intent_names[current_intent] if current_intent is not None else 'NOT_DETECTED'
                print(f"Progress: {progress:.1f}% - Current intent: {current_intent_name}")
    
    except Exception as e:
        print(f"Processing error: {e}")
        return False
    
    finally:
        cap.release()
        writer.release()
    
    print(f"\n=== English Analysis Complete ===")
    print(f"Total frames: {frame_count}")
    print(f"Detection frames: {detection_count}")
    print(f"Detection rate: {detection_count/frame_count*100:.1f}%")
    print(f"\nIntent distribution:")
    for intent, count in intent_stats.items():
        percentage = count / frame_count * 100 if frame_count > 0 else 0
        print(f"  {intent}: {count} frames ({percentage:.1f}%)")
    print(f"Output video: {output_path}")
    
    return True

if __name__ == '__main__':
    success = analyze_video_english()
    if success:
        print("\n🎉 English intent analysis completed!")
        print("Please check left_english_analysis.mp4")
    else:
        print("\n❌ English intent analysis failed!")

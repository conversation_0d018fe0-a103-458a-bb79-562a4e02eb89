"""
测试模型修复
"""

import torch
import yaml
import numpy as np

def load_config(config_path: str):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def test_model():
    """测试模型"""
    print("测试模型修复...")
    
    # 加载配置
    config = load_config('config.yaml')
    
    # 创建模型
    from src.model import create_model
    model = create_model(config)
    
    # 创建测试输入
    batch_size = 2
    seq_len = config['data']['sequence_length']
    num_keypoints = config['data']['pose_keypoints']
    
    # 输入: (batch_size, seq_len, num_keypoints, 6)
    dummy_input = torch.randn(batch_size, seq_len, num_keypoints, 6)
    
    print(f"输入形状: {dummy_input.shape}")
    
    # 测试前向传播
    model.eval()
    with torch.no_grad():
        outputs = model(dummy_input)
    
    print("模型输出:")
    for key, value in outputs.items():
        if isinstance(value, torch.Tensor):
            print(f"  {key}: {value.shape}")
    
    # 测试损失计算
    dummy_targets = torch.randint(0, config['data']['num_classes'], (batch_size, 1))
    losses = model.compute_loss(outputs, dummy_targets, dummy_input)
    
    print("损失:")
    for key, value in losses.items():
        print(f"  {key}: {value.item():.4f}")
    
    # 测试预测
    predicted_class, confidence = model.predict_intent(dummy_input)
    print(f"预测类别: {predicted_class}")
    print(f"预测置信度: {confidence}")
    
    print("✓ 模型测试通过!")

if __name__ == '__main__':
    test_model()

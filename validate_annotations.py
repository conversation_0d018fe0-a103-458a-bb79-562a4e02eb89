"""
验证标注数据的有效性
确保数据可以被深度学习模型正确使用
"""

import json
import numpy as np
import cv2
import os
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt

class PoseIntentDataset(Dataset):
    """
    姿态意图数据集类 - 用于验证数据格式
    """
    def __init__(self, annotation_files, image_dir=None):
        self.annotations = []
        self.images = []
        
        # 加载所有标注文件
        for ann_file in annotation_files:
            with open(ann_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.annotations.extend(data['annotations'])
                self.images.extend(data['images'])
        
        self.image_dir = image_dir or os.path.dirname(annotation_files[0])
        
        print(f"加载了 {len(self.annotations)} 个标注样本")
    
    def __len__(self):
        return len(self.annotations)
    
    def __getitem__(self, idx):
        annotation = self.annotations[idx]
        image_info = self.images[idx]
        
        # 提取关键点 (17个点，每个点3个值: x, y, visibility)
        keypoints = np.array(annotation['keypoints']).reshape(17, 3)
        
        # 归一化关键点坐标
        keypoints_normalized = keypoints.copy()
        keypoints_normalized[:, 0] /= image_info['width']   # x坐标归一化
        keypoints_normalized[:, 1] /= image_info['height']  # y坐标归一化
        
        # 只保留可见的关键点
        visible_mask = keypoints[:, 2] > 0
        
        # 提取意图标签
        intent_label = annotation.get('intent_label', image_info.get('intent_label', 0))
        
        return {
            'keypoints': torch.FloatTensor(keypoints_normalized[:, :2]),  # (17, 2)
            'visibility': torch.FloatTensor(keypoints[:, 2]),            # (17,)
            'intent_label': torch.LongTensor([intent_label]),            # (1,)
            'visible_mask': torch.BoolTensor(visible_mask),              # (17,)
            'num_visible': torch.sum(torch.BoolTensor(visible_mask)),    # 标量
            'image_id': image_info['id'],
            'file_name': image_info['file_name']
        }

class SimpleIntentClassifier(nn.Module):
    """
    简单的意图分类网络 - 用于验证数据
    """
    def __init__(self, input_dim=34, hidden_dim=128, num_classes=5):
        super(SimpleIntentClassifier, self).__init__()
        
        self.fc1 = nn.Linear(input_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, hidden_dim)
        self.fc3 = nn.Linear(hidden_dim, num_classes)
        self.dropout = nn.Dropout(0.3)
        self.relu = nn.ReLU()
        
    def forward(self, keypoints, visibility):
        # 将关键点坐标和可见性拼接
        x = torch.cat([keypoints.flatten(1), visibility], dim=1)  # (batch, 34+17=51)
        
        x = self.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        
        return x

def validate_annotation_format(annotation_file):
    """
    验证标注文件格式
    """
    print(f"\n=== 验证标注格式: {annotation_file} ===")
    
    try:
        with open(annotation_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 检查必要字段
        required_fields = ['images', 'annotations', 'categories']
        for field in required_fields:
            if field not in data:
                print(f"❌ 缺少必要字段: {field}")
                return False
            else:
                print(f"✅ 包含字段: {field}")
        
        # 检查图片信息
        if len(data['images']) > 0:
            img = data['images'][0]
            img_required = ['id', 'width', 'height', 'file_name']
            for field in img_required:
                if field in img:
                    print(f"✅ 图片信息包含: {field}")
                else:
                    print(f"❌ 图片信息缺少: {field}")
            
            # 检查意图标签
            if 'intent_label' in img:
                print(f"✅ 包含意图标签: {img['intent_label']}")
            else:
                print(f"⚠️  图片信息中没有意图标签")
        
        # 检查标注信息
        if len(data['annotations']) > 0:
            ann = data['annotations'][0]
            ann_required = ['keypoints', 'num_keypoints']
            for field in ann_required:
                if field in ann:
                    print(f"✅ 标注信息包含: {field}")
                else:
                    print(f"❌ 标注信息缺少: {field}")
            
            # 检查关键点格式
            if 'keypoints' in ann:
                keypoints = ann['keypoints']
                if len(keypoints) == 51:  # 17个点 × 3个值
                    print(f"✅ 关键点数量正确: {len(keypoints)//3} 个点")
                    
                    # 检查可见关键点数量
                    visible_count = sum(1 for i in range(2, len(keypoints), 3) if keypoints[i] > 0)
                    print(f"✅ 可见关键点: {visible_count} 个")
                else:
                    print(f"❌ 关键点数量错误: 期望51个值，实际{len(keypoints)}个")
            
            # 检查意图标签
            if 'intent_label' in ann:
                print(f"✅ 标注包含意图标签: {ann['intent_label']}")
            else:
                print(f"⚠️  标注中没有意图标签")
        
        print("✅ 标注格式验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 标注格式验证失败: {e}")
        return False

def validate_data_loading(annotation_files):
    """
    验证数据加载
    """
    print(f"\n=== 验证数据加载 ===")
    
    try:
        # 创建数据集
        dataset = PoseIntentDataset(annotation_files)
        dataloader = DataLoader(dataset, batch_size=2, shuffle=True)
        
        print(f"✅ 数据集创建成功，包含 {len(dataset)} 个样本")
        
        # 测试数据加载
        for i, batch in enumerate(dataloader):
            print(f"✅ 批次 {i+1}:")
            print(f"   关键点形状: {batch['keypoints'].shape}")
            print(f"   可见性形状: {batch['visibility'].shape}")
            print(f"   意图标签形状: {batch['intent_label'].shape}")
            print(f"   意图标签值: {batch['intent_label'].numpy().flatten()}")
            print(f"   可见关键点数: {batch['num_visible'].numpy()}")
            
            if i >= 2:  # 只测试前3个批次
                break
        
        print("✅ 数据加载验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据加载验证失败: {e}")
        return False

def validate_model_training(annotation_files):
    """
    验证模型训练
    """
    print(f"\n=== 验证模型训练 ===")
    
    try:
        # 创建数据集和数据加载器
        dataset = PoseIntentDataset(annotation_files)
        dataloader = DataLoader(dataset, batch_size=2, shuffle=True)
        
        # 创建模型
        model = SimpleIntentClassifier(input_dim=34, hidden_dim=64, num_classes=5)
        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        
        print("✅ 模型创建成功")
        
        # 测试前向传播
        model.train()
        total_loss = 0
        num_batches = 0
        
        for i, batch in enumerate(dataloader):
            keypoints = batch['keypoints']
            visibility = batch['visibility']
            labels = batch['intent_label'].squeeze()
            
            # 前向传播
            outputs = model(keypoints, visibility)
            loss = criterion(outputs, labels)
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            print(f"✅ 批次 {i+1}: 损失 = {loss.item():.4f}")
            
            if i >= 4:  # 只训练5个批次
                break
        
        avg_loss = total_loss / num_batches
        print(f"✅ 平均损失: {avg_loss:.4f}")
        
        # 测试推理
        model.eval()
        with torch.no_grad():
            sample_batch = next(iter(dataloader))
            outputs = model(sample_batch['keypoints'], sample_batch['visibility'])
            predictions = torch.argmax(outputs, dim=1)
            
            print(f"✅ 推理测试:")
            print(f"   预测结果: {predictions.numpy()}")
            print(f"   真实标签: {sample_batch['intent_label'].squeeze().numpy()}")
        
        print("✅ 模型训练验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 模型训练验证失败: {e}")
        return False

def visualize_annotation_validation(annotation_file, image_path=None):
    """
    可视化验证单个标注文件
    """
    print(f"\n=== 可视化验证标注: {annotation_file} ===")

    try:
        # 加载标注数据
        with open(annotation_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        if not data['annotations'] or not data['images']:
            print("❌ 标注文件为空")
            return False

        annotation = data['annotations'][0]
        image_info = data['images'][0]

        # 查找图片文件
        if image_path is None:
            # 尝试在不同目录中查找图片
            possible_paths = [
                image_info['file_name'],
                os.path.join(os.path.dirname(annotation_file), image_info['file_name']),
                os.path.join('.', image_info['file_name'])
            ]

            for path in possible_paths:
                if os.path.exists(path):
                    image_path = path
                    break

        if image_path is None or not os.path.exists(image_path):
            print(f"❌ 找不到图片文件: {image_info['file_name']}")
            return False

        # 读取图片
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ 无法读取图片: {image_path}")
            return False

        # 创建验证可视化
        vis_image = create_validation_visualization(image, annotation, image_info)

        # 保存验证结果
        output_dir = "validation_output"
        os.makedirs(output_dir, exist_ok=True)

        base_name = os.path.splitext(os.path.basename(image_path))[0]
        output_path = os.path.join(output_dir, f"{base_name}_validation.jpg")

        cv2.imwrite(output_path, vis_image)
        print(f"✅ 验证可视化已保存: {output_path}")

        # 打印详细信息
        print_annotation_details(annotation, image_info)

        return True

    except Exception as e:
        print(f"❌ 可视化验证失败: {e}")
        return False

def create_validation_visualization(image, annotation, image_info):
    """
    创建验证可视化图片
    """
    vis_image = image.copy()
    height, width = image.shape[:2]

    # 意图颜色和名称
    intent_colors = {
        0: (0, 255, 0),    # 直行 - 绿色
        1: (255, 0, 0),    # 左转 - 蓝色
        2: (0, 0, 255),    # 右转 - 红色
        3: (255, 0, 255),  # 转身 - 紫色
        4: (0, 255, 255)   # 停止 - 黄色
    }

    intent_names = {0: "STRAIGHT", 1: "LEFT", 2: "RIGHT", 3: "TURN_AROUND", 4: "STOP"}
    intent_chinese = {0: "直行", 1: "左转", 2: "右转", 3: "转身", 4: "停止"}

    # 获取意图标签
    intent_label = annotation.get('intent_label', image_info.get('intent_label', 0))
    intent_color = intent_colors.get(intent_label, (255, 255, 255))

    # 解析关键点
    keypoints = np.array(annotation['keypoints']).reshape(17, 3)

    # 关键点名称
    keypoint_names = [
        "nose", "left_eye", "right_eye", "left_ear", "right_ear",
        "left_shoulder", "right_shoulder", "left_elbow", "right_elbow",
        "left_wrist", "right_wrist", "left_hip", "right_hip",
        "left_knee", "right_knee", "left_ankle", "right_ankle"
    ]

    # 绘制关键点
    visible_points = []
    for i, (x, y, v) in enumerate(keypoints):
        if v > 0:  # 可见
            visible_points.append((x, y))
            # 根据可见性选择颜色
            if v == 2:
                color = (0, 255, 0)  # 完全可见 - 绿色
            else:
                color = (0, 255, 255)  # 部分遮挡 - 黄色

            # 绘制关键点
            cv2.circle(vis_image, (int(x), int(y)), 8, color, -1)
            cv2.circle(vis_image, (int(x), int(y)), 8, (255, 255, 255), 2)

            # 添加关键点编号
            cv2.putText(vis_image, str(i), (int(x)+10, int(y)),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)

    # 绘制骨架连接
    skeleton = [
        (0, 1), (0, 2), (1, 3), (2, 4),  # 头部
        (3, 5), (4, 6),  # 耳朵到肩膀
        (5, 6),          # 肩膀连接
        (5, 7), (7, 9),  # 左臂
        (6, 8), (8, 10), # 右臂
        (5, 11), (6, 12), # 肩膀到髋部
        (11, 12),         # 髋部连接
        (11, 13), (13, 15), # 左腿
        (12, 14), (14, 16)  # 右腿
    ]

    for start_idx, end_idx in skeleton:
        if keypoints[start_idx, 2] > 0 and keypoints[end_idx, 2] > 0:
            start_point = (int(keypoints[start_idx, 0]), int(keypoints[start_idx, 1]))
            end_point = (int(keypoints[end_idx, 0]), int(keypoints[end_idx, 1]))
            cv2.line(vis_image, start_point, end_point, intent_color, 3)

    # 绘制边界框
    if 'bbox' in annotation:
        bbox = annotation['bbox']
        x, y, w, h = bbox
        cv2.rectangle(vis_image, (int(x), int(y)), (int(x+w), int(y+h)), intent_color, 3)

    # 添加信息面板
    panel_height = 200
    panel_width = 600

    # 半透明背景
    overlay = vis_image.copy()
    cv2.rectangle(overlay, (10, 10), (panel_width, panel_height), (0, 0, 0), -1)
    cv2.addWeighted(overlay, 0.8, vis_image, 0.2, 0, vis_image)

    # 白色边框
    cv2.rectangle(vis_image, (10, 10), (panel_width, panel_height), (255, 255, 255), 3)

    # 显示验证信息
    y = 45
    line_height = 30

    # 文件信息
    cv2.putText(vis_image, f"File: {image_info['file_name']}",
               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    y += line_height

    # 图片尺寸
    cv2.putText(vis_image, f"Size: {image_info['width']} x {image_info['height']}",
               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    y += line_height

    # 意图标签
    intent_text = f"Intent: {intent_chinese[intent_label]} ({intent_names[intent_label]})"
    cv2.putText(vis_image, intent_text,
               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.9, intent_color, 3)
    y += line_height

    # 关键点统计
    visible_count = np.sum(keypoints[:, 2] > 0)
    cv2.putText(vis_image, f"Visible Keypoints: {visible_count}/17",
               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    y += line_height

    # 数据格式验证状态
    cv2.putText(vis_image, "Data Format: VALID",
               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

    # 右上角大字显示意图
    intent_display = intent_names[intent_label]
    text_size = cv2.getTextSize(intent_display, cv2.FONT_HERSHEY_SIMPLEX, 2.0, 4)[0]

    # 背景矩形
    cv2.rectangle(vis_image,
                 (width - text_size[0] - 40, 20),
                 (width - 20, 80),
                 intent_color, -1)

    # 白色边框
    cv2.rectangle(vis_image,
                 (width - text_size[0] - 40, 20),
                 (width - 20, 80),
                 (255, 255, 255), 3)

    # 意图文字
    cv2.putText(vis_image, intent_display,
               (width - text_size[0] - 30, 60),
               cv2.FONT_HERSHEY_SIMPLEX, 2.0, (255, 255, 255), 4)

    return vis_image

def analyze_back_view_keypoints(keypoints):
    """
    分析背部视角关键点的质量
    """
    keypoints_array = np.array(keypoints).reshape(17, 3)

    # 关键点分组
    face_points = [0, 1, 2, 3, 4]  # 面部
    upper_body = [5, 6, 7, 8, 9, 10]  # 上身
    lower_body = [11, 12, 13, 14, 15, 16]  # 下身

    # 分析各部分可见性
    face_visible = np.sum(keypoints_array[face_points, 2] > 0)
    upper_visible = np.sum(keypoints_array[upper_body, 2] > 0)
    lower_visible = np.sum(keypoints_array[lower_body, 2] > 0)

    # 背部视角质量评估
    quality_score = 0
    quality_notes = []

    # 面部关键点（背部视角通常不可见）
    if face_visible >= 2:
        quality_score += 10
        quality_notes.append("✅ 面部关键点部分可见（可能是侧面或转身）")
    elif face_visible == 0:
        quality_score += 5
        quality_notes.append("⚠️ 面部关键点不可见（典型背部视角）")

    # 上身关键点（重要）
    if upper_visible >= 5:
        quality_score += 30
        quality_notes.append("✅ 上身关键点完整")
    elif upper_visible >= 3:
        quality_score += 20
        quality_notes.append("⚠️ 上身关键点部分缺失")
    else:
        quality_notes.append("❌ 上身关键点严重缺失")

    # 下身关键点（最重要）
    if lower_visible >= 5:
        quality_score += 40
        quality_notes.append("✅ 下身关键点完整")
    elif lower_visible >= 3:
        quality_score += 25
        quality_notes.append("⚠️ 下身关键点部分缺失")
    else:
        quality_notes.append("❌ 下身关键点严重缺失")

    # 意图识别能力评估
    intent_capability = {}

    # 直行/停止判断能力
    if lower_visible >= 4:
        intent_capability["直行/停止"] = "✅ 优秀"
    elif lower_visible >= 2:
        intent_capability["直行/停止"] = "⚠️ 一般"
    else:
        intent_capability["直行/停止"] = "❌ 困难"

    # 左右转判断能力
    if upper_visible >= 4 and lower_visible >= 4:
        intent_capability["左右转"] = "✅ 优秀"
    elif upper_visible >= 2 and lower_visible >= 2:
        intent_capability["左右转"] = "⚠️ 一般"
    else:
        intent_capability["左右转"] = "❌ 困难"

    # 转身判断能力
    if face_visible >= 2:
        intent_capability["转身"] = "✅ 优秀"
    elif upper_visible >= 4:
        intent_capability["转身"] = "⚠️ 一般"
    else:
        intent_capability["转身"] = "❌ 困难"

    return {
        "quality_score": quality_score,
        "quality_notes": quality_notes,
        "intent_capability": intent_capability,
        "face_visible": face_visible,
        "upper_visible": upper_visible,
        "lower_visible": lower_visible
    }

def print_annotation_details(annotation, image_info):
    """
    打印详细的标注信息（增强背部视角分析）
    """
    print("\n📊 标注详细信息:")

    # 基本信息
    intent_label = annotation.get('intent_label', image_info.get('intent_label', 0))
    intent_names = {0: "直行", 1: "左转", 2: "右转", 3: "转身", 4: "停止"}

    print(f"  图片文件: {image_info['file_name']}")
    print(f"  图片尺寸: {image_info['width']} x {image_info['height']}")
    print(f"  意图标签: {intent_label} ({intent_names.get(intent_label, '未知')})")

    # 关键点分析
    keypoints = np.array(annotation['keypoints']).reshape(17, 3)
    visible_count = np.sum(keypoints[:, 2] > 0)
    fully_visible = np.sum(keypoints[:, 2] == 2)
    partially_visible = np.sum(keypoints[:, 2] == 1)

    print(f"  关键点统计:")
    print(f"    总数: 17 ✅")
    print(f"    可见: {visible_count}")
    print(f"    完全可见: {fully_visible}")
    print(f"    部分遮挡: {partially_visible}")
    print(f"    不可见: {17 - visible_count}")

    # 背部视角专项分析
    back_view_analysis = analyze_back_view_keypoints(annotation['keypoints'])

    print(f"\n  🎯 背部视角质量分析:")
    print(f"    质量分数: {back_view_analysis['quality_score']}/80")

    print(f"    关键点分布:")
    print(f"      面部: {back_view_analysis['face_visible']}/5")
    print(f"      上身: {back_view_analysis['upper_visible']}/6")
    print(f"      下身: {back_view_analysis['lower_visible']}/6")

    print(f"    质量评估:")
    for note in back_view_analysis['quality_notes']:
        print(f"      {note}")

    print(f"    意图识别能力:")
    for intent_type, capability in back_view_analysis['intent_capability'].items():
        print(f"      {intent_type}: {capability}")

    # 边界框信息
    if 'bbox' in annotation:
        bbox = annotation['bbox']
        print(f"  边界框: [{bbox[0]:.1f}, {bbox[1]:.1f}, {bbox[2]:.1f}, {bbox[3]:.1f}]")
        print(f"  面积: {annotation.get('area', 0):.1f} 像素²")

    # 数据完整性检查
    print(f"  数据完整性:")
    print(f"    关键点数据长度: {len(annotation['keypoints'])} (期望: 51) ✅")
    print(f"    包含意图标签: {'✅ 是' if 'intent_label' in annotation else '❌ 否'}")
    print(f"    包含边界框: {'✅ 是' if 'bbox' in annotation else '❌ 否'}")

    # 训练适用性评估
    print(f"  🚀 训练适用性:")
    if back_view_analysis['quality_score'] >= 60:
        print(f"    ✅ 优秀 - 适合训练深度学习模型")
    elif back_view_analysis['quality_score'] >= 40:
        print(f"    ⚠️ 良好 - 可用于训练，建议增加数据增强")
    else:
        print(f"    ❌ 较差 - 建议重新标注或使用其他角度")

    # 针对当前意图的建议
    current_intent = intent_names.get(intent_label, '未知')
    current_capability = back_view_analysis['intent_capability'].get(
        current_intent if current_intent in ['直行/停止', '左右转', '转身'] else '左右转',
        "⚠️ 一般"
    )
    print(f"    当前意图({current_intent})识别能力: {current_capability}")

def visualize_data_distribution(annotation_files):
    """
    可视化数据分布
    """
    print(f"\n=== 数据分布分析 ===")

    intent_counts = {}
    visible_keypoints_stats = []

    for ann_file in annotation_files:
        with open(ann_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        for ann in data['annotations']:
            # 统计意图分布
            intent_label = ann.get('intent_label', data['images'][0].get('intent_label', 0))
            intent_counts[intent_label] = intent_counts.get(intent_label, 0) + 1

            # 统计可见关键点
            keypoints = np.array(ann['keypoints']).reshape(17, 3)
            visible_count = np.sum(keypoints[:, 2] > 0)
            visible_keypoints_stats.append(visible_count)

    # 打印统计结果
    print("意图标签分布:")
    intent_names = {0: "直行", 1: "左转", 2: "右转", 3: "转身", 4: "停止"}
    for intent_id, count in intent_counts.items():
        intent_name = intent_names.get(intent_id, f"未知({intent_id})")
        print(f"  {intent_name}: {count} 个样本")

    print(f"\n可见关键点统计:")
    print(f"  平均可见关键点: {np.mean(visible_keypoints_stats):.1f}")
    print(f"  最少可见关键点: {np.min(visible_keypoints_stats)}")
    print(f"  最多可见关键点: {np.max(visible_keypoints_stats)}")

    return True

def main():
    """
    主验证函数
    """
    print("🔍 标注数据验证工具")
    print("=" * 50)

    # 查找标注文件
    annotation_files = []

    # 检查不同目录中的标注文件
    search_dirs = ['annotation_output', 'intent_annotations', 'intent_annotations_fixed', '.']

    for search_dir in search_dirs:
        if os.path.exists(search_dir):
            for file in os.listdir(search_dir):
                if file.endswith('.json') and 'keypoints' in file:
                    annotation_files.append(os.path.join(search_dir, file))

    if not annotation_files:
        print("❌ 未找到标注文件")
        return

    print(f"找到 {len(annotation_files)} 个标注文件:")
    for file in annotation_files:
        print(f"  - {file}")

    # 执行验证
    all_passed = True

    # 1. 验证格式
    for ann_file in annotation_files:
        if not validate_annotation_format(ann_file):
            all_passed = False

    # 2. 可视化验证每个标注文件
    print(f"\n=== 可视化验证 ===")
    for ann_file in annotation_files:
        if not visualize_annotation_validation(ann_file):
            all_passed = False

    # 3. 验证数据加载
    if not validate_data_loading(annotation_files):
        all_passed = False

    # 4. 验证模型训练
    if not validate_model_training(annotation_files):
        all_passed = False

    # 5. 数据分布分析
    visualize_data_distribution(annotation_files)

    # 总结
    print(f"\n{'='*50}")
    if all_passed:
        print("🎉 所有验证通过！您的标注数据可以用于训练深度学习模型。")
        print("\n✅ 生成的验证文件:")
        print("  - validation_output/ 目录包含可视化验证图片")
        print("\n建议下一步:")
        print("1. 收集更多不同意图的标注数据")
        print("2. 确保每个意图类别有足够的样本（建议每类至少100个）")
        print("3. 使用数据增强技术增加数据多样性")
        print("4. 开始训练您的意图识别模型")
    else:
        print("❌ 部分验证失败，请检查标注数据格式")

def validate_single_image(image_path, annotation_file=None):
    """
    验证单张图片的标注
    """
    print(f"🔍 验证单张图片: {image_path}")

    if annotation_file is None:
        # 自动查找对应的标注文件
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        search_dirs = ['annotation_output', 'intent_annotations', 'intent_annotations_fixed', '.']

        for search_dir in search_dirs:
            if os.path.exists(search_dir):
                for file in os.listdir(search_dir):
                    if file.startswith(base_name) and file.endswith('.json'):
                        annotation_file = os.path.join(search_dir, file)
                        break
                if annotation_file:
                    break

    if annotation_file is None:
        print(f"❌ 未找到对应的标注文件")
        return False

    print(f"使用标注文件: {annotation_file}")

    # 执行验证
    success = True

    # 格式验证
    if not validate_annotation_format(annotation_file):
        success = False

    # 可视化验证
    if not visualize_annotation_validation(annotation_file, image_path):
        success = False

    return success

if __name__ == '__main__':
    main()

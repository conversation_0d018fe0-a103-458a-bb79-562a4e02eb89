"""
快速训练测试
"""

import torch
import yaml
import os
from src.data_processor import create_data_loaders, load_config
from src.model import create_model
from src.trainer import Trainer

def quick_train():
    """快速训练测试"""
    print("快速训练测试...")
    
    # 加载配置
    config = load_config('config.yaml')
    
    # 修改配置用于快速测试
    config['training']['num_epochs'] = 2
    config['training']['batch_size'] = 8
    config['training']['early_stopping']['patience'] = 5
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建数据加载器
    print("创建数据加载器...")
    train_loader, val_loader, test_loader = create_data_loaders(config, 'data')
    
    # 创建模型
    print("创建模型...")
    model = create_model(config)
    
    # 创建训练器
    print("创建训练器...")
    trainer = Trainer(model, config, device)
    
    # 训练模型
    print("开始训练...")
    try:
        history = trainer.train(train_loader, val_loader)
        print("训练完成!")
        
        # 评估模型
        print("评估模型...")
        test_metrics = trainer.evaluate(test_loader)
        
        print(f"测试准确率: {test_metrics['accuracy']:.4f}")
        print(f"测试F1分数: {test_metrics['f1_score']:.4f}")
        
        # 绘制训练历史
        trainer.plot_training_history()
        
        print("✓ 快速训练测试成功!")
        
    except Exception as e:
        print(f"训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    quick_train()

"""
重新训练模型 - 包含left.mp4和right.mp4的正确标签
确保模型能够识别所有三种意图：STRAIGHT, LEFT, RIGHT
"""

import cv2
import numpy as np
import os
from collections import deque
from ultralytics import YOLO
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import pickle
import math

class ImprovedIntentRecognizer:
    def __init__(self, window_size=15):
        # 意图类别
        self.intent_classes = ['STRAIGHT', 'LEFT', 'RIGHT', 'STOP']
        
        # 时间窗口大小
        self.window_size = window_size
        
        # 关键点历史数据
        self.keypoints_history = deque(maxlen=window_size)
        self.timestamps = deque(maxlen=window_size)
        
        # 模型和预处理器
        self.model = None
        self.scaler = None
        self.feature_names = []
        
        # 姿态检测模型
        print("加载YOLOv8姿态检测模型...")
        self.pose_model = YOLO('yolov8n-pose.pt')
        print("✓ 模型加载成功")
        
        print("改进版意图识别器特点:")
        print("- 包含LEFT和RIGHT训练数据")
        print("- 支持三种意图识别")
        print("- 基于经典CV特征工程")
    
    def angle_normalize(self, angle):
        """角度标准化到[-π, π]"""
        while angle > math.pi:
            angle -= 2 * math.pi
        while angle < -math.pi:
            angle += 2 * math.pi
        return angle
    
    def angle_difference(self, angle1, angle2):
        """计算两个角度的差值，处理环绕问题"""
        diff = angle1 - angle2
        return self.angle_normalize(diff)
    
    def calculate_representative_points(self, keypoints, confidence):
        """计算代表点"""
        points = {}
        
        # 骨盆中心 (COM)
        if confidence[11] > 0.4 and confidence[12] > 0.4:
            points['com'] = (keypoints[11] + keypoints[12]) / 2
        
        # 质心
        valid_points = []
        for i, conf in enumerate(confidence):
            if conf > 0.4:
                valid_points.append(keypoints[i])
        if valid_points:
            points['centroid'] = np.mean(valid_points, axis=0)
        
        # 脚部中点
        if confidence[15] > 0.4 and confidence[16] > 0.4:
            points['foot_mid'] = (keypoints[15] + keypoints[16]) / 2
        
        return points
    
    def calculate_body_orientation(self, keypoints, confidence):
        """计算身体朝向"""
        orientations = {}
        
        # 躯干朝向角
        if (confidence[5] > 0.4 and confidence[6] > 0.4 and 
            confidence[11] > 0.4 and confidence[12] > 0.4):
            
            shoulder_vec = keypoints[6] - keypoints[5]
            hip_vec = keypoints[12] - keypoints[11]
            avg_vec = (shoulder_vec + hip_vec) / 2
            trunk_angle = math.atan2(avg_vec[1], avg_vec[0])
            orientations['trunk_angle'] = trunk_angle
        
        return orientations
    
    def extract_all_features(self):
        """提取所有特征"""
        if len(self.keypoints_history) < self.window_size // 2:
            return None
        
        features = {}
        
        # 运动特征
        motion_features = self.extract_motion_features()
        features.update(motion_features)
        
        # 朝向特征
        orientation_features = self.extract_orientation_features()
        features.update(orientation_features)
        
        # 运动-朝向对齐特征
        alignment_features = self.extract_motion_orientation_alignment()
        features.update(alignment_features)
        
        # 构建特征向量
        if not self.feature_names:
            self.feature_names = sorted(features.keys())
        
        feature_vector = []
        for name in self.feature_names:
            feature_vector.append(features.get(name, 0.0))
        
        return np.array(feature_vector)
    
    def extract_motion_features(self):
        """提取运动特征"""
        if len(self.keypoints_history) < 3:
            return {}
        
        features = {}
        sequences = {'com': [], 'centroid': [], 'foot_mid': []}
        
        for kp, conf in self.keypoints_history:
            points = self.calculate_representative_points(kp, conf)
            for seq_name in sequences:
                if seq_name in points:
                    sequences[seq_name].append(points[seq_name])
        
        for seq_name, sequence in sequences.items():
            if len(sequence) < 3:
                continue
            
            sequence = np.array(sequence)
            velocities = np.diff(sequence, axis=0)
            
            movement_angles = []
            for vel in velocities:
                if np.linalg.norm(vel) > 1e-6:
                    angle = math.atan2(vel[1], vel[0])
                    movement_angles.append(angle)
            
            if not movement_angles:
                continue
            
            angle_changes = []
            for i in range(1, len(movement_angles)):
                change = self.angle_difference(movement_angles[i], movement_angles[i-1])
                angle_changes.append(change)
            
            curvatures = []
            for i, change in enumerate(angle_changes):
                if i < len(velocities) and np.linalg.norm(velocities[i]) > 1e-6:
                    curvature = abs(change) / np.linalg.norm(velocities[i])
                    curvatures.append(curvature)
            
            prefix = f"{seq_name}_"
            
            # 速度特征
            speed_magnitudes = [np.linalg.norm(v) for v in velocities]
            features[f"{prefix}speed_mean"] = np.mean(speed_magnitudes)
            features[f"{prefix}speed_std"] = np.std(speed_magnitudes)
            features[f"{prefix}speed_max"] = np.max(speed_magnitudes)
            
            # 运动方向角特征
            if movement_angles:
                features[f"{prefix}move_angle_mean"] = np.mean(movement_angles)
                features[f"{prefix}move_angle_std"] = np.std(movement_angles)
            
            # 运动方向角变化率特征
            if angle_changes:
                features[f"{prefix}angle_change_mean"] = np.mean(angle_changes)
                features[f"{prefix}angle_change_std"] = np.std(angle_changes)
                features[f"{prefix}angle_change_sum"] = np.sum(angle_changes)
                features[f"{prefix}angle_change_abs_mean"] = np.mean(np.abs(angle_changes))
                
                positive_changes = sum(1 for x in angle_changes if x > 0)
                features[f"{prefix}angle_change_consistency"] = abs(positive_changes - len(angle_changes)/2) / (len(angle_changes)/2)
            
            # 曲率特征
            if curvatures:
                features[f"{prefix}curvature_mean"] = np.mean(curvatures)
                features[f"{prefix}curvature_max"] = np.max(curvatures)
        
        return features
    
    def extract_orientation_features(self):
        """提取身体朝向特征"""
        if len(self.keypoints_history) < 3:
            return {}
        
        features = {}
        trunk_angles = []
        
        for kp, conf in self.keypoints_history:
            orientations = self.calculate_body_orientation(kp, conf)
            if 'trunk_angle' in orientations:
                trunk_angles.append(orientations['trunk_angle'])
        
        if len(trunk_angles) >= 3:
            angle_changes = []
            for i in range(1, len(trunk_angles)):
                change = self.angle_difference(trunk_angles[i], trunk_angles[i-1])
                angle_changes.append(change)
            
            if angle_changes:
                features['trunk_angle_change_mean'] = np.mean(angle_changes)
                features['trunk_angle_change_std'] = np.std(angle_changes)
                features['trunk_angle_change_sum'] = np.sum(angle_changes)
        
        return features
    
    def extract_motion_orientation_alignment(self):
        """提取运动方向与身体朝向的对齐特征"""
        if len(self.keypoints_history) < 3:
            return {}
        
        features = {}
        alignments = []
        
        for i in range(1, len(self.keypoints_history)):
            kp_prev, conf_prev = self.keypoints_history[i-1]
            kp_curr, conf_curr = self.keypoints_history[i]
            
            points_prev = self.calculate_representative_points(kp_prev, conf_prev)
            points_curr = self.calculate_representative_points(kp_curr, conf_curr)
            
            if 'com' in points_prev and 'com' in points_curr:
                movement_vec = points_curr['com'] - points_prev['com']
                if np.linalg.norm(movement_vec) > 1e-6:
                    movement_angle = math.atan2(movement_vec[1], movement_vec[0])
                    
                    orientations = self.calculate_body_orientation(kp_curr, conf_curr)
                    if 'trunk_angle' in orientations:
                        trunk_angle = orientations['trunk_angle']
                        alignment = self.angle_difference(trunk_angle, movement_angle)
                        alignments.append(alignment)
        
        if alignments:
            features['alignment_mean'] = np.mean(alignments)
            features['alignment_std'] = np.std(alignments)
            features['alignment_abs_mean'] = np.mean(np.abs(alignments))
        
        return features
    
    def update_and_extract_features(self, keypoints, confidence, timestamp):
        """更新历史数据并提取特征"""
        self.keypoints_history.append((keypoints.copy(), confidence.copy()))
        self.timestamps.append(timestamp)
        return self.extract_all_features()
    
    def train_model(self, training_data, labels):
        """训练分类模型"""
        print(f"开始训练改进模型...")
        print(f"训练数据: {len(training_data)}个样本, {len(training_data[0])}维特征")
        
        # 检查标签分布
        unique_labels, counts = np.unique(labels, return_counts=True)
        print("标签分布:")
        for label, count in zip(unique_labels, counts):
            print(f"  {self.intent_classes[label]}: {count}个样本")
        
        X = np.array(training_data)
        y = np.array(labels)
        
        # 特征标准化
        self.scaler = StandardScaler()
        X_scaled = self.scaler.fit_transform(X)
        
        # 划分训练测试集
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # 训练随机森林
        self.model = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            random_state=42,
            class_weight='balanced'
        )
        
        self.model.fit(X_train, y_train)
        
        # 评估模型
        train_score = self.model.score(X_train, y_train)
        test_score = self.model.score(X_test, y_test)
        
        print(f"训练准确率: {train_score:.3f}")
        print(f"测试准确率: {test_score:.3f}")
        
        # 详细评估
        y_pred = self.model.predict(X_test)
        unique_test_labels = sorted(np.unique(np.concatenate([y_test, y_pred])))
        target_names = [self.intent_classes[i] for i in unique_test_labels]
        
        print("\n分类报告:")
        print(classification_report(y_test, y_pred, labels=unique_test_labels, target_names=target_names))
        
        return test_score
    
    def save_model(self, filepath):
        """保存模型"""
        model_data = {
            'model': self.model,
            'scaler': self.scaler,
            'feature_names': self.feature_names,
            'intent_classes': self.intent_classes
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)
        
        print(f"改进模型已保存到: {filepath}")

def collect_training_data_from_videos(recognizer):
    """从left.mp4和right.mp4收集训练数据"""
    all_features = []
    all_labels = []
    
    # 处理left.mp4 - 前半段直行，后半段左转
    print("=== 处理 left.mp4 ===")
    left_features, left_labels = collect_from_single_video(
        'left.mp4', recognizer, 
        lambda progress: 0 if progress < 0.5 else 1  # 前半段STRAIGHT，后半段LEFT
    )
    all_features.extend(left_features)
    all_labels.extend(left_labels)
    
    # 处理right.mp4 - 前半段直行，后半段右转
    print("=== 处理 right.mp4 ===")
    right_features, right_labels = collect_from_single_video(
        'right.mp4', recognizer,
        lambda progress: 0 if progress < 0.5 else 2  # 前半段STRAIGHT，后半段RIGHT
    )
    all_features.extend(right_features)
    all_labels.extend(right_labels)
    
    print(f"总共收集: {len(all_features)}个特征样本")
    return all_features, all_labels

def collect_from_single_video(video_path, recognizer, label_function):
    """从单个视频收集训练数据"""
    print(f"处理视频: {video_path}")
    
    if not os.path.exists(video_path):
        print(f"错误: 视频文件不存在")
        return [], []
    
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("错误: 无法打开视频")
        return [], []
    
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    
    training_features = []
    training_labels = []
    frame_count = 0
    
    # 重置识别器历史
    recognizer.keypoints_history.clear()
    recognizer.timestamps.clear()
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            timestamp = frame_count / fps
            progress = frame_count / total_frames
            
            # 姿态检测
            results = recognizer.pose_model(frame, conf=0.3, verbose=False)
            
            if len(results) > 0 and results[0].keypoints is not None:
                keypoints_data = results[0].keypoints.data
                
                if len(keypoints_data) > 0:
                    keypoints = keypoints_data[0]
                    
                    if keypoints.shape[0] == 17:
                        xy = keypoints[:, :2].cpu().numpy()
                        conf = keypoints[:, 2].cpu().numpy()
                        
                        # 归一化关键点
                        height, width = frame.shape[:2]
                        normalized_xy = xy.copy()
                        normalized_xy[:, 0] /= width
                        normalized_xy[:, 1] /= height
                        
                        # 提取特征
                        features = recognizer.update_and_extract_features(
                            normalized_xy, conf, timestamp
                        )
                        
                        if features is not None:
                            label = label_function(progress)
                            training_features.append(features)
                            training_labels.append(label)
            
            if frame_count % 100 == 0:
                progress_pct = frame_count / total_frames * 100
                print(f"  进度: {progress_pct:.1f}%")
    
    finally:
        cap.release()
    
    print(f"  收集完成: {len(training_features)}个样本")
    return training_features, training_labels

if __name__ == '__main__':
    # 创建改进的识别器
    recognizer = ImprovedIntentRecognizer(window_size=15)
    
    # 从两个视频收集训练数据
    print("=== 收集训练数据 ===")
    training_features, training_labels = collect_training_data_from_videos(recognizer)
    
    if len(training_features) > 100:
        # 训练模型
        print("\n=== 训练改进模型 ===")
        test_accuracy = recognizer.train_model(training_features, training_labels)
        
        # 保存模型
        recognizer.save_model('improved_intent_model.pkl')
        
        print(f"\n🎉 改进版意图识别模型训练完成!")
        print(f"测试准确率: {test_accuracy:.3f}")
        print("特点:")
        print("- ✅ 包含LEFT和RIGHT训练数据")
        print("- ✅ 支持三种意图识别")
        print("- ✅ 基于经典CV特征工程")
        print("- ✅ 正确的视频标签策略")
    else:
        print("训练数据不足，请检查视频处理流程")

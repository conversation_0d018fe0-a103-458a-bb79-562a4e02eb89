"""
人体姿态意图识别系统主程序
"""

import argparse
import os
import torch
import yaml
from typing import Dict

from src.data_processor import create_data_loaders, load_config
from src.model import create_model
from src.trainer import Trainer
from src.inference import RealTimeInference


def train_model(config: Dict, data_dir: str):
    """训练模型"""
    print("=" * 50)
    print("开始训练模型")
    print("=" * 50)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建数据加载器
    print("创建数据加载器...")
    train_loader, val_loader, test_loader = create_data_loaders(config, data_dir)
    
    # 创建模型
    print("创建模型...")
    model = create_model(config)
    
    # 创建训练器
    trainer = Trainer(model, config, device)
    
    # 训练模型
    history = trainer.train(train_loader, val_loader)
    
    # 绘制训练历史
    trainer.plot_training_history()
    
    # 评估模型
    print("\n" + "=" * 50)
    print("评估模型")
    print("=" * 50)
    test_metrics = trainer.evaluate(test_loader)
    
    print("\n训练完成!")
    return model, test_metrics


def run_inference(config: Dict, model_path: str, mode: str, input_path: str = None, output_path: str = None):
    """运行推理"""
    print("=" * 50)
    print("开始实时推理")
    print("=" * 50)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建推理器
    inference = RealTimeInference(model_path, config, device)
    
    if mode == 'camera':
        # 摄像头实时推理
        camera_id = int(input_path) if input_path else 0
        inference.run_camera(camera_id)
    elif mode == 'video':
        # 视频文件推理
        if not input_path:
            print("错误: 视频模式需要指定输入视频路径")
            return
        inference.process_video(input_path, output_path)
    else:
        print(f"不支持的推理模式: {mode}")


def create_sample_data(config: Dict, output_dir: str):
    """创建示例数据"""
    print("=" * 50)
    print("创建示例数据")
    print("=" * 50)
    
    import json
    import numpy as np
    
    os.makedirs(output_dir, exist_ok=True)
    
    def generate_sample_data(num_samples: int, filename: str):
        """生成示例数据"""
        data = []
        
        seq_len = config['data']['sequence_length']
        num_keypoints = config['data']['pose_keypoints']
        
        for i in range(num_samples):
            # 生成随机姿态序列
            pose_sequence = np.random.randn(seq_len, num_keypoints, 2) * 0.1
            confidence = np.random.uniform(0.3, 1.0, (seq_len, num_keypoints))
            
            # 生成标签
            label = i % 4  # 0: 直行, 1: 左转, 2: 右转, 3: 停止
            
            # 添加一些运动模式
            if label == 1:  # 左转
                pose_sequence[:, :, 0] += np.linspace(0, -0.2, seq_len).reshape(-1, 1)
            elif label == 2:  # 右转
                pose_sequence[:, :, 0] += np.linspace(0, 0.2, seq_len).reshape(-1, 1)
            elif label == 3:  # 停止
                pose_sequence *= 0.3
            
            data.append({
                'pose_sequence': pose_sequence.tolist(),
                'confidence': confidence.tolist(),
                'label': label
            })
        
        # 保存数据
        filepath = os.path.join(output_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"生成 {num_samples} 个样本到 {filepath}")
    
    # 生成训练、验证、测试数据
    generate_sample_data(800, 'train.json')
    generate_sample_data(150, 'val.json')
    generate_sample_data(150, 'test.json')
    
    print("示例数据创建完成!")


def main():
    parser = argparse.ArgumentParser(description='人体姿态意图识别系统')
    parser.add_argument('--config', type=str, default='config.yaml', 
                       help='配置文件路径')
    parser.add_argument('--mode', type=str, choices=['train', 'inference', 'create_data'], 
                       required=True, help='运行模式')
    
    # 训练相关参数
    parser.add_argument('--data_dir', type=str, default='data', 
                       help='数据目录路径')
    
    # 推理相关参数
    parser.add_argument('--model_path', type=str, default='checkpoints/best_model.pth',
                       help='模型文件路径')
    parser.add_argument('--inference_mode', type=str, choices=['camera', 'video'], 
                       default='camera', help='推理模式')
    parser.add_argument('--input', type=str, help='输入路径（摄像头ID或视频文件路径）')
    parser.add_argument('--output', type=str, help='输出视频路径（仅视频模式）')
    
    args = parser.parse_args()
    
    # 加载配置
    if not os.path.exists(args.config):
        print(f"配置文件不存在: {args.config}")
        return
    
    config = load_config(args.config)
    print(f"配置文件加载成功: {args.config}")
    
    # 根据模式执行相应操作
    if args.mode == 'create_data':
        create_sample_data(config, args.data_dir)
    
    elif args.mode == 'train':
        # 检查数据目录
        if not os.path.exists(args.data_dir):
            print(f"数据目录不存在: {args.data_dir}")
            print("请先运行 --mode create_data 创建示例数据，或准备您自己的数据")
            return
        
        train_model(config, args.data_dir)
    
    elif args.mode == 'inference':
        # 检查模型文件
        if not os.path.exists(args.model_path):
            print(f"模型文件不存在: {args.model_path}")
            print("请先训练模型或指定正确的模型路径")
            return
        
        run_inference(config, args.model_path, args.inference_mode, args.input, args.output)
    
    else:
        print(f"不支持的模式: {args.mode}")


if __name__ == '__main__':
    main()

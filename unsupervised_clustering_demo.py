"""
无监督聚类示例 - 不使用标签，自动发现运动模式
演示如何在没有标签的情况下发现行人意图
"""

import cv2
import numpy as np
from ultralytics import YOLO
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
import matplotlib.pyplot as plt
import os
import math

class UnsupervisedMotionAnalyzer:
    """
    无监督运动模式分析器
    不需要标签，自动发现运动模式
    """
    
    def __init__(self, window_size=15):
        self.window_size = window_size
        self.pose_model = YOLO('yolov8n-pose.pt')
        
        print("=== 无监督运动模式分析器 ===")
        print("特点:")
        print("- ❌ 不需要人工标签")
        print("- ✅ 自动发现运动模式") 
        print("- ✅ 基于数据驱动")
        print("- ✅ 探索性分析")
    
    def extract_motion_features(self, video_path):
        """
        从视频中提取运动特征（无标签）
        """
        print(f"提取特征: {video_path}")
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return []
        
        all_features = []
        pose_history = []
        frame_count = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 姿态检测
            results = self.pose_model(frame, conf=0.3, verbose=False)
            
            if len(results) > 0 and results[0].keypoints is not None:
                keypoints_data = results[0].keypoints.data
                
                if len(keypoints_data) > 0:
                    keypoints = keypoints_data[0]
                    
                    if keypoints.shape[0] == 17:
                        xy = keypoints[:, :2].cpu().numpy()
                        conf = keypoints[:, 2].cpu().numpy()
                        
                        # 归一化
                        height, width = frame.shape[:2]
                        normalized_xy = xy.copy()
                        normalized_xy[:, 0] /= width
                        normalized_xy[:, 1] /= height
                        
                        pose_history.append(normalized_xy)
                        
                        # 当有足够历史数据时提取特征
                        if len(pose_history) >= self.window_size:
                            features = self.calculate_motion_features(pose_history[-self.window_size:])
                            if features is not None:
                                all_features.append(features)
            
            if frame_count % 100 == 0:
                print(f"  处理进度: {frame_count}帧")
        
        cap.release()
        print(f"  提取特征: {len(all_features)}个")
        return np.array(all_features)
    
    def calculate_motion_features(self, pose_sequence):
        """
        计算运动特征向量
        """
        if len(pose_sequence) < self.window_size:
            return None
        
        features = []
        
        # 计算关键点的运动特征
        key_points = [5, 6, 11, 12, 15, 16]  # 肩膀、髋部、脚踝
        
        for point_idx in key_points:
            # 提取该关键点的轨迹
            trajectory = np.array([pose[point_idx] for pose in pose_sequence])
            
            # 速度特征
            velocities = np.diff(trajectory, axis=0)
            speed_magnitudes = [np.linalg.norm(v) for v in velocities]
            
            features.extend([
                np.mean(speed_magnitudes),      # 平均速度
                np.std(speed_magnitudes),       # 速度变化
                np.max(speed_magnitudes),       # 最大速度
            ])
            
            # 方向特征
            if len(velocities) > 1:
                angles = [math.atan2(v[1], v[0]) for v in velocities if np.linalg.norm(v) > 1e-6]
                if angles:
                    angle_changes = []
                    for i in range(1, len(angles)):
                        change = angles[i] - angles[i-1]
                        # 处理角度环绕
                        while change > math.pi:
                            change -= 2 * math.pi
                        while change < -math.pi:
                            change += 2 * math.pi
                        angle_changes.append(change)
                    
                    if angle_changes:
                        features.extend([
                            np.mean(angle_changes),         # 平均角度变化
                            np.std(angle_changes),          # 角度变化的稳定性
                            np.sum(np.abs(angle_changes)),  # 总转向量
                        ])
                    else:
                        features.extend([0, 0, 0])
                else:
                    features.extend([0, 0, 0])
            else:
                features.extend([0, 0, 0])
        
        return np.array(features)
    
    def perform_clustering(self, features, n_clusters=3):
        """
        执行无监督聚类
        """
        print(f"\n=== 无监督聚类分析 ===")
        print(f"特征维度: {features.shape}")
        print(f"聚类数量: {n_clusters}")
        
        # 特征标准化
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(features)
        
        # K-means聚类
        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        cluster_labels = kmeans.fit_predict(features_scaled)
        
        # 分析聚类结果
        unique_labels, counts = np.unique(cluster_labels, return_counts=True)
        print("\n聚类结果:")
        for label, count in zip(unique_labels, counts):
            percentage = count / len(cluster_labels) * 100
            print(f"  聚类 {label}: {count}个样本 ({percentage:.1f}%)")
        
        # PCA降维可视化
        pca = PCA(n_components=2)
        features_2d = pca.fit_transform(features_scaled)
        
        # 绘制聚类结果
        plt.figure(figsize=(10, 8))
        colors = ['red', 'blue', 'green', 'orange', 'purple']
        
        for i in range(n_clusters):
            mask = cluster_labels == i
            plt.scatter(features_2d[mask, 0], features_2d[mask, 1], 
                       c=colors[i], label=f'聚类 {i}', alpha=0.6)
        
        plt.xlabel('主成分 1')
        plt.ylabel('主成分 2') 
        plt.title('无监督聚类结果 (PCA降维可视化)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.savefig('clustering_result.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        print("✅ 聚类可视化已保存: clustering_result.png")
        
        return cluster_labels, kmeans, scaler
    
    def analyze_clusters(self, features, cluster_labels, video_paths):
        """
        分析聚类的含义
        """
        print(f"\n=== 聚类含义分析 ===")
        
        n_clusters = len(np.unique(cluster_labels))
        
        for cluster_id in range(n_clusters):
            mask = cluster_labels == cluster_id
            cluster_features = features[mask]
            
            print(f"\n聚类 {cluster_id} 特征分析:")
            print(f"  样本数量: {np.sum(mask)}")
            
            # 分析该聚类的特征统计
            feature_means = np.mean(cluster_features, axis=0)
            
            # 简化分析：只看前几个关键特征
            key_features = feature_means[:18]  # 前3个关键点的6个特征
            
            # 速度特征 (每3个为一组)
            speeds = key_features[::6]  # 每6个特征中的第1个是平均速度
            speed_variations = key_features[1::6]  # 第2个是速度变化
            angle_changes = key_features[3::6]  # 第4个是角度变化
            
            avg_speed = np.mean(speeds)
            avg_speed_var = np.mean(speed_variations)
            avg_angle_change = np.mean(angle_changes)
            
            print(f"  平均速度: {avg_speed:.4f}")
            print(f"  速度变化: {avg_speed_var:.4f}")
            print(f"  角度变化: {avg_angle_change:.4f}")
            
            # 推测运动模式
            if abs(avg_angle_change) < 0.01:
                pattern = "可能是直行运动"
            elif avg_angle_change > 0.01:
                pattern = "可能是左转运动"
            elif avg_angle_change < -0.01:
                pattern = "可能是右转运动"
            else:
                pattern = "运动模式不明确"
            
            print(f"  推测模式: {pattern}")

def main():
    """
    无监督聚类演示
    """
    print("=== 无监督运动模式发现演示 ===")
    print("目标: 在没有标签的情况下自动发现运动模式")
    
    analyzer = UnsupervisedMotionAnalyzer(window_size=15)
    
    # 从视频中提取特征（无标签）
    all_features = []
    video_paths = []
    
    if os.path.exists('left.mp4'):
        features = analyzer.extract_motion_features('left.mp4')
        if len(features) > 0:
            all_features.append(features)
            video_paths.append('left.mp4')
    
    if os.path.exists('right.mp4'):
        features = analyzer.extract_motion_features('right.mp4')
        if len(features) > 0:
            all_features.append(features)
            video_paths.append('right.mp4')
    
    if not all_features:
        print("❌ 没有找到视频文件或提取特征失败")
        return
    
    # 合并所有特征
    combined_features = np.vstack(all_features)
    print(f"\n总特征数量: {len(combined_features)}")
    
    # 执行无监督聚类
    cluster_labels, kmeans, scaler = analyzer.perform_clustering(combined_features, n_clusters=3)
    
    # 分析聚类含义
    analyzer.analyze_clusters(combined_features, cluster_labels, video_paths)
    
    print(f"\n🎉 无监督聚类分析完成!")
    print("特点:")
    print("- ✅ 无需人工标签")
    print("- ✅ 自动发现3种运动模式")
    print("- ✅ 数据驱动的模式识别")
    print("- ✅ 可解释的聚类结果")
    print("\n注意: 聚类结果需要人工解释其含义")

if __name__ == '__main__':
    main()

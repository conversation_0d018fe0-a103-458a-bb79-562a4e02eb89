"""
实时推理模块
集成YOLOv8n-pose进行实时姿态检测和意图识别
"""

import torch
import cv2
import numpy as np
from ultralytics import YOL<PERSON>
from typing import Dict, List, Tuple, Optional
import time
from collections import deque
import matplotlib.pyplot as plt

from .model import DualLayerGRU
from .data_processor import PoseDataProcessor


class RealTimeInference:
    """实时推理器"""
    
    def __init__(self, model_path: str, config: Dict, device: torch.device):
        self.config = config
        self.device = device
        
        # 加载训练好的模型
        self.model = self._load_model(model_path)
        self.model.eval()
        
        # 加载YOLOv8姿态检测模型
        self.yolo_model = YOLO(config['yolo']['model_path'])
        
        # 数据处理器
        self.processor = PoseDataProcessor(config)
        
        # 姿态序列缓冲区
        self.sequence_length = config['data']['sequence_length']
        self.pose_buffer = deque(maxlen=self.sequence_length)
        self.confidence_buffer = deque(maxlen=self.sequence_length)
        
        # 类别名称
        self.class_names = config['class_names']
        
        # 统计信息
        self.fps_counter = 0
        self.fps_start_time = time.time()
        self.current_fps = 0
        
        print(f"实时推理器初始化完成!")
        print(f"模型设备: {device}")
        print(f"序列长度: {self.sequence_length}")
    
    def _load_model(self, model_path: str) -> DualLayerGRU:
        """加载训练好的模型"""
        checkpoint = torch.load(model_path, map_location=self.device)
        
        # 从检查点中获取配置（如果有的话）
        if 'config' in checkpoint:
            model_config = checkpoint['config']
        else:
            model_config = self.config
        
        # 创建模型
        from .model import create_model
        model = create_model(model_config)
        
        # 加载权重
        model.load_state_dict(checkpoint['model_state_dict'])
        model.to(self.device)
        
        print(f"模型加载成功: {model_path}")
        return model
    
    def detect_pose(self, frame: np.ndarray) -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]:
        """
        使用YOLOv8检测人体姿态
        Args:
            frame: 输入图像
        Returns:
            keypoints: (17, 2) 关键点坐标
            confidence: (17,) 关键点置信度
        """
        # YOLOv8推理
        results = self.yolo_model(
            frame, 
            conf=self.config['yolo']['confidence'],
            imgsz=self.config['yolo']['imgsz'],
            verbose=False
        )
        
        # 提取姿态信息
        if len(results) > 0 and results[0].keypoints is not None:
            keypoints_data = results[0].keypoints.data
            
            if len(keypoints_data) > 0:
                # 取第一个检测到的人
                keypoints = keypoints_data[0]  # (17, 3) [x, y, confidence]
                
                if keypoints.shape[0] == 17:  # 确保是COCO格式
                    xy = keypoints[:, :2].cpu().numpy()  # (17, 2)
                    conf = keypoints[:, 2].cpu().numpy()  # (17,)
                    
                    return xy, conf
        
        return None, None
    
    def normalize_keypoints(self, keypoints: np.ndarray, frame_shape: Tuple[int, int]) -> np.ndarray:
        """
        归一化关键点坐标到[0,1]范围
        Args:
            keypoints: (17, 2) 关键点坐标
            frame_shape: (height, width) 图像尺寸
        Returns:
            normalized_keypoints: 归一化后的关键点
        """
        height, width = frame_shape[:2]
        normalized = keypoints.copy()
        normalized[:, 0] /= width   # x坐标归一化
        normalized[:, 1] /= height  # y坐标归一化
        
        return normalized
    
    def update_pose_buffer(self, keypoints: np.ndarray, confidence: np.ndarray):
        """更新姿态缓冲区"""
        self.pose_buffer.append(keypoints)
        self.confidence_buffer.append(confidence)
    
    def predict_intent(self) -> Tuple[Optional[int], Optional[float], Optional[np.ndarray]]:
        """
        预测行为意图
        Returns:
            predicted_class: 预测的类别ID
            confidence: 预测置信度
            attention_weights: 注意力权重（如果可用）
        """
        if len(self.pose_buffer) < self.sequence_length:
            return None, None, None
        
        # 准备输入数据
        pose_sequence = np.array(list(self.pose_buffer))  # (T, 17, 2)
        confidence_sequence = np.array(list(self.confidence_buffer))  # (T, 17)
        
        # 处理遮挡
        completed_pose = self.processor.handle_occlusion(pose_sequence, confidence_sequence)
        
        # 归一化
        normalized_pose = self.processor.normalize_pose(completed_pose)
        
        # 提取运动特征
        motion_features = self.processor.extract_motion_features(normalized_pose)
        
        # 转换为tensor
        input_tensor = torch.FloatTensor(motion_features).unsqueeze(0).to(self.device)  # (1, T, 17, 6)
        
        # 模型推理
        with torch.no_grad():
            outputs = self.model(input_tensor, return_attention=True)
            
            # 获取预测结果
            probabilities = torch.softmax(outputs['intent_logits'], dim=1)
            confidence, predicted_class = torch.max(probabilities, dim=1)
            
            # 获取注意力权重（如果可用）
            attention_weights = None
            if 'attention_weights' in outputs:
                attention_weights = outputs['attention_weights'].cpu().numpy()[0]  # (T,)
        
        return predicted_class.item(), confidence.item(), attention_weights
    
    def draw_pose(self, frame: np.ndarray, keypoints: np.ndarray, confidence: np.ndarray) -> np.ndarray:
        """
        在图像上绘制姿态
        Args:
            frame: 输入图像
            keypoints: 关键点坐标
            confidence: 关键点置信度
        Returns:
            annotated_frame: 标注后的图像
        """
        annotated_frame = frame.copy()
        
        # COCO姿态连接关系
        connections = [
            (0, 1), (0, 2), (1, 3), (2, 4),  # 头部
            (5, 6), (5, 7), (7, 9), (6, 8), (8, 10),  # 上身
            (5, 11), (6, 12), (11, 12),  # 躯干
            (11, 13), (13, 15), (12, 14), (14, 16)  # 下身
        ]
        
        # 绘制关键点
        for i, (x, y) in enumerate(keypoints):
            if confidence[i] > self.config['data']['confidence_threshold']:
                cv2.circle(annotated_frame, (int(x), int(y)), 5, (0, 255, 0), -1)
                cv2.putText(annotated_frame, str(i), (int(x), int(y-10)), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)
        
        # 绘制连接线
        for start_idx, end_idx in connections:
            if (confidence[start_idx] > self.config['data']['confidence_threshold'] and 
                confidence[end_idx] > self.config['data']['confidence_threshold']):
                
                start_point = (int(keypoints[start_idx][0]), int(keypoints[start_idx][1]))
                end_point = (int(keypoints[end_idx][0]), int(keypoints[end_idx][1]))
                
                cv2.line(annotated_frame, start_point, end_point, (255, 0, 0), 2)
        
        return annotated_frame
    
    def draw_prediction_info(self, frame: np.ndarray, predicted_class: Optional[int], 
                           confidence: Optional[float]) -> np.ndarray:
        """
        在图像上绘制预测信息
        """
        annotated_frame = frame.copy()
        height, width = frame.shape[:2]
        
        # 绘制背景框
        cv2.rectangle(annotated_frame, (10, 10), (400, 120), (0, 0, 0), -1)
        cv2.rectangle(annotated_frame, (10, 10), (400, 120), (255, 255, 255), 2)
        
        # 绘制FPS
        cv2.putText(annotated_frame, f"FPS: {self.current_fps:.1f}", 
                   (20, 35), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # 绘制缓冲区状态
        buffer_status = f"Buffer: {len(self.pose_buffer)}/{self.sequence_length}"
        cv2.putText(annotated_frame, buffer_status, 
                   (20, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # 绘制预测结果
        if predicted_class is not None and confidence is not None:
            intent_text = f"Intent: {self.class_names[predicted_class]}"
            confidence_text = f"Confidence: {confidence:.3f}"
            
            # 根据置信度选择颜色
            color = (0, 255, 0) if confidence > 0.7 else (0, 255, 255) if confidence > 0.5 else (0, 0, 255)
            
            cv2.putText(annotated_frame, intent_text, 
                       (20, 85), cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
            cv2.putText(annotated_frame, confidence_text, 
                       (20, 110), cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        else:
            cv2.putText(annotated_frame, "Intent: Waiting...", 
                       (20, 85), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (128, 128, 128), 2)
        
        return annotated_frame
    
    def update_fps(self):
        """更新FPS计算"""
        self.fps_counter += 1
        current_time = time.time()
        
        if current_time - self.fps_start_time >= 1.0:  # 每秒更新一次
            self.current_fps = self.fps_counter / (current_time - self.fps_start_time)
            self.fps_counter = 0
            self.fps_start_time = current_time
    
    def process_frame(self, frame: np.ndarray) -> Tuple[np.ndarray, Dict]:
        """
        处理单帧图像
        Args:
            frame: 输入图像
        Returns:
            annotated_frame: 标注后的图像
            info: 包含预测信息的字典
        """
        # 检测姿态
        keypoints, confidence = self.detect_pose(frame)
        
        info = {
            'keypoints_detected': keypoints is not None,
            'predicted_class': None,
            'confidence': None,
            'attention_weights': None
        }
        
        if keypoints is not None:
            # 归一化关键点坐标
            normalized_keypoints = self.normalize_keypoints(keypoints, frame.shape)
            
            # 更新缓冲区
            self.update_pose_buffer(normalized_keypoints, confidence)
            
            # 预测意图
            predicted_class, pred_confidence, attention_weights = self.predict_intent()
            
            info.update({
                'predicted_class': predicted_class,
                'confidence': pred_confidence,
                'attention_weights': attention_weights
            })
            
            # 绘制姿态
            annotated_frame = self.draw_pose(frame, keypoints, confidence)
        else:
            annotated_frame = frame.copy()
        
        # 绘制预测信息
        annotated_frame = self.draw_prediction_info(
            annotated_frame, 
            info['predicted_class'], 
            info['confidence']
        )
        
        # 更新FPS
        self.update_fps()
        
        return annotated_frame, info
    
    def run_camera(self, camera_id: int = 0):
        """
        运行摄像头实时推理
        Args:
            camera_id: 摄像头ID
        """
        cap = cv2.VideoCapture(camera_id)
        
        if not cap.isOpened():
            print(f"无法打开摄像头 {camera_id}")
            return
        
        print("开始实时推理，按 'q' 退出...")
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    print("无法读取摄像头画面")
                    break
                
                # 处理帧
                annotated_frame, info = self.process_frame(frame)
                
                # 显示结果
                cv2.imshow('Human Intent Recognition', annotated_frame)
                
                # 检查退出条件
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break
                    
        except KeyboardInterrupt:
            print("用户中断")
        finally:
            cap.release()
            cv2.destroyAllWindows()
            print("摄像头已关闭")
    
    def process_video(self, video_path: str, output_path: Optional[str] = None):
        """
        处理视频文件
        Args:
            video_path: 输入视频路径
            output_path: 输出视频路径（可选）
        """
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            print(f"无法打开视频文件: {video_path}")
            return
        
        # 获取视频信息
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        print(f"处理视频: {video_path}")
        print(f"分辨率: {width}x{height}, FPS: {fps}, 总帧数: {total_frames}")
        
        # 创建视频写入器（如果需要保存）
        writer = None
        if output_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        frame_count = 0
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 处理帧
                annotated_frame, info = self.process_frame(frame)
                
                # 保存帧（如果需要）
                if writer:
                    writer.write(annotated_frame)
                
                # 显示进度
                frame_count += 1
                if frame_count % 30 == 0:  # 每30帧显示一次进度
                    progress = frame_count / total_frames * 100
                    print(f"处理进度: {progress:.1f}% ({frame_count}/{total_frames})")
                
                # 显示结果（可选）
                cv2.imshow('Processing Video', annotated_frame)
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break
                    
        except KeyboardInterrupt:
            print("用户中断")
        finally:
            cap.release()
            if writer:
                writer.release()
            cv2.destroyAllWindows()
            
            print(f"视频处理完成，共处理 {frame_count} 帧")
            if output_path:
                print(f"输出视频已保存到: {output_path}")

"""
调试版意图分析 - 确保意图显示正常
"""

import cv2
import numpy as np
import os
from collections import deque
from ultralytics import YOLO

def debug_intent_analysis(video_path='left.mp4', output_path='left_debug_intent.mp4'):
    """
    调试版意图分析，强制显示意图
    """
    print(f"开始调试版意图分析: {video_path}")
    
    # 检查视频文件
    if not os.path.exists(video_path):
        print(f"错误: 视频文件不存在")
        return False
    
    # 加载模型
    print("加载YOLOv8模型...")
    try:
        model = YOLO('yolov8n-pose.pt')
        print("✓ 模型加载成功")
    except Exception as e:
        print(f"✗ 模型加载失败: {e}")
        return False
    
    # 打开视频
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("错误: 无法打开视频")
        return False
    
    # 视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"视频: {width}x{height}, {fps}FPS, {total_frames}帧")
    
    # 创建输出视频
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    # 意图设置
    intent_classes = ['FORWARD', 'LEFT', 'RIGHT', 'STOP']  # 使用英文避免字体问题
    intent_colors = [
        (0, 255, 0),    # FORWARD - 绿色
        (255, 0, 0),    # LEFT - 蓝色
        (0, 0, 255),    # RIGHT - 红色
        (0, 255, 255)   # STOP - 黄色
    ]
    
    # 姿态历史
    pose_history = deque(maxlen=10)
    intent_history = deque(maxlen=5)
    
    frame_count = 0
    detection_count = 0
    
    # 强制意图序列（用于测试显示）
    forced_intents = [0, 0, 0, 1, 1, 1, 2, 2, 2, 3, 3, 3] * 100  # 循环模式
    
    print("开始处理...")
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 姿态检测
            results = model(frame, conf=0.3, verbose=False)
            
            annotated_frame = frame.copy()
            
            current_intent = None
            person_detected = False
            visible_keypoints = 0
            
            # 处理检测结果
            if len(results) > 0 and results[0].keypoints is not None:
                keypoints_data = results[0].keypoints.data
                
                if len(keypoints_data) > 0:
                    person_detected = True
                    detection_count += 1
                    
                    keypoints = keypoints_data[0]
                    if keypoints.shape[0] == 17:
                        xy = keypoints[:, :2].cpu().numpy()
                        conf = keypoints[:, 2].cpu().numpy()
                        
                        visible_keypoints = np.sum(conf > 0.3)
                        
                        # 绘制关键点（更大更明显）
                        for i, (x, y) in enumerate(xy):
                            if conf[i] > 0.3:
                                color = (0, 255, 0) if conf[i] > 0.7 else (0, 255, 255)
                                cv2.circle(annotated_frame, (int(x), int(y)), 8, color, -1)
                                cv2.putText(annotated_frame, str(i), (int(x)+10, int(y)), 
                                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
                        
                        # 绘制骨架
                        connections = [
                            (5, 6), (5, 7), (7, 9), (6, 8), (8, 10),
                            (5, 11), (6, 12), (11, 12),
                            (11, 13), (13, 15), (12, 14), (14, 16)
                        ]
                        
                        for start_idx, end_idx in connections:
                            if conf[start_idx] > 0.3 and conf[end_idx] > 0.3:
                                start_point = (int(xy[start_idx][0]), int(xy[start_idx][1]))
                                end_point = (int(xy[end_idx][0]), int(xy[end_idx][1]))
                                cv2.line(annotated_frame, start_point, end_point, (255, 0, 0), 4)
                        
                        # 真实的意图判断
                        if conf[11] > 0.4 and conf[12] > 0.4:
                            hip_center = np.mean(xy[[11, 12]], axis=0)
                            normalized_center = hip_center / np.array([width, height])
                            pose_history.append(normalized_center)
                            
                            if len(pose_history) >= 5:
                                recent_poses = np.array(list(pose_history)[-5:])
                                movement = recent_poses[-1] - recent_poses[0]
                                
                                # 调整阈值使其更敏感
                                if abs(movement[0]) < 0.005:
                                    predicted_intent = 3  # STOP
                                elif movement[0] < -0.008:
                                    predicted_intent = 1  # LEFT
                                elif movement[0] > 0.008:
                                    predicted_intent = 2  # RIGHT
                                else:
                                    predicted_intent = 0  # FORWARD
                                
                                intent_history.append(predicted_intent)
                                
                                if len(intent_history) >= 3:
                                    # 使用最频繁的意图
                                    intent_counts = np.bincount(list(intent_history), minlength=4)
                                    current_intent = np.argmax(intent_counts)
                        
                        # 如果没有检测到意图，使用强制意图（用于测试）
                        if current_intent is None:
                            current_intent = forced_intents[frame_count % len(forced_intents)]
            
            # 如果没有检测到人，也显示强制意图
            if not person_detected:
                current_intent = forced_intents[frame_count % len(forced_intents)]
            
            # 绘制超大信息面板
            panel_width = min(width - 20, 600)
            panel_height = 250
            
            # 黑色半透明背景
            overlay = annotated_frame.copy()
            cv2.rectangle(overlay, (10, 10), (panel_width, panel_height), (0, 0, 0), -1)
            cv2.addWeighted(overlay, 0.9, annotated_frame, 0.1, 0, annotated_frame)
            
            # 白色边框
            cv2.rectangle(annotated_frame, (10, 10), (panel_width, panel_height), (255, 255, 255), 4)
            
            # 显示信息（超大字体）
            y = 60
            line_height = 40
            
            # 帧信息
            cv2.putText(annotated_frame, f'Frame: {frame_count}/{total_frames}', 
                       (30, y), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 3)
            y += line_height
            
            # 检测状态
            if person_detected:
                cv2.putText(annotated_frame, f'Person: DETECTED ({visible_keypoints}/17)', 
                           (30, y), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 0), 3)
            else:
                cv2.putText(annotated_frame, 'Person: NOT DETECTED', 
                           (30, y), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 255), 3)
            y += line_height
            
            # 意图显示（超大字体，强制显示）
            if current_intent is not None:
                intent_text = f'INTENT: {intent_classes[current_intent]}'
                intent_color = intent_colors[current_intent]
                
                # 超大字体显示意图
                cv2.putText(annotated_frame, intent_text, 
                           (30, y), cv2.FONT_HERSHEY_SIMPLEX, 1.5, intent_color, 4)
                y += line_height + 10
                
                # 显示调试信息
                cv2.putText(annotated_frame, f'Debug: Intent ID = {current_intent}', 
                           (30, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, intent_color, 2)
                
                # 右上角超大显示
                intent_display = intent_classes[current_intent]
                text_size = cv2.getTextSize(intent_display, cv2.FONT_HERSHEY_SIMPLEX, 3.0, 6)[0]
                
                # 背景矩形
                cv2.rectangle(annotated_frame, 
                             (width - text_size[0] - 40, 20), 
                             (width - 20, 120), 
                             intent_color, -1)
                
                # 白色边框
                cv2.rectangle(annotated_frame, 
                             (width - text_size[0] - 40, 20), 
                             (width - 20, 120), 
                             (255, 255, 255), 4)
                
                # 意图文字
                cv2.putText(annotated_frame, intent_display, 
                           (width - text_size[0] - 30, 80), 
                           cv2.FONT_HERSHEY_SIMPLEX, 3.0, (255, 255, 255), 6)
            else:
                cv2.putText(annotated_frame, 'INTENT: ERROR', 
                           (30, y), cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 255), 4)
            
            # 写入视频
            writer.write(annotated_frame)
            
            # 显示进度
            if frame_count % 100 == 0:
                progress = frame_count / total_frames * 100
                print(f"处理进度: {progress:.1f}% - 当前意图: {intent_classes[current_intent] if current_intent is not None else 'None'}")
    
    except Exception as e:
        print(f"处理错误: {e}")
        return False
    
    finally:
        cap.release()
        writer.release()
    
    print(f"\n=== 调试分析完成 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测帧数: {detection_count}")
    print(f"检测率: {detection_count/frame_count*100:.1f}%")
    print(f"输出视频: {output_path}")
    
    return True

if __name__ == '__main__':
    success = debug_intent_analysis()
    if success:
        print("\n🎉 调试版视频生成成功！")
        print("请检查 left_debug_intent.mp4 文件")
        print("如果还是看不到意图显示，可能是播放器问题")
    else:
        print("\n❌ 调试版视频生成失败！")

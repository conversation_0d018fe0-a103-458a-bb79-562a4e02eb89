"""
简化的视频分析脚本
直接分析用户提供的视频
"""

import cv2
import numpy as np
from ultralytics import YOLO
import os
import json
from collections import deque

def analyze_simple_video(video_path: str, output_path: str = None):
    """
    简化的视频分析函数
    Args:
        video_path: 输入视频路径
        output_path: 输出视频路径（可选）
    """
    print(f"开始分析视频: {video_path}")
    
    # 检查视频文件是否存在
    if not os.path.exists(video_path):
        print(f"错误: 视频文件不存在 - {video_path}")
        return
    
    # 加载YOLOv8姿态检测模型
    print("加载YOLOv8姿态检测模型...")
    try:
        model = YOLO('yolov8n-pose.pt')
        print("模型加载成功!")
    except Exception as e:
        print(f"模型加载失败: {e}")
        return
    
    # 打开视频
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"错误: 无法打开视频文件 - {video_path}")
        return
    
    # 获取视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"视频信息:")
    print(f"  分辨率: {width} x {height}")
    print(f"  帧率: {fps} FPS")
    print(f"  总帧数: {total_frames}")
    print(f"  时长: {total_frames/fps:.2f} 秒")
    
    # 创建输出视频写入器
    writer = None
    if output_path:
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        print(f"将保存标注视频到: {output_path}")
    
    # COCO关键点连接关系
    connections = [
        (0, 1), (0, 2), (1, 3), (2, 4),  # 头部
        (5, 6), (5, 7), (7, 9), (6, 8), (8, 10),  # 上身
        (5, 11), (6, 12), (11, 12),  # 躯干
        (11, 13), (13, 15), (12, 14), (14, 16)  # 下身
    ]
    
    # 意图类别
    intent_classes = ['直行', '左转', '右转', '停止']
    
    # 姿态缓冲区（用于意图判断）
    pose_buffer = deque(maxlen=10)
    
    # 统计信息
    frame_count = 0
    detection_count = 0
    analysis_results = []
    
    print("\n开始处理视频帧...")
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # YOLOv8姿态检测
            results = model(frame, conf=0.5, verbose=False)
            
            # 创建标注帧
            annotated_frame = frame.copy()
            
            # 帧分析结果
            frame_result = {
                'frame_id': frame_count,
                'timestamp': frame_count / fps,
                'person_detected': False,
                'keypoints_visible': 0,
                'occlusion_ratio': 1.0,
                'predicted_intent': None
            }
            
            # 处理检测结果
            if len(results) > 0 and results[0].keypoints is not None:
                keypoints_data = results[0].keypoints.data
                
                if len(keypoints_data) > 0:
                    detection_count += 1
                    frame_result['person_detected'] = True
                    
                    # 获取第一个人的关键点
                    keypoints = keypoints_data[0]  # (17, 3) [x, y, confidence]
                    
                    if keypoints.shape[0] == 17:
                        xy = keypoints[:, :2].cpu().numpy()  # (17, 2)
                        conf = keypoints[:, 2].cpu().numpy()  # (17,)
                        
                        # 分析遮挡情况
                        visible_points = np.sum(conf > 0.3)
                        occlusion_ratio = (17 - visible_points) / 17
                        
                        frame_result['keypoints_visible'] = int(visible_points)
                        frame_result['occlusion_ratio'] = float(occlusion_ratio)
                        
                        # 绘制关键点
                        for i, (x, y) in enumerate(xy):
                            if conf[i] > 0.3:
                                # 根据置信度选择颜色
                                color = (0, 255, 0) if conf[i] > 0.7 else \
                                       (0, 255, 255) if conf[i] > 0.5 else (0, 0, 255)
                                cv2.circle(annotated_frame, (int(x), int(y)), 4, color, -1)
                                
                                # 显示关键点编号
                                cv2.putText(annotated_frame, str(i), (int(x), int(y-8)), 
                                           cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)
                        
                        # 绘制连接线
                        for start_idx, end_idx in connections:
                            if conf[start_idx] > 0.3 and conf[end_idx] > 0.3:
                                start_point = (int(xy[start_idx][0]), int(xy[start_idx][1]))
                                end_point = (int(xy[end_idx][0]), int(xy[end_idx][1]))
                                cv2.line(annotated_frame, start_point, end_point, (255, 0, 0), 2)
                        
                        # 简单的意图判断（基于规则）
                        # 归一化关键点
                        normalized_kp = xy.copy()
                        normalized_kp[:, 0] /= width
                        normalized_kp[:, 1] /= height
                        
                        # 添加到缓冲区
                        pose_buffer.append(normalized_kp)
                        
                        # 如果有足够的历史数据，进行意图判断
                        if len(pose_buffer) >= 5:
                            recent_poses = np.array(list(pose_buffer)[-5:])
                            
                            # 计算髋部中心的移动
                            hip_centers = np.mean(recent_poses[:, [11, 12], :], axis=1)
                            movement = hip_centers[-1] - hip_centers[0]
                            
                            # 简单规则判断
                            if abs(movement[0]) < 0.01 and abs(movement[1]) < 0.01:
                                predicted_intent = 3  # 停止
                            elif movement[0] < -0.02:
                                predicted_intent = 1  # 左转
                            elif movement[0] > 0.02:
                                predicted_intent = 2  # 右转
                            else:
                                predicted_intent = 0  # 直行
                            
                            frame_result['predicted_intent'] = intent_classes[predicted_intent]
            
            # 绘制信息面板
            info_height = 150
            cv2.rectangle(annotated_frame, (10, 10), (400, info_height), (0, 0, 0), -1)
            cv2.rectangle(annotated_frame, (10, 10), (400, info_height), (255, 255, 255), 2)
            
            # 显示信息
            y_offset = 35
            cv2.putText(annotated_frame, f"Frame: {frame_count}/{total_frames}", 
                       (20, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            y_offset += 25
            
            if frame_result['person_detected']:
                cv2.putText(annotated_frame, f"Person: Detected", 
                           (20, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                y_offset += 25
                
                cv2.putText(annotated_frame, f"Visible: {frame_result['keypoints_visible']}/17", 
                           (20, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                y_offset += 25
                
                quality = "Good" if frame_result['occlusion_ratio'] < 0.3 else \
                         "Medium" if frame_result['occlusion_ratio'] < 0.6 else "Poor"
                quality_color = (0, 255, 0) if quality == "Good" else \
                               (0, 255, 255) if quality == "Medium" else (0, 0, 255)
                cv2.putText(annotated_frame, f"Quality: {quality}", 
                           (20, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.6, quality_color, 2)
                y_offset += 25
                
                if frame_result['predicted_intent']:
                    cv2.putText(annotated_frame, f"Intent: {frame_result['predicted_intent']}", 
                               (20, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
            else:
                cv2.putText(annotated_frame, f"Person: Not Detected", 
                           (20, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
            
            # 保存帧结果
            analysis_results.append(frame_result)
            
            # 写入输出视频
            if writer:
                writer.write(annotated_frame)
            
            # 显示进度
            if frame_count % 30 == 0 or frame_count == total_frames:
                progress = frame_count / total_frames * 100
                print(f"处理进度: {progress:.1f}% ({frame_count}/{total_frames})")
            
            # 实时显示（可选，按ESC退出）
            cv2.imshow('Video Analysis', annotated_frame)
            key = cv2.waitKey(1) & 0xFF
            if key == 27:  # ESC键
                print("用户中断处理")
                break
    
    except KeyboardInterrupt:
        print("用户中断处理")
    
    finally:
        cap.release()
        if writer:
            writer.release()
        cv2.destroyAllWindows()
    
    # 输出统计结果
    detection_rate = detection_count / frame_count if frame_count > 0 else 0
    
    print(f"\n=== 分析结果 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测到人体的帧数: {detection_count}")
    print(f"检测率: {detection_rate:.2%}")
    
    # 保存分析结果
    result_file = video_path.replace('.mp4', '_analysis.json').replace('.avi', '_analysis.json')
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump({
            'video_info': {
                'path': video_path,
                'width': width,
                'height': height,
                'fps': fps,
                'total_frames': total_frames
            },
            'statistics': {
                'total_frames': frame_count,
                'detection_count': detection_count,
                'detection_rate': detection_rate
            },
            'frame_results': analysis_results
        }, f, ensure_ascii=False, indent=2)
    
    print(f"分析结果已保存到: {result_file}")
    
    if output_path:
        print(f"标注视频已保存到: {output_path}")
    
    print("视频分析完成!")


if __name__ == '__main__':
    # 示例使用
    video_path = input("请输入视频文件路径: ").strip().strip('"')
    
    if not video_path:
        print("未提供视频路径")
        exit(1)
    
    # 生成输出路径
    base_name = os.path.splitext(video_path)[0]
    output_path = f"{base_name}_analyzed.mp4"
    
    # 分析视频
    analyze_simple_video(video_path, output_path)

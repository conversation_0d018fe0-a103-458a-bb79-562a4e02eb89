"""
背部视角的行走意图识别
专门针对从背后观察的简单行走场景
前半段直行，后半段左转
"""

import cv2
import numpy as np
import os
from collections import deque
from ultralytics import YOLO

class BackViewRecognizer:
    def __init__(self):
        # 意图类别
        self.intent_classes = ['STRAIGHT', 'LEFT', 'RIGHT', 'TURN_AROUND', 'STOP']
        self.intent_colors = [
            (0, 255, 0),    # STRAIGHT - 绿色
            (255, 0, 0),    # LEFT - 蓝色
            (0, 0, 255),    # RIGHT - 红色
            (255, 0, 255),  # TURN_AROUND - 紫色
            (0, 255, 255)   # STOP - 黄色
        ]
        
        # 背部视角的特殊考虑
        self.movement_threshold = 0.000005
        
        # 简化的阈值 - 针对简单场景
        self.straight_movement_std_threshold = 0.008  # 移动方向标准差
        self.left_turn_x_threshold = -0.005           # X方向累积移动（向左为负）
        self.significant_turn_threshold = 0.02       # 显著转向阈值
        
        # 时序数据缓冲区
        self.body_center_sequence = deque(maxlen=20)
        self.movement_direction_sequence = deque(maxlen=15)
        
        # 视频信息
        self.total_frames = 0
        
        # 加载姿态检测模型
        print("加载YOLOv8姿态检测模型...")
        self.model = YOLO('yolov8n-pose.pt')
        print("✓ 模型加载成功")
        print("背部视角逻辑: 移动方向稳定=直行, X方向偏移=转弯")
    
    def set_video_info(self, total_frames):
        """设置视频总帧数"""
        self.total_frames = total_frames
    
    def extract_features(self, keypoints, confidence):
        """提取背部视角的特征"""
        features = {}
        
        # 从背部视角，身体中心更重要
        if all(confidence[i] > 0.4 for i in [5, 6, 11, 12]):
            shoulder_center = (keypoints[5] + keypoints[6]) / 2
            hip_center = (keypoints[11] + keypoints[12]) / 2
            body_center = (shoulder_center + hip_center) / 2
            
            features['body_center'] = body_center
            
            # 肩膀宽度（从背后看，肩膀的相对位置很重要）
            shoulder_width = np.linalg.norm(keypoints[6] - keypoints[5])
            features['shoulder_width'] = shoulder_width
        
        return features
    
    def analyze_movement_pattern(self):
        """分析移动模式 - 背部视角专用"""
        if len(self.body_center_sequence) < 10:
            return None, {}
        
        centers = np.array(list(self.body_center_sequence))
        
        # 计算移动向量
        movements = []
        for i in range(1, len(centers)):
            movement = centers[i] - centers[i-1]
            movements.append(movement)
        
        if not movements:
            return None, {}
        
        movements = np.array(movements)
        
        # 关键分析指标
        # 1. 总体移动距离
        total_movement = np.sum([np.linalg.norm(m) for m in movements])
        
        # 2. X方向的累积移动（从背后看，左转意味着X坐标减小）
        cumulative_x_movement = np.sum(movements[:, 0])
        
        # 3. Y方向的累积移动（向前走Y坐标应该增加）
        cumulative_y_movement = np.sum(movements[:, 1])
        
        # 4. 移动方向的稳定性
        movement_angles = []
        for movement in movements:
            if np.linalg.norm(movement) > 1e-6:
                angle = np.degrees(np.arctan2(movement[1], movement[0]))
                movement_angles.append(angle)
        
        movement_angle_std = np.std(movement_angles) if movement_angles else 0
        avg_movement_angle = np.mean(movement_angles) if movement_angles else 0
        
        # 5. 最近的移动趋势（更重要）
        recent_movements = movements[-8:] if len(movements) >= 8 else movements
        recent_x_movement = np.sum(recent_movements[:, 0])
        recent_y_movement = np.sum(recent_movements[:, 1])
        
        analysis = {
            'total_movement': total_movement,
            'cumulative_x_movement': cumulative_x_movement,
            'cumulative_y_movement': cumulative_y_movement,
            'movement_angle_std': movement_angle_std,
            'avg_movement_angle': avg_movement_angle,
            'recent_x_movement': recent_x_movement,
            'recent_y_movement': recent_y_movement,
            'num_movements': len(movements)
        }
        
        # 判断逻辑 - 简化且针对背部视角
        intent = None
        confidence = 0.0
        reason = ""
        
        # 1. 检查是否在移动
        if total_movement < self.movement_threshold * len(movements):
            intent = 'STOP'
            confidence = 0.8
            reason = "insufficient_movement"
        
        # 2. 检查左转（X方向显著负移动）
        elif (recent_x_movement < self.left_turn_x_threshold or 
              cumulative_x_movement < self.significant_turn_threshold):
            intent = 'LEFT'
            confidence = min(0.9, abs(recent_x_movement) / 0.02 + abs(cumulative_x_movement) / 0.05)
            reason = f"left_movement (recent_x={recent_x_movement:.4f}, cum_x={cumulative_x_movement:.4f})"
        
        # 3. 检查右转（X方向显著正移动）
        elif (recent_x_movement > -self.left_turn_x_threshold or 
              cumulative_x_movement > -self.significant_turn_threshold):
            intent = 'RIGHT'
            confidence = min(0.9, abs(recent_x_movement) / 0.02 + abs(cumulative_x_movement) / 0.05)
            reason = f"right_movement (recent_x={recent_x_movement:.4f}, cum_x={cumulative_x_movement:.4f})"
        
        # 4. 检查直行（移动方向稳定，主要向前）
        elif (movement_angle_std < 30 and  # 方向稳定
              abs(recent_x_movement) < abs(recent_y_movement) and  # Y方向移动大于X方向
              recent_y_movement > 0):  # 向前移动
            intent = 'STRAIGHT'
            confidence = min(0.9, (1 - movement_angle_std / 30) * 0.7 + 
                           (recent_y_movement / (abs(recent_x_movement) + recent_y_movement)) * 0.3)
            reason = f"stable_forward (angle_std={movement_angle_std:.2f}, y_dom={recent_y_movement > abs(recent_x_movement)})"
        
        # 5. 默认情况
        else:
            intent = 'STRAIGHT'
            confidence = 0.5
            reason = "default_straight"
        
        analysis['intent'] = intent
        analysis['confidence'] = confidence
        analysis['reason'] = reason
        
        return intent, analysis
    
    def classify_intent_back_view(self, frame_num):
        """背部视角的意图分类"""
        
        # 分析移动模式
        intent, analysis = self.analyze_movement_pattern()
        
        if intent is None:
            return None, 0.0, {'reason': 'insufficient_data'}
        
        # 转换为数字ID
        intent_id = None
        if intent == 'STRAIGHT':
            intent_id = 0
        elif intent == 'LEFT':
            intent_id = 1
        elif intent == 'RIGHT':
            intent_id = 2
        elif intent == 'STOP':
            intent_id = 4
        
        return intent_id, analysis['confidence'], analysis
    
    def update_and_classify(self, keypoints, confidence, frame_num):
        """更新数据并分类"""
        
        # 提取特征
        features = self.extract_features(keypoints, confidence)
        
        # 更新历史数据
        if 'body_center' in features:
            self.body_center_sequence.append(features['body_center'])
        
        # 进行分类
        if len(self.body_center_sequence) >= 10:
            
            intent, confidence, debug_info = self.classify_intent_back_view(frame_num)
            
            # 添加进度信息
            progress = frame_num / self.total_frames if self.total_frames > 0 else 0
            debug_info['progress'] = progress
            debug_info['frame'] = frame_num
            
            return intent, confidence, debug_info
        
        return None, 0.0, {'reason': 'insufficient_data', 'frame': frame_num}

def analyze_video_back_view(video_path='left.mp4', output_path='left_back_view_analysis.mp4'):
    """
    使用背部视角方法分析视频
    """
    print(f"开始背部视角意图识别: {video_path}")
    print("专门针对: 从背后观察的简单行走场景")
    
    if not os.path.exists(video_path):
        print(f"错误: 视频文件不存在")
        return False
    
    # 创建识别器
    recognizer = BackViewRecognizer()
    
    # 打开视频
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("错误: 无法打开视频")
        return False
    
    # 视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    recognizer.set_video_info(total_frames)
    
    print(f"视频: {width}x{height}, {fps}FPS, {total_frames}帧")
    print(f"预期: 前{total_frames//2}帧直行, 后{total_frames-total_frames//2}帧左转")
    
    # 创建输出视频
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    frame_count = 0
    detection_count = 0
    analysis_count = 0
    intent_stats = {name: 0 for name in recognizer.intent_classes}
    intent_stats['ANALYZING'] = 0
    
    # 分段统计
    segment_stats = {'first_half': {}, 'second_half': {}}
    
    print("开始处理...")
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 姿态检测
            results = recognizer.model(frame, conf=0.3, verbose=False)
            
            annotated_frame = frame.copy()
            current_intent = None
            intent_confidence = 0.0
            debug_info = {}
            
            # 处理检测结果
            if len(results) > 0 and results[0].keypoints is not None:
                keypoints_data = results[0].keypoints.data
                
                if len(keypoints_data) > 0:
                    detection_count += 1
                    keypoints = keypoints_data[0]
                    
                    if keypoints.shape[0] == 17:
                        xy = keypoints[:, :2].cpu().numpy()
                        conf = keypoints[:, 2].cpu().numpy()
                        
                        # 归一化关键点
                        normalized_xy = xy.copy()
                        normalized_xy[:, 0] /= width
                        normalized_xy[:, 1] /= height
                        
                        # 背部视角分析
                        intent_id, confidence, debug_info = recognizer.update_and_classify(
                            normalized_xy, conf, frame_count
                        )
                        
                        if intent_id is not None:
                            current_intent = intent_id
                            intent_confidence = confidence
                            analysis_count += 1
                            
                            # 分段统计
                            segment = 'first_half' if frame_count <= total_frames // 2 else 'second_half'
                            intent_name = recognizer.intent_classes[intent_id]
                            segment_stats[segment][intent_name] = segment_stats[segment].get(intent_name, 0) + 1
                        
                        # 绘制关键点（突出背部视角的重要点）
                        for i, (x, y) in enumerate(xy):
                            if conf[i] > 0.3:
                                # 肩膀和髋部用特殊颜色（背部视角的关键点）
                                if i in [5, 6, 11, 12]:  # 肩膀和髋部
                                    color = (0, 255, 255)  # 黄色
                                    radius = 8
                                else:
                                    color = (0, 255, 0) if conf[i] > 0.7 else (0, 255, 255)
                                    radius = 6
                                cv2.circle(annotated_frame, (int(x), int(y)), radius, color, -1)
                        
                        # 绘制骨架
                        connections = [
                            (5, 6), (5, 7), (7, 9), (6, 8), (8, 10),
                            (5, 11), (6, 12), (11, 12),
                            (11, 13), (13, 15), (12, 14), (14, 16)
                        ]
                        
                        for start_idx, end_idx in connections:
                            if conf[start_idx] > 0.3 and conf[end_idx] > 0.3:
                                start_point = (int(xy[start_idx][0]), int(xy[start_idx][1]))
                                end_point = (int(xy[end_idx][0]), int(xy[end_idx][1]))
                                cv2.line(annotated_frame, start_point, end_point, (255, 0, 0), 3)
                        
                        # 绘制移动轨迹（重点突出）
                        if len(recognizer.body_center_sequence) >= 2:
                            trajectory_points = []
                            for center in recognizer.body_center_sequence:
                                point = center * np.array([width, height])
                                trajectory_points.append((int(point[0]), int(point[1])))
                            
                            # 绘制轨迹线（粗线突出）
                            for i in range(1, len(trajectory_points)):
                                cv2.line(annotated_frame, trajectory_points[i-1], trajectory_points[i], (255, 255, 0), 6)
                            
                            # 标记起点和当前点
                            if len(trajectory_points) >= 2:
                                cv2.circle(annotated_frame, trajectory_points[0], 12, (0, 255, 0), -1)  # 起点绿色
                                cv2.circle(annotated_frame, trajectory_points[-1], 12, (0, 0, 255), -1)  # 当前点红色
                                
                                # 绘制移动方向箭头
                                if len(trajectory_points) >= 5:
                                    start_arrow = trajectory_points[-5]
                                    end_arrow = trajectory_points[-1]
                                    cv2.arrowedLine(annotated_frame, start_arrow, end_arrow, (255, 0, 255), 4, tipLength=0.3)
            
            # 更新统计
            if current_intent is not None:
                intent_stats[recognizer.intent_classes[current_intent]] += 1
            else:
                intent_stats['ANALYZING'] += 1
            
            # 绘制信息面板
            panel_width = 950
            panel_height = 420
            
            # 半透明背景
            overlay = annotated_frame.copy()
            cv2.rectangle(overlay, (10, 10), (panel_width, panel_height), (0, 0, 0), -1)
            cv2.addWeighted(overlay, 0.85, annotated_frame, 0.15, 0, annotated_frame)
            
            # 白色边框
            cv2.rectangle(annotated_frame, (10, 10), (panel_width, panel_height), (255, 255, 255), 3)
            
            # 显示信息
            y = 40
            line_height = 25
            
            # 标题
            cv2.putText(annotated_frame, "Back-View Intent Recognition", 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (255, 255, 255), 2)
            y += line_height + 5
            
            # 进度信息
            progress = frame_count / total_frames * 100
            half = "前半段(预期直行)" if frame_count <= total_frames // 2 else "后半段(预期左转)"
            cv2.putText(annotated_frame, f'进度: {progress:.1f}% ({half})', 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            y += line_height
            
            # 进度条
            bar_width = 500
            bar_height = 15
            cv2.rectangle(annotated_frame, (25, y), (25 + bar_width, y + bar_height), (100, 100, 100), -1)
            cv2.rectangle(annotated_frame, (25, y), (25 + int(bar_width * progress / 100), y + bar_height), (0, 255, 0), -1)
            
            # 中点标记
            mid_x = 25 + bar_width // 2
            cv2.line(annotated_frame, (mid_x, y), (mid_x, y + bar_height), (255, 255, 255), 3)
            cv2.putText(annotated_frame, "直行|左转", (mid_x - 30, y - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
            
            y += line_height + 10
            
            # 意图显示
            if current_intent is not None:
                intent_text = f'检测意图: {recognizer.intent_classes[current_intent]}'
                confidence_text = f'置信度: {intent_confidence:.2f}'
                intent_color = recognizer.intent_colors[current_intent]
                
                # 检查是否符合预期
                expected_intent = 'STRAIGHT' if frame_count <= total_frames // 2 else 'LEFT'
                actual_intent = recognizer.intent_classes[current_intent]
                is_correct = (actual_intent == expected_intent)
                
                cv2.putText(annotated_frame, intent_text, 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.2, intent_color, 3)
                y += line_height + 5
                
                cv2.putText(annotated_frame, confidence_text, 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, intent_color, 2)
                y += line_height
                
                # 显示预期vs实际
                status_color = (0, 255, 0) if is_correct else (0, 0, 255)
                status_text = "✓ 符合预期" if is_correct else "✗ 不符合预期"
                cv2.putText(annotated_frame, f"预期: {expected_intent} | {status_text}", 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, status_color, 2)
                y += line_height
                
                # 显示分析详情
                if debug_info:
                    reason = debug_info.get('reason', 'unknown')
                    cv2.putText(annotated_frame, f"分析: {reason}", 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (200, 200, 200), 2)
                    y += line_height
                    
                    # 显示关键参数
                    if 'recent_x_movement' in debug_info:
                        cv2.putText(annotated_frame, f"X移动: {debug_info['recent_x_movement']:.4f}", 
                                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
                        y += 18
                    
                    if 'movement_angle_std' in debug_info:
                        cv2.putText(annotated_frame, f"方向稳定性: {debug_info['movement_angle_std']:.2f}°", 
                                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
                
                # 右上角大字显示
                intent_display = recognizer.intent_classes[current_intent]
                text_size = cv2.getTextSize(intent_display, cv2.FONT_HERSHEY_SIMPLEX, 2.5, 5)[0]
                
                cv2.rectangle(annotated_frame, 
                             (width - text_size[0] - 40, 20), 
                             (width - 20, 100), 
                             intent_color, -1)
                cv2.rectangle(annotated_frame, 
                             (width - text_size[0] - 40, 20), 
                             (width - 20, 100), 
                             (255, 255, 255), 3)
                cv2.putText(annotated_frame, intent_display, 
                           (width - text_size[0] - 30, 70), 
                           cv2.FONT_HERSHEY_SIMPLEX, 2.5, (255, 255, 255), 5)
            else:
                cv2.putText(annotated_frame, 'Status: 构建移动数据...', 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (128, 128, 128), 3)
            
            # 写入视频
            writer.write(annotated_frame)
            
            # 显示进度
            if frame_count % 100 == 0:
                progress_pct = frame_count / total_frames * 100
                current_intent_name = recognizer.intent_classes[current_intent] if current_intent is not None else 'ANALYZING'
                expected = "直行" if frame_count <= total_frames // 2 else "左转"
                print(f"进度: {progress_pct:.1f}% - 预期: {expected} - 检测: {current_intent_name}")
    
    except Exception as e:
        print(f"处理错误: {e}")
        return False
    
    finally:
        cap.release()
        writer.release()
    
    # 分段准确性分析
    print(f"\n=== 背部视角识别准确性分析 ===")
    
    for segment_name, stats in segment_stats.items():
        if stats:
            total_frames_in_segment = sum(stats.values())
            
            segment_display = "前半段" if segment_name == 'first_half' else "后半段"
            expected_intent = "STRAIGHT" if segment_name == 'first_half' else "LEFT"
            
            correct_frames = stats.get(expected_intent, 0)
            accuracy = correct_frames / total_frames_in_segment if total_frames_in_segment > 0 else 0
            
            print(f"{segment_display} (预期: {expected_intent}):")
            print(f"  准确率: {accuracy:.1%} ({correct_frames}/{total_frames_in_segment})")
            
            for intent, count in stats.items():
                ratio = count / total_frames_in_segment
                status = "✓" if intent == expected_intent else "✗"
                print(f"  {status} {intent}: {count}帧 ({ratio:.1%})")
    
    # 总体准确性
    first_half_correct = segment_stats['first_half'].get('STRAIGHT', 0)
    first_half_total = sum(segment_stats['first_half'].values()) if segment_stats['first_half'] else 1
    
    second_half_correct = segment_stats['second_half'].get('LEFT', 0)
    second_half_total = sum(segment_stats['second_half'].values()) if segment_stats['second_half'] else 1
    
    overall_correct = first_half_correct + second_half_correct
    overall_total = first_half_total + second_half_total
    overall_accuracy = overall_correct / overall_total if overall_total > 0 else 0
    
    print(f"\n总体准确率: {overall_accuracy:.1%} ({overall_correct}/{overall_total})")
    
    print(f"\n=== 背部视角意图识别完成 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测帧数: {detection_count}")
    print(f"分析帧数: {analysis_count}")
    print(f"检测率: {detection_count/frame_count*100:.1f}%")
    print(f"分析率: {analysis_count/frame_count*100:.1f}%")
    print(f"\n总体意图分布:")
    for intent, count in intent_stats.items():
        percentage = count / frame_count * 100 if frame_count > 0 else 0
        print(f"  {intent}: {count} 帧 ({percentage:.1f}%)")
    print(f"输出视频: {output_path}")
    
    return True

if __name__ == '__main__':
    success = analyze_video_back_view()
    if success:
        print("\n🎉 背部视角意图识别完成！")
        print("特点:")
        print("- ✅ 专门针对背部视角优化")
        print("- ✅ 基于X方向移动检测转弯")
        print("- ✅ 基于移动方向稳定性检测直行")
        print("- ✅ 准确性评估和预期对比")
    else:
        print("\n❌ 背部视角意图识别失败！")

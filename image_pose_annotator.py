"""
图片人体姿态关键点标注工具
输出COCO格式的关键点数据
"""

import cv2
import numpy as np
import os
import json
from ultralytics import YOLO
from datetime import datetime
import glob

class ImagePoseAnnotator:
    def __init__(self):
        # COCO关键点定义（17个关键点）
        self.coco_keypoints = [
            "nose",           # 0
            "left_eye",       # 1
            "right_eye",      # 2
            "left_ear",       # 3
            "right_ear",      # 4
            "left_shoulder",  # 5
            "right_shoulder", # 6
            "left_elbow",     # 7
            "right_elbow",    # 8
            "left_wrist",     # 9
            "right_wrist",    # 10
            "left_hip",       # 11
            "right_hip",      # 12
            "left_knee",      # 13
            "right_knee",     # 14
            "left_ankle",     # 15
            "right_ankle"     # 16
        ]
        
        # COCO骨架连接
        self.skeleton = [
            [16, 14], [14, 12], [17, 15], [15, 13], [12, 13],
            [6, 12], [7, 13], [6, 7], [6, 8], [7, 9],
            [8, 10], [9, 11], [2, 3], [1, 2], [1, 3],
            [2, 4], [3, 5], [4, 6], [5, 7]
        ]
        
        # 加载YOLOv8姿态检测模型
        print("加载YOLOv8n-pose模型...")
        try:
            self.model = YOLO('yolov8n-pose.pt')
            print("✓ 模型加载成功")
        except Exception as e:
            print(f"✗ 模型加载失败: {e}")
            raise e
    
    def annotate_single_image(self, image_path, output_dir=None, visualize=True):
        """
        标注单张图片的姿态关键点
        
        Args:
            image_path: 图片路径
            output_dir: 输出目录
            visualize: 是否生成可视化图片
        
        Returns:
            dict: COCO格式的标注数据
        """
        if not os.path.exists(image_path):
            print(f"错误: 图片文件不存在 - {image_path}")
            return None
        
        # 读取图片
        image = cv2.imread(image_path)
        if image is None:
            print(f"错误: 无法读取图片 - {image_path}")
            return None
        
        height, width = image.shape[:2]
        
        # 姿态检测
        results = self.model(image, conf=0.3, verbose=False)
        
        # 准备COCO格式数据
        image_info = {
            "id": 1,
            "width": width,
            "height": height,
            "file_name": os.path.basename(image_path)
        }
        
        annotations = []
        
        if len(results) > 0 and results[0].keypoints is not None:
            keypoints_data = results[0].keypoints.data
            
            for person_id, keypoints in enumerate(keypoints_data):
                if keypoints.shape[0] == 17:
                    xy = keypoints[:, :2].cpu().numpy()  # (17, 2)
                    conf = keypoints[:, 2].cpu().numpy()  # (17,)
                    
                    # 转换为COCO格式 [x1, y1, v1, x2, y2, v2, ...]
                    coco_keypoints = []
                    num_keypoints = 0
                    
                    for i in range(17):
                        x, y = xy[i]
                        visibility = 2 if conf[i] > 0.5 else 1 if conf[i] > 0.3 else 0
                        
                        coco_keypoints.extend([float(x), float(y), int(visibility)])
                        
                        if visibility > 0:
                            num_keypoints += 1
                    
                    # 计算边界框
                    visible_points = xy[conf > 0.3]
                    if len(visible_points) > 0:
                        x_min, y_min = np.min(visible_points, axis=0)
                        x_max, y_max = np.max(visible_points, axis=0)
                        
                        bbox_width = x_max - x_min
                        bbox_height = y_max - y_min
                        area = bbox_width * bbox_height
                        
                        annotation = {
                            "id": person_id + 1,
                            "image_id": 1,
                            "category_id": 1,  # person类别
                            "bbox": [float(x_min), float(y_min), float(bbox_width), float(bbox_height)],
                            "area": float(area),
                            "iscrowd": 0,
                            "keypoints": coco_keypoints,
                            "num_keypoints": num_keypoints
                        }
                        
                        annotations.append(annotation)
        
        # 构建完整的COCO格式数据
        coco_data = {
            "info": {
                "description": "Human Pose Keypoints Dataset",
                "version": "1.0",
                "year": datetime.now().year,
                "contributor": "YOLOv8n-pose",
                "date_created": datetime.now().isoformat()
            },
            "licenses": [
                {
                    "id": 1,
                    "name": "Unknown",
                    "url": ""
                }
            ],
            "images": [image_info],
            "annotations": annotations,
            "categories": [
                {
                    "id": 1,
                    "name": "person",
                    "supercategory": "person",
                    "keypoints": self.coco_keypoints,
                    "skeleton": self.skeleton
                }
            ]
        }
        
        # 保存结果
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            
            # 保存JSON标注
            base_name = os.path.splitext(os.path.basename(image_path))[0]
            json_path = os.path.join(output_dir, f"{base_name}_keypoints.json")
            
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(coco_data, f, indent=2, ensure_ascii=False)
            
            print(f"✓ 保存标注数据: {json_path}")
            
            # 生成可视化图片
            if visualize:
                vis_image = self.visualize_keypoints(image, annotations)
                vis_path = os.path.join(output_dir, f"{base_name}_visualized.jpg")
                cv2.imwrite(vis_path, vis_image)
                print(f"✓ 保存可视化图片: {vis_path}")
        
        return coco_data
    
    def visualize_keypoints(self, image, annotations):
        """
        可视化关键点
        """
        vis_image = image.copy()
        
        # 关键点颜色
        colors = [
            (255, 0, 0), (255, 85, 0), (255, 170, 0), (255, 255, 0),
            (170, 255, 0), (85, 255, 0), (0, 255, 0), (0, 255, 85),
            (0, 255, 170), (0, 255, 255), (0, 170, 255), (0, 85, 255),
            (0, 0, 255), (85, 0, 255), (170, 0, 255), (255, 0, 255),
            (255, 0, 170)
        ]
        
        for ann in annotations:
            keypoints = ann['keypoints']
            
            # 绘制关键点
            for i in range(0, len(keypoints), 3):
                x, y, v = keypoints[i], keypoints[i+1], keypoints[i+2]
                if v > 0:
                    color = colors[i//3 % len(colors)]
                    cv2.circle(vis_image, (int(x), int(y)), 5, color, -1)
                    cv2.putText(vis_image, str(i//3), (int(x)+5, int(y)), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
            
            # 绘制骨架
            for connection in self.skeleton:
                pt1_idx = (connection[0] - 1) * 3
                pt2_idx = (connection[1] - 1) * 3
                
                if (pt1_idx < len(keypoints) and pt2_idx < len(keypoints) and
                    keypoints[pt1_idx + 2] > 0 and keypoints[pt2_idx + 2] > 0):
                    
                    pt1 = (int(keypoints[pt1_idx]), int(keypoints[pt1_idx + 1]))
                    pt2 = (int(keypoints[pt2_idx]), int(keypoints[pt2_idx + 1]))
                    cv2.line(vis_image, pt1, pt2, (0, 255, 0), 2)
            
            # 绘制边界框
            bbox = ann['bbox']
            x, y, w, h = bbox
            cv2.rectangle(vis_image, (int(x), int(y)), (int(x+w), int(y+h)), (255, 0, 0), 2)
        
        return vis_image
    
    def annotate_batch_images(self, input_dir, output_dir, image_extensions=None):
        """
        批量标注图片
        
        Args:
            input_dir: 输入图片目录
            output_dir: 输出目录
            image_extensions: 支持的图片格式
        """
        if image_extensions is None:
            image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff']
        
        # 查找所有图片文件
        image_files = []
        for ext in image_extensions:
            image_files.extend(glob.glob(os.path.join(input_dir, ext)))
            image_files.extend(glob.glob(os.path.join(input_dir, ext.upper())))
        
        if not image_files:
            print(f"错误: 在目录 {input_dir} 中未找到图片文件")
            return
        
        print(f"找到 {len(image_files)} 张图片")
        
        os.makedirs(output_dir, exist_ok=True)
        
        # 批量处理
        all_annotations = []
        all_images = []
        
        for i, image_path in enumerate(image_files):
            print(f"处理 {i+1}/{len(image_files)}: {os.path.basename(image_path)}")
            
            coco_data = self.annotate_single_image(image_path, output_dir, visualize=True)
            
            if coco_data and coco_data['annotations']:
                # 更新ID
                for img in coco_data['images']:
                    img['id'] = i + 1
                for ann in coco_data['annotations']:
                    ann['image_id'] = i + 1
                    ann['id'] = len(all_annotations) + 1
                    all_annotations.append(ann)
                
                all_images.extend(coco_data['images'])
        
        # 保存合并的标注文件
        merged_coco = {
            "info": {
                "description": "Batch Human Pose Keypoints Dataset",
                "version": "1.0",
                "year": datetime.now().year,
                "contributor": "YOLOv8n-pose",
                "date_created": datetime.now().isoformat()
            },
            "licenses": [{"id": 1, "name": "Unknown", "url": ""}],
            "images": all_images,
            "annotations": all_annotations,
            "categories": [
                {
                    "id": 1,
                    "name": "person",
                    "supercategory": "person",
                    "keypoints": self.coco_keypoints,
                    "skeleton": self.skeleton
                }
            ]
        }
        
        merged_json_path = os.path.join(output_dir, "merged_keypoints.json")
        with open(merged_json_path, 'w', encoding='utf-8') as f:
            json.dump(merged_coco, f, indent=2, ensure_ascii=False)
        
        print(f"\n✓ 批量处理完成!")
        print(f"✓ 处理图片数: {len(image_files)}")
        print(f"✓ 检测到人体数: {len(all_annotations)}")
        print(f"✓ 合并标注文件: {merged_json_path}")

def main():
    """
    主函数 - 使用示例
    """
    annotator = ImagePoseAnnotator()
    
    print("人体姿态关键点标注工具")
    print("支持的操作:")
    print("1. 单张图片标注")
    print("2. 批量图片标注")
    
    choice = input("请选择操作 (1/2): ").strip()
    
    if choice == "1":
        image_path = input("请输入图片路径: ").strip()
        output_dir = input("请输入输出目录 (默认: ./output): ").strip() or "./output"
        
        result = annotator.annotate_single_image(image_path, output_dir)
        if result:
            print("✓ 单张图片标注完成!")
        else:
            print("✗ 标注失败!")
    
    elif choice == "2":
        input_dir = input("请输入图片目录: ").strip()
        output_dir = input("请输入输出目录 (默认: ./batch_output): ").strip() or "./batch_output"
        
        annotator.annotate_batch_images(input_dir, output_dir)
    
    else:
        print("无效选择!")

if __name__ == '__main__':
    main()

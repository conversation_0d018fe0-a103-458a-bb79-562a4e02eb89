"""
简化的转弯分析器 - 基于最直观的逻辑
"""

import cv2
import numpy as np
import os
from collections import deque
from ultralytics import YOL<PERSON>

def analyze_simple_turn(video_path='left.mp4', output_path='left_simple_turn.mp4'):
    """
    使用最简单直观的逻辑分析转弯
    """
    print(f"开始简化转弯分析: {video_path}")
    
    # 检查视频文件
    if not os.path.exists(video_path):
        print(f"错误: 视频文件不存在")
        return False
    
    # 加载模型
    print("加载YOLOv8模型...")
    try:
        model = YOLO('yolov8n-pose.pt')
        print("✓ 模型加载成功")
    except Exception as e:
        print(f"✗ 模型加载失败: {e}")
        return False
    
    # 打开视频
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("错误: 无法打开视频")
        return False
    
    # 视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"视频: {width}x{height}, {fps}FPS, {total_frames}帧")
    
    # 创建输出视频
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    # 意图设置
    intent_names = ['STRAIGHT', 'LEFT', 'RIGHT', 'TURN AROUND', 'STOP']
    intent_colors = [
        (0, 255, 0),    # STRAIGHT - 绿色
        (255, 0, 0),    # LEFT - 蓝色
        (0, 0, 255),    # RIGHT - 红色
        (255, 0, 255),  # TURN AROUND - 紫色
        (0, 255, 255)   # STOP - 黄色
    ]
    
    # 历史缓冲区
    position_history = deque(maxlen=20)  # 位置历史
    intent_history = deque(maxlen=5)     # 意图历史
    
    frame_count = 0
    detection_count = 0
    intent_stats = {name: 0 for name in intent_names}
    intent_stats['NOT_DETECTED'] = 0
    
    print("开始处理...")
    
    def simple_intent_analysis(pos_history):
        """
        最简单的意图分析
        """
        if len(pos_history) < 10:
            return None, 0.0
        
        # 取最近10帧的位置
        recent_positions = np.array(list(pos_history)[-10:])
        
        # 计算总体移动
        start_pos = recent_positions[0]
        end_pos = recent_positions[-1]
        total_movement = end_pos - start_pos
        
        # 计算移动距离
        movement_distance = np.linalg.norm(total_movement)
        
        # 如果移动距离很小，判断为停止
        if movement_distance < 0.02:  # 调整阈值
            return 4, 0.8  # STOP
        
        # 分析水平移动
        horizontal_movement = total_movement[0]
        
        # 设置更敏感的阈值
        if horizontal_movement < -0.015:  # 向左移动
            confidence = min(0.9, abs(horizontal_movement) * 30)
            return 1, confidence  # LEFT
        elif horizontal_movement > 0.015:  # 向右移动
            confidence = min(0.9, abs(horizontal_movement) * 30)
            return 2, confidence  # RIGHT
        else:
            return 0, 0.7  # STRAIGHT
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 姿态检测
            results = model(frame, conf=0.3, verbose=False)
            
            annotated_frame = frame.copy()
            
            current_intent = None
            intent_confidence = 0.0
            person_detected = False
            current_position = None
            
            # 处理检测结果
            if len(results) > 0 and results[0].keypoints is not None:
                keypoints_data = results[0].keypoints.data
                
                if len(keypoints_data) > 0:
                    person_detected = True
                    detection_count += 1
                    
                    keypoints = keypoints_data[0]
                    if keypoints.shape[0] == 17:
                        xy = keypoints[:, :2].cpu().numpy()
                        conf = keypoints[:, 2].cpu().numpy()
                        
                        # 计算身体中心（使用所有可见的关键点）
                        visible_points = []
                        for i in range(17):
                            if conf[i] > 0.3:
                                visible_points.append(xy[i])
                        
                        if len(visible_points) >= 5:
                            body_center = np.mean(visible_points, axis=0)
                            normalized_position = body_center / np.array([width, height])
                            current_position = normalized_position
                            
                            # 添加到历史
                            position_history.append(normalized_position)
                            
                            # 分析意图
                            intent_id, confidence = simple_intent_analysis(position_history)
                            
                            if intent_id is not None:
                                intent_history.append(intent_id)
                                
                                # 平滑预测
                                if len(intent_history) >= 3:
                                    # 使用最频繁的意图
                                    intent_counts = np.bincount(list(intent_history), minlength=5)
                                    current_intent = np.argmax(intent_counts)
                                    intent_confidence = confidence
                        
                        # 绘制关键点
                        for i, (x, y) in enumerate(xy):
                            if conf[i] > 0.3:
                                color = (0, 255, 0) if conf[i] > 0.7 else (0, 255, 255)
                                cv2.circle(annotated_frame, (int(x), int(y)), 5, color, -1)
                        
                        # 绘制骨架
                        connections = [
                            (5, 6), (5, 7), (7, 9), (6, 8), (8, 10),
                            (5, 11), (6, 12), (11, 12),
                            (11, 13), (13, 15), (12, 14), (14, 16)
                        ]
                        
                        for start_idx, end_idx in connections:
                            if conf[start_idx] > 0.3 and conf[end_idx] > 0.3:
                                start_point = (int(xy[start_idx][0]), int(xy[start_idx][1]))
                                end_point = (int(xy[end_idx][0]), int(xy[end_idx][1]))
                                cv2.line(annotated_frame, start_point, end_point, (255, 0, 0), 2)
                        
                        # 绘制身体中心
                        if current_position is not None:
                            center_pixel = current_position * np.array([width, height])
                            cv2.circle(annotated_frame, (int(center_pixel[0]), int(center_pixel[1])), 
                                     12, (255, 255, 0), -1)
                        
                        # 绘制移动轨迹
                        if len(position_history) >= 2:
                            for i in range(1, len(position_history)):
                                prev_pos = position_history[i-1] * np.array([width, height])
                                curr_pos = position_history[i] * np.array([width, height])
                                cv2.line(annotated_frame, 
                                        (int(prev_pos[0]), int(prev_pos[1])),
                                        (int(curr_pos[0]), int(curr_pos[1])),
                                        (0, 255, 255), 4)
                        
                        # 绘制移动方向箭头
                        if len(position_history) >= 5:
                            start_pos = position_history[-5] * np.array([width, height])
                            end_pos = position_history[-1] * np.array([width, height])
                            cv2.arrowedLine(annotated_frame,
                                          (int(start_pos[0]), int(start_pos[1])),
                                          (int(end_pos[0]), int(end_pos[1])),
                                          (255, 0, 255), 6, tipLength=0.3)
            
            # 更新统计
            if current_intent is not None:
                intent_stats[intent_names[current_intent]] += 1
            else:
                intent_stats['NOT_DETECTED'] += 1
            
            # 绘制信息面板
            panel_width = 650
            panel_height = 200
            
            # 半透明背景
            overlay = annotated_frame.copy()
            cv2.rectangle(overlay, (10, 10), (panel_width, panel_height), (0, 0, 0), -1)
            cv2.addWeighted(overlay, 0.85, annotated_frame, 0.15, 0, annotated_frame)
            
            # 白色边框
            cv2.rectangle(annotated_frame, (10, 10), (panel_width, panel_height), (255, 255, 255), 3)
            
            # 显示信息
            y = 45
            line_height = 30
            
            # 帧信息
            cv2.putText(annotated_frame, f'Frame: {frame_count}/{total_frames} ({frame_count/fps:.1f}s)', 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
            y += line_height
            
            # 检测状态
            if person_detected:
                cv2.putText(annotated_frame, 'Person: DETECTED', 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
                y += line_height
                
                # 位置信息
                if current_position is not None:
                    pos_text = f'Position: ({current_position[0]:.3f}, {current_position[1]:.3f})'
                    cv2.putText(annotated_frame, pos_text, 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                    y += line_height
                
                # 意图显示
                if current_intent is not None:
                    intent_text = f'INTENT: {intent_names[current_intent]}'
                    confidence_text = f'Confidence: {intent_confidence:.2f}'
                    intent_color = intent_colors[current_intent]
                    
                    # 显示意图
                    cv2.putText(annotated_frame, intent_text, 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.2, intent_color, 3)
                    y += line_height
                    
                    # 显示置信度
                    cv2.putText(annotated_frame, confidence_text, 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, intent_color, 2)
                    
                    # 右上角大字显示
                    intent_display = intent_names[current_intent]
                    text_size = cv2.getTextSize(intent_display, cv2.FONT_HERSHEY_SIMPLEX, 2.5, 5)[0]
                    
                    # 背景矩形
                    cv2.rectangle(annotated_frame, 
                                 (width - text_size[0] - 40, 20), 
                                 (width - 20, 100), 
                                 intent_color, -1)
                    
                    # 白色边框
                    cv2.rectangle(annotated_frame, 
                                 (width - text_size[0] - 40, 20), 
                                 (width - 20, 100), 
                                 (255, 255, 255), 3)
                    
                    # 意图文字
                    cv2.putText(annotated_frame, intent_display, 
                               (width - text_size[0] - 30, 70), 
                               cv2.FONT_HERSHEY_SIMPLEX, 2.5, (255, 255, 255), 5)
                else:
                    cv2.putText(annotated_frame, 'INTENT: ANALYZING...', 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (128, 128, 128), 3)
            else:
                cv2.putText(annotated_frame, 'Person: NOT DETECTED', 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
            
            # 写入视频
            writer.write(annotated_frame)
            
            # 显示进度
            if frame_count % 100 == 0:
                progress = frame_count / total_frames * 100
                current_intent_name = intent_names[current_intent] if current_intent is not None else 'NOT_DETECTED'
                print(f"进度: {progress:.1f}% - 当前意图: {current_intent_name}")
    
    except Exception as e:
        print(f"处理错误: {e}")
        return False
    
    finally:
        cap.release()
        writer.release()
    
    print(f"\n=== 简化转弯分析完成 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测帧数: {detection_count}")
    print(f"检测率: {detection_count/frame_count*100:.1f}%")
    print(f"\n意图分布:")
    for intent, count in intent_stats.items():
        percentage = count / frame_count * 100 if frame_count > 0 else 0
        print(f"  {intent}: {count} 帧 ({percentage:.1f}%)")
    print(f"输出视频: {output_path}")
    
    print(f"\n使用的逻辑:")
    print(f"1. 计算所有可见关键点的中心作为身体位置")
    print(f"2. 分析最近10帧的位置变化")
    print(f"3. 水平移动 < -0.015 → LEFT")
    print(f"4. 水平移动 > 0.015 → RIGHT")
    print(f"5. 移动距离 < 0.02 → STOP")
    print(f"6. 其他情况 → STRAIGHT")
    
    return True

if __name__ == '__main__':
    success = analyze_simple_turn()
    if success:
        print("\n🎉 简化转弯分析完成！")
        print("请查看 left_simple_turn.mp4")
        print("这个版本使用最直观的逻辑，应该能检测到左转！")
    else:
        print("\n❌ 简化转弯分析失败！")

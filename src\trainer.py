"""
模型训练和评估模块
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
import numpy as np
import os
import time
from typing import Dict, List, Tuple, Optional
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import json

from .model import DualLayerGRU


class EarlyStopping:
    """早停机制"""
    
    def __init__(self, patience: int = 15, min_delta: float = 0.001):
        self.patience = patience
        self.min_delta = min_delta
        self.counter = 0
        self.best_loss = float('inf')
        
    def __call__(self, val_loss: float) -> bool:
        if val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.counter = 0
        else:
            self.counter += 1
            
        return self.counter >= self.patience


class MetricsTracker:
    """指标跟踪器"""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        self.total_loss = 0.0
        self.intent_loss = 0.0
        self.pose_loss = 0.0
        self.correct_predictions = 0
        self.total_predictions = 0
        self.all_predictions = []
        self.all_targets = []
        
    def update(self, losses: Dict[str, torch.Tensor], 
               predictions: torch.Tensor, 
               targets: torch.Tensor):
        """更新指标"""
        batch_size = targets.size(0)
        
        # 损失
        self.total_loss += losses['total_loss'].item() * batch_size
        self.intent_loss += losses['intent_loss'].item() * batch_size
        self.pose_loss += losses['pose_loss'].item() * batch_size
        
        # 准确率
        pred_classes = torch.argmax(predictions, dim=1)
        self.correct_predictions += (pred_classes == targets.squeeze()).sum().item()
        self.total_predictions += batch_size
        
        # 保存预测和目标用于详细评估
        self.all_predictions.extend(pred_classes.cpu().numpy())
        self.all_targets.extend(targets.squeeze().cpu().numpy())
    
    def get_metrics(self) -> Dict[str, float]:
        """获取平均指标"""
        if self.total_predictions == 0:
            return {}
        
        accuracy = self.correct_predictions / self.total_predictions
        avg_total_loss = self.total_loss / self.total_predictions
        avg_intent_loss = self.intent_loss / self.total_predictions
        avg_pose_loss = self.pose_loss / self.total_predictions
        
        # 计算精确率、召回率、F1分数
        precision, recall, f1, _ = precision_recall_fscore_support(
            self.all_targets, self.all_predictions, average='weighted', zero_division=0
        )
        
        return {
            'accuracy': accuracy,
            'total_loss': avg_total_loss,
            'intent_loss': avg_intent_loss,
            'pose_loss': avg_pose_loss,
            'precision': precision,
            'recall': recall,
            'f1_score': f1
        }


class Trainer:
    """训练器"""
    
    def __init__(self, model: DualLayerGRU, config: Dict, device: torch.device):
        self.model = model.to(device)
        self.config = config
        self.device = device
        
        # 优化器
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=config['training']['learning_rate'],
            weight_decay=config['training']['weight_decay']
        )
        
        # 学习率调度器
        if config['training']['scheduler']['type'] == 'StepLR':
            self.scheduler = optim.lr_scheduler.StepLR(
                self.optimizer,
                step_size=config['training']['scheduler']['step_size'],
                gamma=config['training']['scheduler']['gamma']
            )
        else:
            self.scheduler = None
        
        # 早停
        self.early_stopping = EarlyStopping(
            patience=config['training']['early_stopping']['patience'],
            min_delta=config['training']['early_stopping']['min_delta']
        )
        
        # 创建输出目录
        os.makedirs(config['output']['model_dir'], exist_ok=True)
        os.makedirs(config['output']['results_dir'], exist_ok=True)
        os.makedirs(config['logging']['tensorboard_dir'], exist_ok=True)
        
        # TensorBoard
        self.writer = SummaryWriter(config['logging']['tensorboard_dir'])
        
        # 训练历史
        self.train_history = []
        self.val_history = []
        
        # 类别名称
        self.class_names = config['class_names']
    
    def train_epoch(self, train_loader: DataLoader) -> Dict[str, float]:
        """训练一个epoch"""
        self.model.train()
        metrics_tracker = MetricsTracker()
        
        pbar = tqdm(train_loader, desc='Training', leave=False)
        for batch_idx, (pose_seq, targets) in enumerate(pbar):
            pose_seq = pose_seq.to(self.device)
            targets = targets.to(self.device)
            
            # 前向传播
            self.optimizer.zero_grad()
            outputs = self.model(pose_seq)
            losses = self.model.compute_loss(outputs, targets, pose_seq)
            
            # 反向传播
            losses['total_loss'].backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(
                self.model.parameters(), 
                max_norm=self.config['training']['grad_clip_norm']
            )
            
            self.optimizer.step()
            
            # 更新指标
            metrics_tracker.update(losses, outputs['intent_logits'], targets)
            
            # 更新进度条
            pbar.set_postfix({
                'Loss': f"{losses['total_loss'].item():.4f}",
                'Acc': f"{metrics_tracker.correct_predictions/metrics_tracker.total_predictions:.4f}"
            })
        
        return metrics_tracker.get_metrics()
    
    def validate_epoch(self, val_loader: DataLoader) -> Dict[str, float]:
        """验证一个epoch"""
        self.model.eval()
        metrics_tracker = MetricsTracker()
        
        with torch.no_grad():
            for pose_seq, targets in tqdm(val_loader, desc='Validation', leave=False):
                pose_seq = pose_seq.to(self.device)
                targets = targets.to(self.device)
                
                # 前向传播
                outputs = self.model(pose_seq)
                losses = self.model.compute_loss(outputs, targets, pose_seq)
                
                # 更新指标
                metrics_tracker.update(losses, outputs['intent_logits'], targets)
        
        return metrics_tracker.get_metrics()
    
    def train(self, train_loader: DataLoader, val_loader: DataLoader) -> Dict[str, List[float]]:
        """完整训练过程"""
        print("开始训练...")
        print(f"训练集大小: {len(train_loader.dataset)}")
        print(f"验证集大小: {len(val_loader.dataset)}")
        
        best_val_loss = float('inf')
        best_model_state = None
        start_time = time.time()
        
        for epoch in range(self.config['training']['num_epochs']):
            print(f"\nEpoch {epoch + 1}/{self.config['training']['num_epochs']}")
            
            # 训练
            train_metrics = self.train_epoch(train_loader)
            
            # 验证
            val_metrics = self.validate_epoch(val_loader)
            
            # 学习率调度
            if self.scheduler:
                self.scheduler.step()
            
            # 记录历史
            self.train_history.append(train_metrics)
            self.val_history.append(val_metrics)
            
            # TensorBoard记录
            self._log_metrics(epoch, train_metrics, val_metrics)
            
            # 打印指标
            print(f"Train - Loss: {train_metrics['total_loss']:.4f}, "
                  f"Acc: {train_metrics['accuracy']:.4f}, "
                  f"F1: {train_metrics['f1_score']:.4f}")
            print(f"Val   - Loss: {val_metrics['total_loss']:.4f}, "
                  f"Acc: {val_metrics['accuracy']:.4f}, "
                  f"F1: {val_metrics['f1_score']:.4f}")
            
            # 保存最佳模型
            if val_metrics['total_loss'] < best_val_loss:
                best_val_loss = val_metrics['total_loss']
                best_model_state = self.model.state_dict().copy()
                self.save_checkpoint(epoch, val_metrics, is_best=True)
                print("✓ 保存最佳模型")
            
            # 定期保存检查点
            if (epoch + 1) % 10 == 0:
                self.save_checkpoint(epoch, val_metrics, is_best=False)
            
            # 早停检查
            if self.early_stopping(val_metrics['total_loss']):
                print(f"Early stopping at epoch {epoch + 1}")
                break
        
        # 加载最佳模型
        if best_model_state:
            self.model.load_state_dict(best_model_state)
        
        training_time = time.time() - start_time
        print(f"\n训练完成! 总用时: {training_time:.2f}秒")
        
        return {
            'train_history': self.train_history,
            'val_history': self.val_history
        }
    
    def evaluate(self, test_loader: DataLoader) -> Dict[str, float]:
        """评估模型"""
        print("开始评估...")
        
        self.model.eval()
        metrics_tracker = MetricsTracker()
        
        with torch.no_grad():
            for pose_seq, targets in tqdm(test_loader, desc='Testing'):
                pose_seq = pose_seq.to(self.device)
                targets = targets.to(self.device)
                
                outputs = self.model(pose_seq)
                losses = self.model.compute_loss(outputs, targets, pose_seq)
                
                metrics_tracker.update(losses, outputs['intent_logits'], targets)
        
        test_metrics = metrics_tracker.get_metrics()
        
        # 生成混淆矩阵
        self._plot_confusion_matrix(
            metrics_tracker.all_targets, 
            metrics_tracker.all_predictions
        )
        
        # 保存评估结果
        self._save_evaluation_results(test_metrics, metrics_tracker)
        
        print("评估完成!")
        print(f"Test Accuracy: {test_metrics['accuracy']:.4f}")
        print(f"Test F1 Score: {test_metrics['f1_score']:.4f}")
        print(f"Test Precision: {test_metrics['precision']:.4f}")
        print(f"Test Recall: {test_metrics['recall']:.4f}")
        
        return test_metrics

    def _log_metrics(self, epoch: int, train_metrics: Dict[str, float], val_metrics: Dict[str, float]):
        """记录指标到TensorBoard"""
        for metric_name, value in train_metrics.items():
            self.writer.add_scalar(f'Train/{metric_name}', value, epoch)

        for metric_name, value in val_metrics.items():
            self.writer.add_scalar(f'Validation/{metric_name}', value, epoch)

        # 记录学习率
        current_lr = self.optimizer.param_groups[0]['lr']
        self.writer.add_scalar('Learning_Rate', current_lr, epoch)

    def _plot_confusion_matrix(self, y_true: List[int], y_pred: List[int]):
        """绘制混淆矩阵"""
        cm = confusion_matrix(y_true, y_pred)

        plt.figure(figsize=(10, 8))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=self.class_names,
                   yticklabels=self.class_names)
        plt.title('Confusion Matrix', fontsize=16)
        plt.ylabel('True Label', fontsize=14)
        plt.xlabel('Predicted Label', fontsize=14)
        plt.tight_layout()

        # 保存图片
        save_path = os.path.join(self.config['output']['results_dir'], 'confusion_matrix.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"混淆矩阵已保存到: {save_path}")

    def _save_evaluation_results(self, test_metrics: Dict[str, float], metrics_tracker):
        """保存评估结果"""
        results = {
            'test_metrics': test_metrics,
            'predictions': [int(x) for x in metrics_tracker.all_predictions],  # 转换为Python int
            'targets': [int(x) for x in metrics_tracker.all_targets],  # 转换为Python int
            'class_names': self.class_names
        }

        save_path = os.path.join(self.config['output']['results_dir'], 'evaluation_results.json')
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        print(f"评估结果已保存到: {save_path}")

    def save_checkpoint(self, epoch: int, metrics: Dict[str, float], is_best: bool = False):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'metrics': metrics,
            'config': self.config
        }

        if is_best:
            save_path = os.path.join(self.config['output']['model_dir'], 'best_model.pth')
            torch.save(checkpoint, save_path)

        save_path = os.path.join(self.config['output']['model_dir'], f'checkpoint_epoch_{epoch}.pth')
        torch.save(checkpoint, save_path)

    def load_checkpoint(self, checkpoint_path: str):
        """加载检查点"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)

        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

        return checkpoint['epoch'], checkpoint['metrics']

    def plot_training_history(self):
        """绘制训练历史"""
        if not self.train_history or not self.val_history:
            return

        epochs = range(1, len(self.train_history) + 1)

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # 损失
        axes[0, 0].plot(epochs, [h['total_loss'] for h in self.train_history], 'b-', label='Train', linewidth=2)
        axes[0, 0].plot(epochs, [h['total_loss'] for h in self.val_history], 'r-', label='Validation', linewidth=2)
        axes[0, 0].set_title('Total Loss', fontsize=14)
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # 准确率
        axes[0, 1].plot(epochs, [h['accuracy'] for h in self.train_history], 'b-', label='Train', linewidth=2)
        axes[0, 1].plot(epochs, [h['accuracy'] for h in self.val_history], 'r-', label='Validation', linewidth=2)
        axes[0, 1].set_title('Accuracy', fontsize=14)
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Accuracy')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # F1分数
        axes[1, 0].plot(epochs, [h['f1_score'] for h in self.train_history], 'b-', label='Train', linewidth=2)
        axes[1, 0].plot(epochs, [h['f1_score'] for h in self.val_history], 'r-', label='Validation', linewidth=2)
        axes[1, 0].set_title('F1 Score', fontsize=14)
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('F1 Score')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # 意图损失
        axes[1, 1].plot(epochs, [h['intent_loss'] for h in self.train_history], 'b-', label='Train', linewidth=2)
        axes[1, 1].plot(epochs, [h['intent_loss'] for h in self.val_history], 'r-', label='Validation', linewidth=2)
        axes[1, 1].set_title('Intent Loss', fontsize=14)
        axes[1, 1].set_xlabel('Epoch')
        axes[1, 1].set_ylabel('Loss')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图片
        save_path = os.path.join(self.config['output']['results_dir'], 'training_history.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"训练历史图已保存到: {save_path}")

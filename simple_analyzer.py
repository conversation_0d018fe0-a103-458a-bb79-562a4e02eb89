"""
简化版视频分析器 - 稳定版本
"""

def main():
    print("开始简化版视频分析...")
    
    try:
        import cv2
        print("✓ OpenCV 导入成功")
    except ImportError as e:
        print(f"✗ OpenCV 导入失败: {e}")
        return
    
    try:
        import numpy as np
        print("✓ NumPy 导入成功")
    except ImportError as e:
        print(f"✗ NumPy 导入失败: {e}")
        return
    
    try:
        from ultralytics import YOLO
        print("✓ Ultralytics 导入成功")
    except ImportError as e:
        print(f"✗ Ultralytics 导入失败: {e}")
        print("请确保在正确的conda环境中安装了ultralytics")
        return
    
    # 检查视频文件
    video_path = 'left.mp4'
    if not cv2.os.path.exists(video_path):
        print(f"✗ 视频文件不存在: {video_path}")
        return
    
    print(f"✓ 视频文件存在: {video_path}")
    
    # 加载模型
    print("正在加载YOLOv8姿态检测模型...")
    try:
        model = YOLO('yolov8n-pose.pt')
        print("✓ 模型加载成功")
    except Exception as e:
        print(f"✗ 模型加载失败: {e}")
        return
    
    # 打开视频
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"✗ 无法打开视频文件")
        return
    
    # 获取视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"视频信息: {width}x{height}, {fps}FPS, {total_frames}帧")
    
    # 创建输出视频
    output_path = 'left_simple_analysis.mp4'
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    if not writer.isOpened():
        print("✗ 无法创建输出视频文件")
        cap.release()
        return
    
    print(f"开始处理视频，输出到: {output_path}")
    
    # 处理视频
    frame_count = 0
    detection_count = 0
    pose_history = []
    
    # 意图类别和颜色
    intent_classes = ['直行', '左转', '右转', '停止']
    intent_colors = [(0, 255, 0), (255, 0, 0), (0, 0, 255), (0, 255, 255)]
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 每10帧显示一次进度，避免刷屏
            if frame_count % 10 == 0:
                progress = frame_count / total_frames * 100
                print(f"处理进度: {progress:.1f}%")
            
            # 姿态检测
            results = model(frame, conf=0.5, verbose=False)
            
            # 创建标注帧
            annotated_frame = frame.copy()
            
            current_intent = None
            intent_confidence = 0.0
            
            # 处理检测结果
            if len(results) > 0 and results[0].keypoints is not None:
                keypoints_data = results[0].keypoints.data
                
                if len(keypoints_data) > 0:
                    detection_count += 1
                    keypoints = keypoints_data[0]
                    
                    if keypoints.shape[0] == 17:
                        xy = keypoints[:, :2].cpu().numpy()
                        conf = keypoints[:, 2].cpu().numpy()
                        
                        # 绘制关键点
                        for i, (x, y) in enumerate(xy):
                            if conf[i] > 0.3:
                                color = (0, 255, 0) if conf[i] > 0.7 else (0, 255, 255)
                                cv2.circle(annotated_frame, (int(x), int(y)), 4, color, -1)
                        
                        # 绘制骨架
                        connections = [
                            (5, 6), (5, 7), (7, 9), (6, 8), (8, 10),  # 上身
                            (5, 11), (6, 12), (11, 12),  # 躯干
                            (11, 13), (13, 15), (12, 14), (14, 16)  # 下身
                        ]
                        
                        for start_idx, end_idx in connections:
                            if conf[start_idx] > 0.3 and conf[end_idx] > 0.3:
                                start_point = (int(xy[start_idx][0]), int(xy[start_idx][1]))
                                end_point = (int(xy[end_idx][0]), int(xy[end_idx][1]))
                                cv2.line(annotated_frame, start_point, end_point, (255, 0, 0), 2)
                        
                        # 简化的意图判断
                        if conf[11] > 0.5 and conf[12] > 0.5:  # 髋部可见
                            hip_center = np.mean(xy[[11, 12]], axis=0)
                            normalized_center = hip_center / np.array([width, height])
                            
                            pose_history.append(normalized_center)
                            
                            # 保持历史长度
                            if len(pose_history) > 10:
                                pose_history.pop(0)
                            
                            # 意图判断
                            if len(pose_history) >= 5:
                                movement = pose_history[-1] - pose_history[0]
                                
                                if abs(movement[0]) < 0.01:
                                    current_intent = 3  # 停止
                                    intent_confidence = 0.8
                                elif movement[0] < -0.02:
                                    current_intent = 1  # 左转
                                    intent_confidence = 0.9
                                elif movement[0] > 0.02:
                                    current_intent = 2  # 右转
                                    intent_confidence = 0.9
                                else:
                                    current_intent = 0  # 直行
                                    intent_confidence = 0.7
            
            # 绘制信息面板
            panel_height = 120
            cv2.rectangle(annotated_frame, (10, 10), (400, panel_height), (0, 0, 0), -1)
            cv2.rectangle(annotated_frame, (10, 10), (400, panel_height), (255, 255, 255), 2)
            
            # 显示信息
            y = 35
            cv2.putText(annotated_frame, f'Frame: {frame_count}/{total_frames}', 
                       (20, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            y += 25
            
            if detection_count > 0:
                cv2.putText(annotated_frame, 'Person: DETECTED', 
                           (20, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                y += 25
                
                if current_intent is not None:
                    intent_text = f'Intent: {intent_classes[current_intent]}'
                    intent_color = intent_colors[current_intent]
                    cv2.putText(annotated_frame, intent_text, 
                               (20, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, intent_color, 2)
                    
                    # 右上角大字显示
                    cv2.putText(annotated_frame, intent_classes[current_intent], 
                               (width-200, 50), cv2.FONT_HERSHEY_SIMPLEX, 1.5, intent_color, 3)
                else:
                    cv2.putText(annotated_frame, 'Intent: ANALYZING...', 
                               (20, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (128, 128, 128), 2)
            else:
                cv2.putText(annotated_frame, 'Person: NOT DETECTED', 
                           (20, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
            
            # 写入视频
            writer.write(annotated_frame)
    
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
    
    finally:
        cap.release()
        writer.release()
        print("视频处理完成")
    
    # 输出结果
    detection_rate = detection_count / frame_count if frame_count > 0 else 0
    print(f"\n=== 分析结果 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测帧数: {detection_count}")
    print(f"检测率: {detection_rate:.2%}")
    print(f"输出视频: {output_path}")

if __name__ == '__main__':
    main()

"""
系统测试脚本
验证各个模块是否正常工作
"""

import torch
import numpy as np
import os
import sys
import traceback
from typing import Dict, Any

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from src.data_processor import PoseDataProcessor, load_config
        from src.model import create_model, DualLayerGRU
        from src.trainer import Trainer
        from src.inference import RealTimeInference
        print("✓ 所有模块导入成功")
        return True
    except Exception as e:
        print(f"✗ 模块导入失败: {e}")
        return False


def test_config_loading():
    """测试配置文件加载"""
    print("测试配置文件加载...")
    
    try:
        from src.data_processor import load_config
        config = load_config('config.yaml')
        
        # 检查必要的配置项
        required_keys = ['data', 'model', 'training', 'yolo']
        for key in required_keys:
            if key not in config:
                raise ValueError(f"配置文件缺少必要项: {key}")
        
        print("✓ 配置文件加载成功")
        return True, config
    except Exception as e:
        print(f"✗ 配置文件加载失败: {e}")
        return False, None


def test_data_processor(config: Dict):
    """测试数据处理器"""
    print("测试数据处理器...")
    
    try:
        from src.data_processor import PoseDataProcessor
        
        processor = PoseDataProcessor(config)
        
        # 生成测试数据
        seq_len = 10
        num_keypoints = 17
        pose_sequence = np.random.randn(seq_len, num_keypoints, 2) * 0.1
        confidence = np.random.uniform(0.3, 1.0, (seq_len, num_keypoints))
        
        # 测试遮挡处理
        completed_pose = processor.handle_occlusion(pose_sequence, confidence)
        assert completed_pose.shape == pose_sequence.shape
        
        # 测试归一化
        normalized_pose = processor.normalize_pose(completed_pose)
        assert normalized_pose.shape == pose_sequence.shape
        
        # 测试运动特征提取
        motion_features = processor.extract_motion_features(normalized_pose)
        assert motion_features.shape == (seq_len, num_keypoints, 6)
        
        # 测试数据增强
        augmented_data = processor.augment_data(normalized_pose, label=1)
        assert len(augmented_data) > 1
        
        print("✓ 数据处理器测试通过")
        return True
    except Exception as e:
        print(f"✗ 数据处理器测试失败: {e}")
        traceback.print_exc()
        return False


def test_model_creation(config: Dict):
    """测试模型创建"""
    print("测试模型创建...")
    
    try:
        from src.model import create_model
        
        model = create_model(config)
        
        # 检查模型类型
        from src.model import DualLayerGRU
        assert isinstance(model, DualLayerGRU)
        
        # 测试前向传播
        batch_size = 2
        seq_len = config['data']['sequence_length']
        num_keypoints = config['data']['pose_keypoints']
        
        dummy_input = torch.randn(batch_size, seq_len, num_keypoints, 6)
        
        model.eval()
        with torch.no_grad():
            outputs = model(dummy_input)
        
        # 检查输出
        assert 'intent_logits' in outputs
        assert 'reconstructed_pose' in outputs
        assert outputs['intent_logits'].shape == (batch_size, config['data']['num_classes'])
        
        print("✓ 模型创建和前向传播测试通过")
        return True, model
    except Exception as e:
        print(f"✗ 模型测试失败: {e}")
        traceback.print_exc()
        return False, None


def test_loss_computation(config: Dict, model):
    """测试损失计算"""
    print("测试损失计算...")
    
    try:
        batch_size = 2
        seq_len = config['data']['sequence_length']
        num_keypoints = config['data']['pose_keypoints']
        
        # 生成测试数据
        dummy_input = torch.randn(batch_size, seq_len, num_keypoints, 6)
        dummy_targets = torch.randint(0, config['data']['num_classes'], (batch_size, 1))
        
        # 前向传播
        outputs = model(dummy_input)
        
        # 计算损失
        losses = model.compute_loss(outputs, dummy_targets, dummy_input)
        
        # 检查损失
        required_losses = ['total_loss', 'intent_loss', 'pose_loss']
        for loss_name in required_losses:
            assert loss_name in losses
            assert isinstance(losses[loss_name], torch.Tensor)
            assert losses[loss_name].item() >= 0
        
        print("✓ 损失计算测试通过")
        return True
    except Exception as e:
        print(f"✗ 损失计算测试失败: {e}")
        traceback.print_exc()
        return False


def test_trainer_creation(config: Dict, model):
    """测试训练器创建"""
    print("测试训练器创建...")
    
    try:
        from src.trainer import Trainer
        
        device = torch.device('cpu')  # 使用CPU进行测试
        trainer = Trainer(model, config, device)
        
        # 检查训练器属性
        assert trainer.model is not None
        assert trainer.optimizer is not None
        assert trainer.early_stopping is not None
        
        print("✓ 训练器创建测试通过")
        return True
    except Exception as e:
        print(f"✗ 训练器创建测试失败: {e}")
        traceback.print_exc()
        return False


def test_data_generation():
    """测试数据生成"""
    print("测试数据生成...")
    
    try:
        # 创建临时数据目录
        temp_dir = 'temp_test_data'
        os.makedirs(temp_dir, exist_ok=True)
        
        # 运行数据生成
        from main import create_sample_data
        from src.data_processor import load_config
        
        config = load_config('config.yaml')
        create_sample_data(config, temp_dir)
        
        # 检查生成的文件
        expected_files = ['train.json', 'val.json', 'test.json']
        for filename in expected_files:
            filepath = os.path.join(temp_dir, filename)
            assert os.path.exists(filepath), f"文件不存在: {filepath}"
            
            # 检查文件内容
            import json
            with open(filepath, 'r') as f:
                data = json.load(f)
            assert len(data) > 0, f"文件为空: {filepath}"
        
        print("✓ 数据生成测试通过")
        
        # 清理临时文件
        import shutil
        shutil.rmtree(temp_dir)
        
        return True
    except Exception as e:
        print(f"✗ 数据生成测试失败: {e}")
        traceback.print_exc()
        return False


def test_dataset_loading(config: Dict):
    """测试数据集加载"""
    print("测试数据集加载...")
    
    try:
        from src.data_processor import PoseIntentDataset
        import json
        
        # 创建临时测试数据
        test_data = []
        for i in range(10):
            seq_len = config['data']['sequence_length']
            num_keypoints = config['data']['pose_keypoints']
            
            pose_seq = np.random.randn(seq_len, num_keypoints, 2).tolist()
            confidence = np.random.uniform(0.5, 1.0, (seq_len, num_keypoints)).tolist()
            label = i % 4
            
            test_data.append({
                'pose_sequence': pose_seq,
                'confidence': confidence,
                'label': label
            })
        
        # 保存临时数据
        temp_file = 'temp_test.json'
        with open(temp_file, 'w') as f:
            json.dump(test_data, f)
        
        # 创建数据集
        dataset = PoseIntentDataset(temp_file, config, mode='test')
        
        # 检查数据集
        assert len(dataset) > 0
        
        # 测试数据加载
        sample_data, sample_label = dataset[0]
        assert isinstance(sample_data, torch.Tensor)
        assert isinstance(sample_label, torch.Tensor)
        
        print("✓ 数据集加载测试通过")
        
        # 清理临时文件
        os.remove(temp_file)
        
        return True
    except Exception as e:
        print(f"✗ 数据集加载测试失败: {e}")
        traceback.print_exc()
        return False


def test_device_compatibility():
    """测试设备兼容性"""
    print("测试设备兼容性...")
    
    try:
        # 检查CUDA可用性
        cuda_available = torch.cuda.is_available()
        print(f"  CUDA可用: {cuda_available}")
        
        if cuda_available:
            print(f"  CUDA设备数量: {torch.cuda.device_count()}")
            print(f"  当前CUDA设备: {torch.cuda.current_device()}")
            print(f"  设备名称: {torch.cuda.get_device_name()}")
        
        # 测试tensor创建
        cpu_tensor = torch.randn(10, 10)
        assert cpu_tensor.device.type == 'cpu'
        
        if cuda_available:
            gpu_tensor = torch.randn(10, 10).cuda()
            assert gpu_tensor.device.type == 'cuda'
        
        print("✓ 设备兼容性测试通过")
        return True
    except Exception as e:
        print(f"✗ 设备兼容性测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("=" * 60)
    print("人体姿态意图识别系统测试")
    print("=" * 60)
    
    test_results = []
    
    # 1. 测试模块导入
    test_results.append(("模块导入", test_imports()))
    
    # 2. 测试配置加载
    config_success, config = test_config_loading()
    test_results.append(("配置加载", config_success))
    
    if not config_success:
        print("配置加载失败，跳过后续测试")
        return
    
    # 3. 测试数据处理器
    test_results.append(("数据处理器", test_data_processor(config)))
    
    # 4. 测试模型创建
    model_success, model = test_model_creation(config)
    test_results.append(("模型创建", model_success))
    
    if model_success:
        # 5. 测试损失计算
        test_results.append(("损失计算", test_loss_computation(config, model)))
        
        # 6. 测试训练器创建
        test_results.append(("训练器创建", test_trainer_creation(config, model)))
    
    # 7. 测试数据生成
    test_results.append(("数据生成", test_data_generation()))
    
    # 8. 测试数据集加载
    test_results.append(("数据集加载", test_dataset_loading(config)))
    
    # 9. 测试设备兼容性
    test_results.append(("设备兼容性", test_device_compatibility()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常使用。")
    else:
        print("⚠️  部分测试失败，请检查错误信息。")
    
    print("=" * 60)


if __name__ == '__main__':
    run_all_tests()

"""
分段感知的意图识别
专门识别"前半段直走，后半段左转"的模式
"""

import cv2
import numpy as np
import os
from collections import deque
from ultralytics import YOLO

class SegmentAwareRecognizer:
    def __init__(self):
        # 意图类别
        self.intent_classes = ['STRAIGHT', 'LEFT', 'RIGHT', 'TURN_AROUND', 'STOP']
        self.intent_colors = [
            (0, 255, 0),    # STRAIGHT - 绿色
            (255, 0, 0),    # LEFT - 蓝色
            (0, 0, 255),    # RIGHT - 红色
            (255, 0, 255),  # TURN_AROUND - 紫色
            (0, 255, 255)   # STOP - 黄色
        ]
        
        # 分段参数
        self.total_frames = 0
        self.current_frame = 0
        
        # 基于调试数据的阈值
        self.movement_threshold = 0.000005
        
        # 不同的阈值用于不同阶段
        self.early_stage_angle_threshold = 0.02   # 前期更严格，倾向于直行
        self.late_stage_angle_threshold = 0.06    # 后期更敏感，容易检测转弯
        
        # 时序数据缓冲区
        self.body_center_sequence = deque(maxlen=20)
        self.body_angle_sequence = deque(maxlen=20)
        
        # 分段统计
        self.segment_data = {
            'early': {'frames': [], 'intents': []},    # 前25%
            'mid_early': {'frames': [], 'intents': []}, # 25%-50%
            'mid_late': {'frames': [], 'intents': []},  # 50%-75%
            'late': {'frames': [], 'intents': []}       # 后25%
        }
        
        # 加载姿态检测模型
        print("加载YOLOv8姿态检测模型...")
        self.model = YOLO('yolov8n-pose.pt')
        print("✓ 模型加载成功")
    
    def set_video_info(self, total_frames):
        """设置视频总帧数"""
        self.total_frames = total_frames
        print(f"视频总帧数: {total_frames}")
        print(f"分段: 前25% (1-{total_frames//4}), 25%-50% ({total_frames//4+1}-{total_frames//2})")
        print(f"      50%-75% ({total_frames//2+1}-{total_frames*3//4}), 后25% ({total_frames*3//4+1}-{total_frames})")
    
    def get_current_stage(self, frame_num):
        """获取当前所处的阶段"""
        if self.total_frames == 0:
            return 'unknown'
        
        progress = frame_num / self.total_frames
        
        if progress <= 0.25:
            return 'early'
        elif progress <= 0.5:
            return 'mid_early'
        elif progress <= 0.75:
            return 'mid_late'
        else:
            return 'late'
    
    def extract_features(self, keypoints, confidence):
        """提取基本特征"""
        features = {}
        
        # 身体中心和角度
        if all(confidence[i] > 0.4 for i in [5, 6, 11, 12]):
            shoulder_center = (keypoints[5] + keypoints[6]) / 2
            hip_center = (keypoints[11] + keypoints[12]) / 2
            body_center = (shoulder_center + hip_center) / 2
            
            # 身体朝向角度
            body_vector = shoulder_center - hip_center
            body_angle = np.degrees(np.arctan2(body_vector[1], body_vector[0]))
            
            features['body_center'] = body_center
            features['body_angle'] = body_angle
        
        return features
    
    def analyze_movement(self):
        """分析移动状态"""
        if len(self.body_center_sequence) < 3:
            return False, 0.0
        
        centers = np.array(list(self.body_center_sequence)[-5:])  # 最近5帧
        
        movements = []
        for i in range(1, len(centers)):
            movement = np.linalg.norm(centers[i] - centers[i-1])
            movements.append(movement)
        
        avg_movement = np.mean(movements) if movements else 0
        is_moving = avg_movement > self.movement_threshold
        
        return is_moving, avg_movement
    
    def analyze_direction_change(self, stage):
        """根据阶段分析方向变化"""
        if len(self.body_angle_sequence) < 8:
            return None, 0.0, {}
        
        angles = list(self.body_angle_sequence)[-8:]  # 最近8帧
        
        # 计算角度变化
        angle_changes = []
        for i in range(1, len(angles)):
            change = angles[i] - angles[i-1]
            
            # 处理角度跨越
            if change > 180:
                change -= 360
            elif change < -180:
                change += 360
            
            angle_changes.append(change)
        
        if not angle_changes:
            return None, 0.0, {}
        
        # 计算统计量
        total_change = sum(angle_changes)
        avg_change = np.mean(angle_changes)
        abs_avg_change = np.mean([abs(c) for c in angle_changes])
        
        analysis = {
            'total_change': total_change,
            'avg_change': avg_change,
            'abs_avg_change': abs_avg_change,
            'stage': stage
        }
        
        # 根据阶段使用不同的阈值
        if stage in ['early', 'mid_early']:
            # 前半段：更严格的阈值，倾向于直行
            angle_threshold = self.early_stage_angle_threshold
            significant_threshold = 0.1
        else:
            # 后半段：更敏感的阈值，容易检测转弯
            angle_threshold = self.late_stage_angle_threshold
            significant_threshold = 0.08
        
        # 判断方向
        direction = None
        confidence = 0.0
        
        # 显著变化判断
        if abs(total_change) > significant_threshold:
            if total_change < 0:
                direction = 'LEFT'
                confidence = min(0.9, abs(total_change) / 0.3)
            else:
                direction = 'RIGHT'
                confidence = min(0.9, abs(total_change) / 0.3)
        
        # 平均变化判断
        elif abs(avg_change) > angle_threshold:
            if avg_change < 0:
                direction = 'LEFT'
                confidence = min(0.8, abs(avg_change) / 0.15)
            else:
                direction = 'RIGHT'
                confidence = min(0.8, abs(avg_change) / 0.15)
        
        return direction, confidence, analysis
    
    def classify_intent_stage_aware(self, frame_num):
        """基于阶段的意图分类"""
        
        # 1. 获取当前阶段
        stage = self.get_current_stage(frame_num)
        
        # 2. 检查移动状态
        is_moving, movement_strength = self.analyze_movement()
        
        if not is_moving:
            return 4, 0.8, {'stage': stage, 'reason': 'not_moving'}
        
        # 3. 分析方向变化
        direction, direction_confidence, direction_analysis = self.analyze_direction_change(stage)
        
        # 4. 基于阶段和方向进行分类
        if direction and direction_confidence > 0.3:
            if direction == 'LEFT':
                # 在前半段，需要更高的置信度才认为是左转
                if stage in ['early', 'mid_early'] and direction_confidence < 0.6:
                    return 0, 0.7, {
                        'stage': stage, 
                        'reason': 'early_stage_bias_to_straight',
                        'direction_analysis': direction_analysis
                    }
                else:
                    return 1, direction_confidence, {
                        'stage': stage,
                        'reason': 'left_turn_detected',
                        'direction_analysis': direction_analysis
                    }
            elif direction == 'RIGHT':
                # 同样的逻辑应用于右转
                if stage in ['early', 'mid_early'] and direction_confidence < 0.6:
                    return 0, 0.7, {
                        'stage': stage,
                        'reason': 'early_stage_bias_to_straight',
                        'direction_analysis': direction_analysis
                    }
                else:
                    return 2, direction_confidence, {
                        'stage': stage,
                        'reason': 'right_turn_detected',
                        'direction_analysis': direction_analysis
                    }
        
        # 5. 默认为直行
        if is_moving:
            return 0, 0.8, {
                'stage': stage,
                'reason': 'straight_walking',
                'direction_analysis': direction_analysis
            }
        
        return None, 0.0, {'stage': stage, 'reason': 'unknown'}
    
    def update_and_classify(self, keypoints, confidence, frame_num):
        """更新数据并分类"""
        self.current_frame = frame_num
        
        # 提取特征
        features = self.extract_features(keypoints, confidence)
        
        # 更新历史数据
        if 'body_center' in features:
            self.body_center_sequence.append(features['body_center'])
        
        if 'body_angle' in features:
            self.body_angle_sequence.append(features['body_angle'])
        
        # 进行分类
        if (len(self.body_center_sequence) >= 3 and 
            len(self.body_angle_sequence) >= 8):
            
            intent, confidence, debug_info = self.classify_intent_stage_aware(frame_num)
            
            # 记录分段数据
            stage = self.get_current_stage(frame_num)
            if intent is not None:
                self.segment_data[stage]['frames'].append(frame_num)
                self.segment_data[stage]['intents'].append(intent)
            
            return intent, confidence, debug_info
        
        return None, 0.0, {'stage': self.get_current_stage(frame_num), 'reason': 'insufficient_data'}

def analyze_video_segment_aware(video_path='left.mp4', output_path='left_segment_aware_analysis.mp4'):
    """
    使用分段感知方法分析视频
    """
    print(f"开始分段感知意图识别: {video_path}")
    print("特点: 前半段倾向直行，后半段敏感转弯")
    
    if not os.path.exists(video_path):
        print(f"错误: 视频文件不存在")
        return False
    
    # 创建识别器
    recognizer = SegmentAwareRecognizer()
    
    # 打开视频
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("错误: 无法打开视频")
        return False
    
    # 视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    # 设置视频信息到识别器
    recognizer.set_video_info(total_frames)
    
    print(f"视频: {width}x{height}, {fps}FPS, {total_frames}帧")
    
    # 创建输出视频
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    frame_count = 0
    detection_count = 0
    analysis_count = 0
    intent_stats = {name: 0 for name in recognizer.intent_classes}
    intent_stats['ANALYZING'] = 0
    
    print("开始处理...")
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 姿态检测
            results = recognizer.model(frame, conf=0.3, verbose=False)
            
            annotated_frame = frame.copy()
            current_intent = None
            intent_confidence = 0.0
            debug_info = {}
            
            # 处理检测结果
            if len(results) > 0 and results[0].keypoints is not None:
                keypoints_data = results[0].keypoints.data
                
                if len(keypoints_data) > 0:
                    detection_count += 1
                    keypoints = keypoints_data[0]
                    
                    if keypoints.shape[0] == 17:
                        xy = keypoints[:, :2].cpu().numpy()
                        conf = keypoints[:, 2].cpu().numpy()
                        
                        # 归一化关键点
                        normalized_xy = xy.copy()
                        normalized_xy[:, 0] /= width
                        normalized_xy[:, 1] /= height
                        
                        # 分段感知分析
                        intent_id, confidence, debug_info = recognizer.update_and_classify(
                            normalized_xy, conf, frame_count
                        )
                        
                        if intent_id is not None:
                            current_intent = intent_id
                            intent_confidence = confidence
                            analysis_count += 1
                        
                        # 绘制关键点
                        for i, (x, y) in enumerate(xy):
                            if conf[i] > 0.3:
                                color = (0, 255, 0) if conf[i] > 0.7 else (0, 255, 255)
                                cv2.circle(annotated_frame, (int(x), int(y)), 6, color, -1)
                        
                        # 绘制骨架
                        connections = [
                            (5, 6), (5, 7), (7, 9), (6, 8), (8, 10),
                            (5, 11), (6, 12), (11, 12),
                            (11, 13), (13, 15), (12, 14), (14, 16)
                        ]
                        
                        for start_idx, end_idx in connections:
                            if conf[start_idx] > 0.3 and conf[end_idx] > 0.3:
                                start_point = (int(xy[start_idx][0]), int(xy[start_idx][1]))
                                end_point = (int(xy[end_idx][0]), int(xy[end_idx][1]))
                                cv2.line(annotated_frame, start_point, end_point, (255, 0, 0), 3)
                        
                        # 绘制身体中心轨迹
                        if len(recognizer.body_center_sequence) >= 2:
                            for i in range(1, len(recognizer.body_center_sequence)):
                                prev_center = recognizer.body_center_sequence[i-1] * np.array([width, height])
                                curr_center = recognizer.body_center_sequence[i] * np.array([width, height])
                                cv2.line(annotated_frame,
                                        (int(prev_center[0]), int(prev_center[1])),
                                        (int(curr_center[0]), int(curr_center[1])),
                                        (255, 255, 0), 4)
            
            # 更新统计
            if current_intent is not None:
                intent_stats[recognizer.intent_classes[current_intent]] += 1
            else:
                intent_stats['ANALYZING'] += 1
            
            # 绘制信息面板
            panel_width = 900
            panel_height = 350
            
            # 半透明背景
            overlay = annotated_frame.copy()
            cv2.rectangle(overlay, (10, 10), (panel_width, panel_height), (0, 0, 0), -1)
            cv2.addWeighted(overlay, 0.85, annotated_frame, 0.15, 0, annotated_frame)
            
            # 白色边框
            cv2.rectangle(annotated_frame, (10, 10), (panel_width, panel_height), (255, 255, 255), 3)
            
            # 显示信息
            y = 40
            line_height = 25
            
            # 标题
            cv2.putText(annotated_frame, "Stage-Aware Intent Recognition", 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (255, 255, 255), 2)
            y += line_height + 5
            
            # 当前阶段信息
            stage = recognizer.get_current_stage(frame_count)
            progress = frame_count / total_frames * 100
            stage_names = {
                'early': '前25% (倾向直行)',
                'mid_early': '25%-50% (倾向直行)', 
                'mid_late': '50%-75% (敏感转弯)',
                'late': '后25% (敏感转弯)'
            }
            
            cv2.putText(annotated_frame, f'Stage: {stage_names.get(stage, stage)} ({progress:.1f}%)', 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
            y += line_height
            
            # 进度条
            bar_width = 500
            bar_height = 15
            cv2.rectangle(annotated_frame, (25, y), (25 + bar_width, y + bar_height), (100, 100, 100), -1)
            cv2.rectangle(annotated_frame, (25, y), (25 + int(bar_width * progress / 100), y + bar_height), (0, 255, 0), -1)
            
            # 分段标记
            for i in range(1, 4):
                mark_x = 25 + int(bar_width * i / 4)
                cv2.line(annotated_frame, (mark_x, y), (mark_x, y + bar_height), (255, 255, 255), 2)
            
            y += line_height + 10
            
            # 意图显示
            if current_intent is not None:
                intent_text = f'Intent: {recognizer.intent_classes[current_intent]}'
                confidence_text = f'Confidence: {intent_confidence:.2f}'
                intent_color = recognizer.intent_colors[current_intent]
                
                cv2.putText(annotated_frame, intent_text, 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.2, intent_color, 3)
                y += line_height + 5
                
                cv2.putText(annotated_frame, confidence_text, 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, intent_color, 2)
                y += line_height
                
                # 显示分析原因
                if debug_info:
                    reason = debug_info.get('reason', 'unknown')
                    cv2.putText(annotated_frame, f"Reason: {reason}", 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (200, 200, 200), 2)
                    y += line_height
                    
                    # 显示方向分析
                    direction_analysis = debug_info.get('direction_analysis', {})
                    if 'avg_change' in direction_analysis:
                        cv2.putText(annotated_frame, f"Angle Change: {direction_analysis['avg_change']:.4f}°", 
                                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
                
                # 右上角大字显示
                intent_display = recognizer.intent_classes[current_intent]
                text_size = cv2.getTextSize(intent_display, cv2.FONT_HERSHEY_SIMPLEX, 2.5, 5)[0]
                
                cv2.rectangle(annotated_frame, 
                             (width - text_size[0] - 40, 20), 
                             (width - 20, 100), 
                             intent_color, -1)
                cv2.rectangle(annotated_frame, 
                             (width - text_size[0] - 40, 20), 
                             (width - 20, 100), 
                             (255, 255, 255), 3)
                cv2.putText(annotated_frame, intent_display, 
                           (width - text_size[0] - 30, 70), 
                           cv2.FONT_HERSHEY_SIMPLEX, 2.5, (255, 255, 255), 5)
            else:
                cv2.putText(annotated_frame, 'Status: Building stage data...', 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (128, 128, 128), 3)
            
            # 写入视频
            writer.write(annotated_frame)
            
            # 显示进度
            if frame_count % 100 == 0:
                progress_pct = frame_count / total_frames * 100
                current_intent_name = recognizer.intent_classes[current_intent] if current_intent is not None else 'ANALYZING'
                print(f"进度: {progress_pct:.1f}% - 阶段: {stage} - 意图: {current_intent_name}")
    
    except Exception as e:
        print(f"处理错误: {e}")
        return False
    
    finally:
        cap.release()
        writer.release()
    
    # 分段统计分析
    print(f"\n=== 分段统计分析 ===")
    
    for stage_name, stage_data in recognizer.segment_data.items():
        if stage_data['intents']:
            intent_counts = {}
            for intent in stage_data['intents']:
                intent_name = recognizer.intent_classes[intent]
                intent_counts[intent_name] = intent_counts.get(intent_name, 0) + 1
            
            total_frames_in_stage = len(stage_data['intents'])
            main_intent = max(intent_counts.keys(), key=lambda x: intent_counts[x])
            main_intent_ratio = intent_counts[main_intent] / total_frames_in_stage
            
            stage_names = {
                'early': '前25%',
                'mid_early': '25%-50%',
                'mid_late': '50%-75%',
                'late': '后25%'
            }
            
            print(f"{stage_names[stage_name]}: 主要意图 = {main_intent} ({main_intent_ratio:.1%})")
            for intent, count in intent_counts.items():
                ratio = count / total_frames_in_stage
                print(f"  {intent}: {count}帧 ({ratio:.1%})")
    
    print(f"\n=== 分段感知意图识别完成 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测帧数: {detection_count}")
    print(f"分析帧数: {analysis_count}")
    print(f"检测率: {detection_count/frame_count*100:.1f}%")
    print(f"分析率: {analysis_count/frame_count*100:.1f}%")
    print(f"\n总体意图分布:")
    for intent, count in intent_stats.items():
        percentage = count / frame_count * 100 if frame_count > 0 else 0
        print(f"  {intent}: {count} 帧 ({percentage:.1f}%)")
    print(f"输出视频: {output_path}")
    
    return True

if __name__ == '__main__':
    success = analyze_video_segment_aware()
    if success:
        print("\n🎉 分段感知意图识别完成！")
        print("特点:")
        print("- ✅ 前半段倾向于识别直行")
        print("- ✅ 后半段敏感于转弯检测")
        print("- ✅ 分段统计分析")
        print("- ✅ 阶段感知的阈值调整")
    else:
        print("\n❌ 分段感知意图识别失败！")

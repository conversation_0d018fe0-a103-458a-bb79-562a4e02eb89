"""
改进的人体姿态标注工具
专门处理背部视角，确保17个关键点完整性
"""

import cv2
import numpy as np
import os
import json
from ultralytics import YOLO
from datetime import datetime

class ImprovedPoseAnnotator:
    def __init__(self):
        # 5种意图类别
        self.intent_classes = {
            0: "straight",      # 直行
            1: "left",          # 左转
            2: "right",         # 右转
            3: "turn_around",   # 转身
            4: "stop"           # 停止
        }
        
        self.intent_names = {
            0: "直行", 1: "左转", 2: "右转", 3: "转身", 4: "停止"
        }
        
        # COCO关键点定义（17个）
        self.coco_keypoints = [
            "nose", "left_eye", "right_eye", "left_ear", "right_ear",
            "left_shoulder", "right_shoulder", "left_elbow", "right_elbow",
            "left_wrist", "right_wrist", "left_hip", "right_hip",
            "left_knee", "right_knee", "left_ankle", "right_ankle"
        ]
        
        # 背部视角的关键点重要性权重
        self.keypoint_importance = {
            # 面部关键点（背部视角通常不可见）
            0: 0.1,  # nose
            1: 0.1,  # left_eye
            2: 0.1,  # right_eye
            3: 0.3,  # left_ear
            4: 0.3,  # right_ear
            # 上身关键点（重要）
            5: 1.0,  # left_shoulder
            6: 1.0,  # right_shoulder
            7: 0.9,  # left_elbow
            8: 0.9,  # right_elbow
            9: 0.8,  # left_wrist
            10: 0.8, # right_wrist
            # 下身关键点（最重要）
            11: 1.0, # left_hip
            12: 1.0, # right_hip
            13: 1.0, # left_knee
            14: 1.0, # right_knee
            15: 1.0, # left_ankle
            16: 1.0  # right_ankle
        }
        
        # 加载YOLOv8姿态检测模型
        print("加载YOLOv8n-pose模型...")
        try:
            self.model = YOLO('yolov8n-pose.pt')
            print("✓ 模型加载成功")
        except Exception as e:
            print(f"✗ 模型加载失败: {e}")
            raise e
    
    def enhance_keypoints_for_back_view(self, keypoints, confidence):
        """
        针对背部视角增强关键点检测
        """
        enhanced_keypoints = keypoints.copy()
        enhanced_confidence = confidence.copy()
        
        # 如果面部关键点不可见，尝试从其他关键点推断
        if confidence[0] < 0.3:  # 鼻子不可见
            # 从肩膀中心推断头部位置
            if confidence[5] > 0.5 and confidence[6] > 0.5:
                shoulder_center = (keypoints[5] + keypoints[6]) / 2
                # 估算头部位置（在肩膀上方）
                head_offset = np.array([0, -80])  # 向上80像素
                estimated_nose = shoulder_center + head_offset
                enhanced_keypoints[0] = estimated_nose
                enhanced_confidence[0] = 0.4  # 设置为低置信度但可见
        
        # 如果耳朵不可见，从肩膀推断
        if confidence[3] < 0.3 and confidence[5] > 0.5:  # 左耳
            left_ear_offset = np.array([-30, -50])
            enhanced_keypoints[3] = keypoints[5] + left_ear_offset
            enhanced_confidence[3] = 0.4
            
        if confidence[4] < 0.3 and confidence[6] > 0.5:  # 右耳
            right_ear_offset = np.array([30, -50])
            enhanced_keypoints[4] = keypoints[6] + right_ear_offset
            enhanced_confidence[4] = 0.4
        
        return enhanced_keypoints, enhanced_confidence
    
    def annotate_with_enhanced_keypoints(self, image_path, intent_label, output_dir=None, visualize=True):
        """
        使用增强关键点进行标注
        """
        if not os.path.exists(image_path):
            print(f"错误: 图片文件不存在 - {image_path}")
            return None
        
        # 处理意图标签
        if isinstance(intent_label, str):
            intent_mapping = {v: k for k, v in self.intent_classes.items()}
            if intent_label in intent_mapping:
                intent_id = intent_mapping[intent_label]
            else:
                print(f"错误: 未知的意图标签 - {intent_label}")
                return None
        else:
            intent_id = intent_label
        
        if intent_id not in self.intent_classes:
            print(f"错误: 意图标签必须在 0-4 之间，当前: {intent_id}")
            return None
        
        # 读取图片
        image = cv2.imread(image_path)
        if image is None:
            print(f"错误: 无法读取图片 - {image_path}")
            return None
        
        height, width = image.shape[:2]
        
        # 姿态检测
        results = self.model(image, conf=0.25, verbose=False)  # 降低置信度阈值
        
        # 准备数据
        image_info = {
            "id": 1,
            "width": width,
            "height": height,
            "file_name": os.path.basename(image_path),
            "intent_label": intent_id,
            "intent_name": self.intent_classes[intent_id],
            "intent_chinese": self.intent_names[intent_id],
            "view_type": "back_view"  # 标记为背部视角
        }
        
        annotations = []
        
        if len(results) > 0 and results[0].keypoints is not None:
            keypoints_data = results[0].keypoints.data
            
            for person_id, keypoints in enumerate(keypoints_data):
                if keypoints.shape[0] == 17:
                    xy = keypoints[:, :2].cpu().numpy()
                    conf = keypoints[:, 2].cpu().numpy()
                    
                    # 增强关键点检测
                    enhanced_xy, enhanced_conf = self.enhance_keypoints_for_back_view(xy, conf)
                    
                    # 转换为COCO格式
                    coco_keypoints = []
                    num_keypoints = 0
                    quality_score = 0.0
                    
                    for i in range(17):
                        x, y = enhanced_xy[i]
                        original_conf = enhanced_conf[i]
                        
                        # 根据背部视角调整可见性判断
                        if original_conf > 0.5:
                            visibility = 2  # 完全可见
                        elif original_conf > 0.25:
                            visibility = 1  # 部分遮挡
                        else:
                            visibility = 0  # 不可见
                        
                        coco_keypoints.extend([float(x), float(y), int(visibility)])
                        
                        if visibility > 0:
                            num_keypoints += 1
                            # 计算质量分数（考虑关键点重要性）
                            quality_score += self.keypoint_importance[i] * original_conf
                    
                    # 计算边界框
                    visible_points = enhanced_xy[enhanced_conf > 0.25]
                    if len(visible_points) > 0:
                        x_min, y_min = np.min(visible_points, axis=0)
                        x_max, y_max = np.max(visible_points, axis=0)
                        bbox_width = x_max - x_min
                        bbox_height = y_max - y_min
                        area = bbox_width * bbox_height
                        
                        annotation = {
                            "id": person_id + 1,
                            "image_id": 1,
                            "category_id": 1,
                            "bbox": [float(x_min), float(y_min), float(bbox_width), float(bbox_height)],
                            "area": float(area),
                            "iscrowd": 0,
                            "keypoints": coco_keypoints,
                            "num_keypoints": num_keypoints,
                            "intent_label": intent_id,
                            "intent_name": self.intent_classes[intent_id],
                            "quality_score": float(quality_score),
                            "view_type": "back_view",
                            "enhanced": True  # 标记为增强过的数据
                        }
                        
                        annotations.append(annotation)
        
        # 构建完整数据
        pose_intent_data = {
            "info": {
                "description": "Enhanced Human Pose Keypoints with Intent Labels (Back View)",
                "version": "1.1",
                "year": datetime.now().year,
                "contributor": "Enhanced YOLOv8n-pose + Intent Annotation",
                "date_created": datetime.now().isoformat(),
                "view_type": "back_view"
            },
            "images": [image_info],
            "annotations": annotations,
            "intent_classes": self.intent_classes,
            "keypoint_importance": self.keypoint_importance,
            "categories": [
                {
                    "id": 1,
                    "name": "person",
                    "supercategory": "person",
                    "keypoints": self.coco_keypoints
                }
            ]
        }
        
        # 保存结果
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            
            base_name = os.path.splitext(os.path.basename(image_path))[0]
            json_path = os.path.join(output_dir, f"{base_name}_enhanced_intent_{self.intent_classes[intent_id]}.json")
            
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(pose_intent_data, f, indent=2, ensure_ascii=False)
            
            print(f"✓ 保存增强标注数据: {json_path}")
            
            # 生成可视化图片
            if visualize:
                vis_image = self.visualize_enhanced_pose(image, annotations, intent_id)
                vis_path = os.path.join(output_dir, f"{base_name}_enhanced_intent_{self.intent_classes[intent_id]}_vis.jpg")
                cv2.imwrite(vis_path, vis_image)
                print(f"✓ 保存增强可视化图片: {vis_path}")
        
        return pose_intent_data
    
    def visualize_enhanced_pose(self, image, annotations, intent_id):
        """
        可视化增强的姿态关键点
        """
        vis_image = image.copy()
        height, width = image.shape[:2]
        
        # 意图颜色
        intent_colors = {
            0: (0, 255, 0),    # 直行 - 绿色
            1: (255, 0, 0),    # 左转 - 蓝色
            2: (0, 0, 255),    # 右转 - 红色
            3: (255, 0, 255),  # 转身 - 紫色
            4: (0, 255, 255)   # 停止 - 黄色
        }
        
        intent_color = intent_colors.get(intent_id, (255, 255, 255))
        
        for ann in annotations:
            keypoints = ann['keypoints']
            
            # 绘制关键点
            for i in range(0, len(keypoints), 3):
                x, y, v = keypoints[i], keypoints[i+1], keypoints[i+2]
                if v > 0:
                    # 根据关键点重要性选择颜色
                    importance = self.keypoint_importance[i//3]
                    if importance >= 1.0:
                        color = (0, 255, 0)  # 重要关键点 - 绿色
                    elif importance >= 0.8:
                        color = (0, 255, 255)  # 中等重要 - 黄色
                    else:
                        color = (255, 0, 0)  # 低重要性 - 蓝色
                    
                    # 根据可见性调整大小
                    radius = 8 if v == 2 else 6
                    cv2.circle(vis_image, (int(x), int(y)), radius, color, -1)
                    cv2.circle(vis_image, (int(x), int(y)), radius, (255, 255, 255), 2)
                    
                    # 显示关键点编号
                    cv2.putText(vis_image, str(i//3), (int(x)+10, int(y)), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
            
            # 绘制骨架
            skeleton = [
                (0, 1), (0, 2), (1, 3), (2, 4),  # 头部
                (3, 5), (4, 6),  # 耳朵到肩膀
                (5, 6),          # 肩膀连接
                (5, 7), (7, 9),  # 左臂
                (6, 8), (8, 10), # 右臂
                (5, 11), (6, 12), # 肩膀到髋部
                (11, 12),         # 髋部连接
                (11, 13), (13, 15), # 左腿
                (12, 14), (14, 16)  # 右腿
            ]
            
            for start_idx, end_idx in skeleton:
                start_pt_idx = start_idx * 3
                end_pt_idx = end_idx * 3
                
                if (start_pt_idx < len(keypoints) and end_pt_idx < len(keypoints) and
                    keypoints[start_pt_idx + 2] > 0 and keypoints[end_pt_idx + 2] > 0):
                    
                    start_pt = (int(keypoints[start_pt_idx]), int(keypoints[start_pt_idx + 1]))
                    end_pt = (int(keypoints[end_pt_idx]), int(keypoints[end_pt_idx + 1]))
                    cv2.line(vis_image, start_pt, end_pt, intent_color, 3)
        
        # 添加信息面板
        panel_height = 220
        panel_width = 650
        
        # 半透明背景
        overlay = vis_image.copy()
        cv2.rectangle(overlay, (10, 10), (panel_width, panel_height), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.85, vis_image, 0.15, 0, vis_image)
        
        # 白色边框
        cv2.rectangle(vis_image, (10, 10), (panel_width, panel_height), (255, 255, 255), 3)
        
        # 显示信息
        y = 40
        line_height = 28
        
        # 标题
        cv2.putText(vis_image, "Enhanced Back View Pose Annotation", 
                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
        y += line_height
        
        # 意图信息
        intent_text = f"Intent: {self.intent_names[intent_id]} ({self.intent_classes[intent_id]})"
        cv2.putText(vis_image, intent_text, 
                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.9, intent_color, 3)
        y += line_height
        
        # 关键点统计
        if annotations:
            ann = annotations[0]
            cv2.putText(vis_image, f"Keypoints: {ann['num_keypoints']}/17 visible", 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            y += line_height
            
            cv2.putText(vis_image, f"Quality Score: {ann['quality_score']:.2f}", 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            y += line_height
            
            cv2.putText(vis_image, "Enhanced: YES (Back View Optimized)", 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        # 右上角显示意图
        intent_display = self.intent_classes[intent_id].upper()
        text_size = cv2.getTextSize(intent_display, cv2.FONT_HERSHEY_SIMPLEX, 2.0, 4)[0]
        
        cv2.rectangle(vis_image, 
                     (width - text_size[0] - 40, 20), 
                     (width - 20, 80), 
                     intent_color, -1)
        cv2.rectangle(vis_image, 
                     (width - text_size[0] - 40, 20), 
                     (width - 20, 80), 
                     (255, 255, 255), 3)
        cv2.putText(vis_image, intent_display, 
                   (width - text_size[0] - 30, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 2.0, (255, 255, 255), 4)
        
        return vis_image

def main():
    """
    主函数
    """
    annotator = ImprovedPoseAnnotator()
    
    print("🚀 改进的人体姿态 + 意图标注工具 (背部视角优化)")
    print("支持的意图类别:")
    for class_id, class_name in annotator.intent_classes.items():
        print(f"  {class_id}: {class_name} ({annotator.intent_names[class_id]})")
    
    # 示例：标注frame_000000.jpg为直行
    image_path = "frame_000000.jpg"
    if os.path.exists(image_path):
        print(f"\n正在标注 {image_path} 为 '直行' 意图...")
        result = annotator.annotate_with_enhanced_keypoints(
            image_path, 0, "enhanced_annotations", visualize=True
        )
        if result:
            print("✅ 增强标注完成！")
            print("特点:")
            print("- 针对背部视角优化")
            print("- 确保17个关键点完整性")
            print("- 增强面部关键点推断")
            print("- 关键点重要性权重")
        else:
            print("❌ 标注失败！")
    else:
        print(f"❌ 找不到图片文件: {image_path}")

if __name__ == '__main__':
    main()

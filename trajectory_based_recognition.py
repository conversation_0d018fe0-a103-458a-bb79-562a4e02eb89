"""
基于移动轨迹的意图识别
重新定义"直行" = 移动轨迹是直线，而不是身体角度稳定
"""

import cv2
import numpy as np
import os
from collections import deque
from ultralytics import YOLO
from sklearn.linear_model import LinearRegression

class TrajectoryBasedRecognizer:
    def __init__(self):
        # 意图类别
        self.intent_classes = ['STRAIGHT', 'LEFT', 'RIGHT', 'TURN_AROUND', 'STOP']
        self.intent_colors = [
            (0, 255, 0),    # STRAIGHT - 绿色
            (255, 0, 0),    # LEFT - 蓝色
            (0, 0, 255),    # RIGHT - 红色
            (255, 0, 255),  # TURN_AROUND - 紫色
            (0, 255, 255)   # STOP - 黄色
        ]
        
        # 基于轨迹的阈值
        self.movement_threshold = 0.000005
        
        # 直行检测：轨迹的线性度
        self.straight_r2_threshold = 0.85      # R²阈值，越接近1越直
        self.straight_deviation_threshold = 0.02  # 轨迹偏差阈值
        
        # 转弯检测：轨迹的曲率
        self.turn_curvature_threshold = 0.0008  # 曲率阈值
        
        # 时序数据缓冲区
        self.body_center_sequence = deque(maxlen=20)
        self.trajectory_analysis_window = 15  # 分析窗口大小
        
        # 视频信息
        self.total_frames = 0
        
        # 加载姿态检测模型
        print("加载YOLOv8姿态检测模型...")
        self.model = YOLO('yolov8n-pose.pt')
        print("✓ 模型加载成功")
        print("新逻辑: 轨迹直线=直行, 轨迹弯曲=转弯")
    
    def set_video_info(self, total_frames):
        """设置视频总帧数"""
        self.total_frames = total_frames
    
    def extract_features(self, keypoints, confidence):
        """提取基本特征"""
        features = {}
        
        # 身体中心
        if all(confidence[i] > 0.4 for i in [5, 6, 11, 12]):
            shoulder_center = (keypoints[5] + keypoints[6]) / 2
            hip_center = (keypoints[11] + keypoints[12]) / 2
            body_center = (shoulder_center + hip_center) / 2
            
            features['body_center'] = body_center
        
        return features
    
    def analyze_movement(self):
        """分析移动状态"""
        if len(self.body_center_sequence) < 3:
            return False, 0.0
        
        centers = np.array(list(self.body_center_sequence)[-5:])
        
        movements = []
        for i in range(1, len(centers)):
            movement = np.linalg.norm(centers[i] - centers[i-1])
            movements.append(movement)
        
        avg_movement = np.mean(movements) if movements else 0
        is_moving = avg_movement > self.movement_threshold
        
        return is_moving, avg_movement
    
    def analyze_trajectory_linearity(self):
        """分析轨迹的直线度 - 关键函数"""
        if len(self.body_center_sequence) < self.trajectory_analysis_window:
            return None, {}
        
        # 获取最近的轨迹点
        trajectory = np.array(list(self.body_center_sequence)[-self.trajectory_analysis_window:])
        
        # 1. 线性回归分析
        x_coords = trajectory[:, 0]
        y_coords = trajectory[:, 1]
        
        # 检查是否有足够的变化
        x_range = np.max(x_coords) - np.min(x_coords)
        y_range = np.max(y_coords) - np.min(y_coords)
        
        if x_range < 0.001 and y_range < 0.001:
            # 几乎没有移动
            return 'STOP', {'reason': 'no_movement', 'x_range': x_range, 'y_range': y_range}
        
        # 选择变化较大的坐标作为自变量
        if x_range >= y_range:
            X = x_coords.reshape(-1, 1)
            y = y_coords
            primary_axis = 'x'
        else:
            X = y_coords.reshape(-1, 1)
            y = x_coords
            primary_axis = 'y'
        
        # 线性回归
        reg = LinearRegression().fit(X, y)
        r2_score = reg.score(X, y)
        
        # 计算预测值和实际值的偏差
        y_pred = reg.predict(X)
        deviations = np.abs(y - y_pred)
        max_deviation = np.max(deviations)
        avg_deviation = np.mean(deviations)
        
        # 2. 轨迹曲率分析
        curvature = self.calculate_trajectory_curvature(trajectory)
        
        # 3. 方向分析
        start_point = trajectory[0]
        end_point = trajectory[-1]
        overall_direction = end_point - start_point
        overall_angle = np.degrees(np.arctan2(overall_direction[1], overall_direction[0]))
        
        analysis = {
            'r2_score': r2_score,
            'max_deviation': max_deviation,
            'avg_deviation': avg_deviation,
            'curvature': curvature,
            'overall_angle': overall_angle,
            'primary_axis': primary_axis,
            'x_range': x_range,
            'y_range': y_range,
            'trajectory_length': len(trajectory)
        }
        
        # 判断逻辑
        intent = None
        confidence = 0.0
        reason = ""
        
        # 1. 直行判断：轨迹接近直线
        if (r2_score > self.straight_r2_threshold and 
            max_deviation < self.straight_deviation_threshold):
            intent = 'STRAIGHT'
            confidence = min(0.95, r2_score * 0.8 + (1 - max_deviation / self.straight_deviation_threshold) * 0.2)
            reason = f"linear_trajectory (R²={r2_score:.3f}, dev={max_deviation:.4f})"
        
        # 2. 转弯判断：轨迹有明显曲率
        elif abs(curvature) > self.turn_curvature_threshold:
            if curvature > 0:
                intent = 'LEFT'  # 正曲率为左转
            else:
                intent = 'RIGHT'  # 负曲率为右转
            confidence = min(0.9, abs(curvature) / self.turn_curvature_threshold * 0.7)
            reason = f"curved_trajectory (curvature={curvature:.6f})"
        
        # 3. 基于线性度的转弯判断
        elif r2_score < 0.7:  # 轨迹不够直
            # 分析轨迹的整体弯曲方向
            mid_point = len(trajectory) // 2
            first_half = trajectory[:mid_point]
            second_half = trajectory[mid_point:]
            
            if len(first_half) >= 3 and len(second_half) >= 3:
                first_direction = first_half[-1] - first_half[0]
                second_direction = second_half[-1] - second_half[0]
                
                # 计算方向变化（叉积）
                direction_change = np.cross(first_direction, second_direction)
                
                if abs(direction_change) > 0.0005:
                    if direction_change > 0:
                        intent = 'LEFT'
                    else:
                        intent = 'RIGHT'
                    confidence = min(0.8, abs(direction_change) / 0.002)
                    reason = f"direction_change (change={direction_change:.6f})"
                else:
                    intent = 'STRAIGHT'
                    confidence = 0.6
                    reason = f"low_linearity_but_straight (R²={r2_score:.3f})"
            else:
                intent = 'STRAIGHT'
                confidence = 0.5
                reason = "insufficient_trajectory_data"
        
        # 4. 默认直行
        else:
            intent = 'STRAIGHT'
            confidence = 0.7
            reason = f"default_straight (R²={r2_score:.3f})"
        
        analysis['intent'] = intent
        analysis['confidence'] = confidence
        analysis['reason'] = reason
        
        return intent, analysis
    
    def calculate_trajectory_curvature(self, trajectory):
        """计算轨迹曲率"""
        if len(trajectory) < 5:
            return 0.0
        
        # 使用三点法计算曲率
        curvatures = []
        
        for i in range(1, len(trajectory) - 1):
            p1 = trajectory[i-1]
            p2 = trajectory[i]
            p3 = trajectory[i+1]
            
            # 计算向量
            v1 = p2 - p1
            v2 = p3 - p2
            
            # 计算叉积（曲率的近似）
            cross_product = np.cross(v1, v2)
            
            # 归一化
            norm_v1 = np.linalg.norm(v1)
            norm_v2 = np.linalg.norm(v2)
            
            if norm_v1 > 1e-6 and norm_v2 > 1e-6:
                curvature = cross_product / (norm_v1 * norm_v2)
                curvatures.append(curvature)
        
        # 返回平均曲率
        return np.mean(curvatures) if curvatures else 0.0
    
    def classify_intent_trajectory_based(self, frame_num):
        """基于轨迹的意图分类"""
        
        # 1. 检查移动状态
        is_moving, movement_strength = self.analyze_movement()
        
        if not is_moving:
            return 4, 0.8, {'reason': 'not_moving', 'movement': movement_strength}
        
        # 2. 分析轨迹线性度
        intent, analysis = self.analyze_trajectory_linearity()
        
        if intent is None:
            return None, 0.0, {'reason': 'insufficient_data'}
        
        # 3. 转换为数字ID
        intent_id = None
        if intent == 'STRAIGHT':
            intent_id = 0
        elif intent == 'LEFT':
            intent_id = 1
        elif intent == 'RIGHT':
            intent_id = 2
        elif intent == 'STOP':
            intent_id = 4
        
        return intent_id, analysis['confidence'], analysis
    
    def update_and_classify(self, keypoints, confidence, frame_num):
        """更新数据并分类"""
        
        # 提取特征
        features = self.extract_features(keypoints, confidence)
        
        # 更新历史数据
        if 'body_center' in features:
            self.body_center_sequence.append(features['body_center'])
        
        # 进行分类
        if len(self.body_center_sequence) >= self.trajectory_analysis_window:
            
            intent, confidence, debug_info = self.classify_intent_trajectory_based(frame_num)
            
            # 添加进度信息
            progress = frame_num / self.total_frames if self.total_frames > 0 else 0
            debug_info['progress'] = progress
            debug_info['frame'] = frame_num
            
            return intent, confidence, debug_info
        
        return None, 0.0, {'reason': 'insufficient_data', 'frame': frame_num}

def analyze_video_trajectory_based(video_path='left.mp4', output_path='left_trajectory_based_analysis.mp4'):
    """
    使用基于轨迹的方法分析视频
    """
    print(f"开始基于轨迹的意图识别: {video_path}")
    print("新定义: 轨迹直线=直行, 轨迹弯曲=转弯")
    
    if not os.path.exists(video_path):
        print(f"错误: 视频文件不存在")
        return False
    
    # 创建识别器
    recognizer = TrajectoryBasedRecognizer()
    
    # 打开视频
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("错误: 无法打开视频")
        return False
    
    # 视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    recognizer.set_video_info(total_frames)
    
    print(f"视频: {width}x{height}, {fps}FPS, {total_frames}帧")
    
    # 创建输出视频
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    frame_count = 0
    detection_count = 0
    analysis_count = 0
    intent_stats = {name: 0 for name in recognizer.intent_classes}
    intent_stats['ANALYZING'] = 0
    
    # 分段统计
    segment_stats = {'first_half': {}, 'second_half': {}}
    
    print("开始处理...")
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 姿态检测
            results = recognizer.model(frame, conf=0.3, verbose=False)
            
            annotated_frame = frame.copy()
            current_intent = None
            intent_confidence = 0.0
            debug_info = {}
            
            # 处理检测结果
            if len(results) > 0 and results[0].keypoints is not None:
                keypoints_data = results[0].keypoints.data
                
                if len(keypoints_data) > 0:
                    detection_count += 1
                    keypoints = keypoints_data[0]
                    
                    if keypoints.shape[0] == 17:
                        xy = keypoints[:, :2].cpu().numpy()
                        conf = keypoints[:, 2].cpu().numpy()
                        
                        # 归一化关键点
                        normalized_xy = xy.copy()
                        normalized_xy[:, 0] /= width
                        normalized_xy[:, 1] /= height
                        
                        # 基于轨迹的分析
                        intent_id, confidence, debug_info = recognizer.update_and_classify(
                            normalized_xy, conf, frame_count
                        )
                        
                        if intent_id is not None:
                            current_intent = intent_id
                            intent_confidence = confidence
                            analysis_count += 1
                            
                            # 分段统计
                            segment = 'first_half' if frame_count <= total_frames // 2 else 'second_half'
                            intent_name = recognizer.intent_classes[intent_id]
                            segment_stats[segment][intent_name] = segment_stats[segment].get(intent_name, 0) + 1
                        
                        # 绘制关键点
                        for i, (x, y) in enumerate(xy):
                            if conf[i] > 0.3:
                                color = (0, 255, 0) if conf[i] > 0.7 else (0, 255, 255)
                                cv2.circle(annotated_frame, (int(x), int(y)), 6, color, -1)
                        
                        # 绘制骨架
                        connections = [
                            (5, 6), (5, 7), (7, 9), (6, 8), (8, 10),
                            (5, 11), (6, 12), (11, 12),
                            (11, 13), (13, 15), (12, 14), (14, 16)
                        ]
                        
                        for start_idx, end_idx in connections:
                            if conf[start_idx] > 0.3 and conf[end_idx] > 0.3:
                                start_point = (int(xy[start_idx][0]), int(xy[start_idx][1]))
                                end_point = (int(xy[end_idx][0]), int(xy[end_idx][1]))
                                cv2.line(annotated_frame, start_point, end_point, (255, 0, 0), 3)
                        
                        # 绘制轨迹（重点突出）
                        if len(recognizer.body_center_sequence) >= 2:
                            trajectory_points = []
                            for center in recognizer.body_center_sequence:
                                point = center * np.array([width, height])
                                trajectory_points.append((int(point[0]), int(point[1])))
                            
                            # 绘制轨迹线
                            for i in range(1, len(trajectory_points)):
                                cv2.line(annotated_frame, trajectory_points[i-1], trajectory_points[i], (255, 255, 0), 5)
                            
                            # 标记起点和终点
                            if len(trajectory_points) >= 2:
                                cv2.circle(annotated_frame, trajectory_points[0], 10, (0, 255, 0), -1)  # 起点绿色
                                cv2.circle(annotated_frame, trajectory_points[-1], 10, (0, 0, 255), -1)  # 终点红色
            
            # 更新统计
            if current_intent is not None:
                intent_stats[recognizer.intent_classes[current_intent]] += 1
            else:
                intent_stats['ANALYZING'] += 1
            
            # 绘制信息面板
            panel_width = 950
            panel_height = 400
            
            # 半透明背景
            overlay = annotated_frame.copy()
            cv2.rectangle(overlay, (10, 10), (panel_width, panel_height), (0, 0, 0), -1)
            cv2.addWeighted(overlay, 0.85, annotated_frame, 0.15, 0, annotated_frame)
            
            # 白色边框
            cv2.rectangle(annotated_frame, (10, 10), (panel_width, panel_height), (255, 255, 255), 3)
            
            # 显示信息
            y = 40
            line_height = 25
            
            # 标题
            cv2.putText(annotated_frame, "Trajectory-Based Intent Recognition", 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (255, 255, 255), 2)
            y += line_height + 5
            
            # 进度信息
            progress = frame_count / total_frames * 100
            half = "前半段" if frame_count <= total_frames // 2 else "后半段"
            cv2.putText(annotated_frame, f'进度: {progress:.1f}% ({half}) - 轨迹点: {len(recognizer.body_center_sequence)}', 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            y += line_height
            
            # 进度条
            bar_width = 500
            bar_height = 15
            cv2.rectangle(annotated_frame, (25, y), (25 + bar_width, y + bar_height), (100, 100, 100), -1)
            cv2.rectangle(annotated_frame, (25, y), (25 + int(bar_width * progress / 100), y + bar_height), (0, 255, 0), -1)
            
            # 中点标记
            mid_x = 25 + bar_width // 2
            cv2.line(annotated_frame, (mid_x, y), (mid_x, y + bar_height), (255, 255, 255), 3)
            
            y += line_height + 10
            
            # 意图显示
            if current_intent is not None:
                intent_text = f'Intent: {recognizer.intent_classes[current_intent]}'
                confidence_text = f'Confidence: {intent_confidence:.2f}'
                intent_color = recognizer.intent_colors[current_intent]
                
                cv2.putText(annotated_frame, intent_text, 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.2, intent_color, 3)
                y += line_height + 5
                
                cv2.putText(annotated_frame, confidence_text, 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, intent_color, 2)
                y += line_height
                
                # 显示轨迹分析详情
                if debug_info:
                    reason = debug_info.get('reason', 'unknown')
                    cv2.putText(annotated_frame, f"Reason: {reason}", 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (200, 200, 200), 2)
                    y += line_height
                    
                    # 显示关键参数
                    if 'r2_score' in debug_info:
                        cv2.putText(annotated_frame, f"Linearity (R²): {debug_info['r2_score']:.3f}", 
                                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
                        y += 18
                    
                    if 'curvature' in debug_info:
                        cv2.putText(annotated_frame, f"Curvature: {debug_info['curvature']:.6f}", 
                                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
                        y += 18
                    
                    if 'max_deviation' in debug_info:
                        cv2.putText(annotated_frame, f"Max Deviation: {debug_info['max_deviation']:.4f}", 
                                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
                
                # 右上角大字显示
                intent_display = recognizer.intent_classes[current_intent]
                text_size = cv2.getTextSize(intent_display, cv2.FONT_HERSHEY_SIMPLEX, 2.5, 5)[0]
                
                cv2.rectangle(annotated_frame, 
                             (width - text_size[0] - 40, 20), 
                             (width - 20, 100), 
                             intent_color, -1)
                cv2.rectangle(annotated_frame, 
                             (width - text_size[0] - 40, 20), 
                             (width - 20, 100), 
                             (255, 255, 255), 3)
                cv2.putText(annotated_frame, intent_display, 
                           (width - text_size[0] - 30, 70), 
                           cv2.FONT_HERSHEY_SIMPLEX, 2.5, (255, 255, 255), 5)
            else:
                cv2.putText(annotated_frame, 'Status: Building trajectory...', 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (128, 128, 128), 3)
            
            # 写入视频
            writer.write(annotated_frame)
            
            # 显示进度
            if frame_count % 100 == 0:
                progress_pct = frame_count / total_frames * 100
                current_intent_name = recognizer.intent_classes[current_intent] if current_intent is not None else 'ANALYZING'
                print(f"进度: {progress_pct:.1f}% - {half} - 意图: {current_intent_name}")
    
    except Exception as e:
        print(f"处理错误: {e}")
        return False
    
    finally:
        cap.release()
        writer.release()
    
    # 分段统计分析
    print(f"\n=== 前后半段轨迹对比分析 ===")
    
    for segment_name, stats in segment_stats.items():
        if stats:
            total_frames_in_segment = sum(stats.values())
            main_intent = max(stats.keys(), key=lambda x: stats[x])
            main_intent_ratio = stats[main_intent] / total_frames_in_segment
            
            segment_display = "前半段" if segment_name == 'first_half' else "后半段"
            print(f"{segment_display}: 主要意图 = {main_intent} ({main_intent_ratio:.1%})")
            
            for intent, count in stats.items():
                ratio = count / total_frames_in_segment
                print(f"  {intent}: {count}帧 ({ratio:.1%})")
    
    print(f"\n=== 基于轨迹的意图识别完成 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测帧数: {detection_count}")
    print(f"分析帧数: {analysis_count}")
    print(f"检测率: {detection_count/frame_count*100:.1f}%")
    print(f"分析率: {analysis_count/frame_count*100:.1f}%")
    print(f"\n总体意图分布:")
    for intent, count in intent_stats.items():
        percentage = count / frame_count * 100 if frame_count > 0 else 0
        print(f"  {intent}: {count} 帧 ({percentage:.1f}%)")
    print(f"输出视频: {output_path}")
    
    return True

if __name__ == '__main__':
    success = analyze_video_trajectory_based()
    if success:
        print("\n🎉 基于轨迹的意图识别完成！")
        print("特点:")
        print("- ✅ 基于轨迹线性度检测直行")
        print("- ✅ 基于轨迹曲率检测转弯")
        print("- ✅ 线性回归分析轨迹质量")
        print("- ✅ 重新定义直行概念")
    else:
        print("\n❌ 基于轨迹的意图识别失败！")

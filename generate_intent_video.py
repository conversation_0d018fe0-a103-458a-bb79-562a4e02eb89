"""
生成带有清晰意图显示的视频分析
"""

import cv2
import numpy as np
import torch
import os
import json
from collections import deque
from ultralytics import <PERSON>OL<PERSON>

def analyze_video_with_intent(video_path='left.mp4', output_path='left_intent_analysis.mp4'):
    """
    分析视频并生成带有清晰意图显示的结果
    """
    print(f"开始分析视频: {video_path}")
    
    # 检查视频文件
    if not os.path.exists(video_path):
        print(f"错误: 视频文件不存在 - {video_path}")
        return False
    
    # 加载YOLOv8姿态检测模型
    print("加载YOLOv8姿态检测模型...")
    try:
        model = YOLO('yolov8n-pose.pt')
        print("✓ 模型加载成功")
    except Exception as e:
        print(f"✗ 模型加载失败: {e}")
        return False
    
    # 打开视频
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"错误: 无法打开视频文件")
        return False
    
    # 获取视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"视频信息: {width}x{height}, {fps}FPS, {total_frames}帧, 时长{total_frames/fps:.1f}秒")
    
    # 创建输出视频
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    if not writer.isOpened():
        print("错误: 无法创建输出视频文件")
        cap.release()
        return False
    
    print(f"输出视频: {output_path}")
    
    # 意图类别和颜色
    intent_classes = ['直行', '左转', '右转', '停止']
    intent_colors = [
        (0, 255, 0),    # 直行 - 绿色
        (255, 0, 0),    # 左转 - 蓝色  
        (0, 0, 255),    # 右转 - 红色
        (0, 255, 255)   # 停止 - 黄色
    ]
    
    # 姿态历史缓冲区
    pose_history = deque(maxlen=15)
    intent_history = deque(maxlen=8)  # 用于平滑意图预测
    
    # 统计信息
    frame_count = 0
    detection_count = 0
    intent_stats = {intent: 0 for intent in intent_classes}
    intent_stats['未检测'] = 0
    
    # COCO关键点连接
    connections = [
        (0, 1), (0, 2), (1, 3), (2, 4),  # 头部
        (5, 6), (5, 7), (7, 9), (6, 8), (8, 10),  # 上身
        (5, 11), (6, 12), (11, 12),  # 躯干
        (11, 13), (13, 15), (12, 14), (14, 16)  # 下身
    ]
    
    print("开始处理视频帧...")
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 姿态检测
            results = model(frame, conf=0.4, verbose=False)
            
            # 创建标注帧
            annotated_frame = frame.copy()
            
            current_intent = None
            intent_confidence = 0.0
            person_detected = False
            visible_keypoints = 0
            
            # 处理检测结果
            if len(results) > 0 and results[0].keypoints is not None:
                keypoints_data = results[0].keypoints.data
                
                if len(keypoints_data) > 0:
                    person_detected = True
                    detection_count += 1
                    
                    # 获取关键点
                    keypoints = keypoints_data[0]
                    if keypoints.shape[0] == 17:
                        xy = keypoints[:, :2].cpu().numpy()
                        conf = keypoints[:, 2].cpu().numpy()
                        
                        # 统计可见关键点
                        visible_keypoints = np.sum(conf > 0.3)
                        
                        # 绘制关键点
                        for i, (x, y) in enumerate(xy):
                            if conf[i] > 0.3:
                                color = (0, 255, 0) if conf[i] > 0.7 else (0, 255, 255) if conf[i] > 0.5 else (0, 0, 255)
                                cv2.circle(annotated_frame, (int(x), int(y)), 6, color, -1)
                                # 显示关键点编号
                                cv2.putText(annotated_frame, str(i), (int(x)+8, int(y)), 
                                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
                        
                        # 绘制骨架连接
                        for start_idx, end_idx in connections:
                            if conf[start_idx] > 0.3 and conf[end_idx] > 0.3:
                                start_point = (int(xy[start_idx][0]), int(xy[start_idx][1]))
                                end_point = (int(xy[end_idx][0]), int(xy[end_idx][1]))
                                cv2.line(annotated_frame, start_point, end_point, (255, 0, 0), 3)
                        
                        # 意图分析
                        if conf[11] > 0.4 and conf[12] > 0.4:  # 髋部关键点可见
                            # 计算髋部中心
                            hip_center = np.mean(xy[[11, 12]], axis=0)
                            normalized_center = hip_center / np.array([width, height])
                            
                            # 添加到历史
                            pose_history.append(normalized_center)
                            
                            # 意图判断
                            if len(pose_history) >= 8:
                                # 计算移动向量
                                recent_poses = np.array(list(pose_history)[-8:])
                                movement = recent_poses[-1] - recent_poses[0]
                                
                                # 计算移动距离和方向
                                move_distance = np.linalg.norm(movement)
                                
                                # 意图判断逻辑
                                if move_distance < 0.008:
                                    predicted_intent = 3  # 停止
                                    intent_confidence = 0.85
                                elif movement[0] < -0.012:  # 向左移动
                                    predicted_intent = 1  # 左转
                                    intent_confidence = min(0.95, abs(movement[0]) * 60)
                                elif movement[0] > 0.012:  # 向右移动
                                    predicted_intent = 2  # 右转
                                    intent_confidence = min(0.95, abs(movement[0]) * 60)
                                else:  # 主要向前移动
                                    predicted_intent = 0  # 直行
                                    intent_confidence = 0.75
                                
                                # 添加到意图历史进行平滑
                                intent_history.append(predicted_intent)
                                
                                # 使用投票机制平滑预测
                                if len(intent_history) >= 5:
                                    intent_counts = np.bincount(list(intent_history), minlength=4)
                                    current_intent = np.argmax(intent_counts)
                                    
                                    # 如果预测一致，提高置信度
                                    if current_intent == predicted_intent:
                                        intent_confidence = min(0.98, intent_confidence + 0.1)
            
            # 更新统计
            if current_intent is not None:
                intent_stats[intent_classes[current_intent]] += 1
            else:
                intent_stats['未检测'] += 1
            
            # 绘制信息面板
            panel_width = 550
            panel_height = 180
            
            # 半透明背景
            overlay = annotated_frame.copy()
            cv2.rectangle(overlay, (10, 10), (panel_width, panel_height), (0, 0, 0), -1)
            cv2.addWeighted(overlay, 0.8, annotated_frame, 0.2, 0, annotated_frame)
            
            # 白色边框
            cv2.rectangle(annotated_frame, (10, 10), (panel_width, panel_height), (255, 255, 255), 3)
            
            # 显示信息
            y = 40
            line_height = 28
            
            # 帧信息
            cv2.putText(annotated_frame, f'Frame: {frame_count}/{total_frames} ({frame_count/fps:.1f}s)', 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            y += line_height
            
            # 检测状态
            if person_detected:
                cv2.putText(annotated_frame, f'Person: DETECTED ({visible_keypoints}/17 points)', 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                y += line_height
                
                # 意图显示
                if current_intent is not None:
                    intent_text = f'INTENT: {intent_classes[current_intent]}'
                    confidence_text = f'Confidence: {intent_confidence:.2f}'
                    intent_color = intent_colors[current_intent]
                    
                    # 大字体显示意图
                    cv2.putText(annotated_frame, intent_text, 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.0, intent_color, 3)
                    y += line_height + 5
                    
                    # 显示置信度
                    cv2.putText(annotated_frame, confidence_text, 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, intent_color, 2)
                    
                    # 右上角大字显示当前意图
                    intent_display = intent_classes[current_intent]
                    text_size = cv2.getTextSize(intent_display, cv2.FONT_HERSHEY_SIMPLEX, 2.0, 4)[0]
                    
                    # 背景矩形
                    cv2.rectangle(annotated_frame, 
                                 (width - text_size[0] - 30, 10), 
                                 (width - 10, 80), 
                                 intent_color, -1)
                    
                    # 意图文字
                    cv2.putText(annotated_frame, intent_display, 
                               (width - text_size[0] - 20, 55), 
                               cv2.FONT_HERSHEY_SIMPLEX, 2.0, (255, 255, 255), 4)
                else:
                    cv2.putText(annotated_frame, 'INTENT: ANALYZING...', 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (128, 128, 128), 3)
            else:
                cv2.putText(annotated_frame, 'Person: NOT DETECTED', 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            
            # 写入视频
            writer.write(annotated_frame)
            
            # 显示进度
            if frame_count % 50 == 0:
                progress = frame_count / total_frames * 100
                print(f"处理进度: {progress:.1f}% ({frame_count}/{total_frames})")
    
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        return False
    
    finally:
        cap.release()
        writer.release()
    
    # 输出统计结果
    detection_rate = detection_count / frame_count if frame_count > 0 else 0
    
    print(f"\n=== 视频分析完成 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测帧数: {detection_count}")
    print(f"检测率: {detection_rate:.2%}")
    print(f"\n意图分布:")
    for intent, count in intent_stats.items():
        percentage = count / frame_count * 100 if frame_count > 0 else 0
        print(f"  {intent}: {count} 帧 ({percentage:.1f}%)")
    
    print(f"\n✓ 标注视频已保存: {output_path}")
    
    # 保存分析结果
    result_file = 'left_intent_analysis.json'
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump({
            'video_info': {
                'input_path': video_path,
                'output_path': output_path,
                'width': width,
                'height': height,
                'fps': fps,
                'total_frames': total_frames,
                'duration': total_frames / fps
            },
            'statistics': {
                'detection_rate': detection_rate,
                'intent_distribution': intent_stats
            }
        }, f, ensure_ascii=False, indent=2)
    
    print(f"✓ 分析结果已保存: {result_file}")
    return True

if __name__ == '__main__':
    success = analyze_video_with_intent()
    if success:
        print("\n🎉 视频分析成功完成！")
    else:
        print("\n❌ 视频分析失败！")

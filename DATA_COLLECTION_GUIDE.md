# 数据收集指南

## 📊 训练数据需求分析

### 最小可行数据集
- **每个意图类别**: 500-1000个序列
- **序列长度**: 30-60帧（1-2秒）
- **总数据量**: 2000-4000个序列
- **预计收集时间**: 2-4周

### 理想数据集
- **每个意图类别**: 2000-5000个序列  
- **多样性要求**: 不同人员、环境、时间
- **总数据量**: 8000-20000个序列
- **预计收集时间**: 2-3个月

## 🎥 数据收集方案

### 方案一：自主收集（推荐）
```bash
# 使用我们提供的工具收集数据
python collect_data.py --output_dir data/raw --duration 60 --label 0
```

**优势**：
- 数据质量可控
- 标注准确
- 符合具体应用场景

**步骤**：
1. 设置摄像头位置
2. 招募志愿者
3. 录制不同意图的行为
4. 使用工具自动标注

### 方案二：公开数据集改造
可能的数据源：
- **COCO数据集**: 有姿态标注，但缺少意图标签
- **Human3.6M**: 室内动作数据
- **PoseTrack**: 视频姿态跟踪数据

**改造方法**：
1. 下载原始数据
2. 使用规则或人工标注意图
3. 转换为我们的格式

### 方案三：合成数据增强
```python
# 使用数据增强扩充数据集
from src.data_processor import PoseDataProcessor
processor = PoseDataProcessor(config)
augmented_data = processor.augment_data(original_sequence, label)
```

## 🛠️ 数据收集工具

### 自动数据收集脚本
```python
"""
自动数据收集工具
"""
import cv2
import json
import os
from datetime import datetime

class DataCollector:
    def __init__(self, output_dir: str):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
    def collect_sequence(self, label: int, duration: int = 60):
        """收集一个序列"""
        cap = cv2.VideoCapture(0)
        
        # 录制设置
        fps = 30
        frames = []
        
        print(f"开始录制 {duration} 秒，标签: {label}")
        print("按空格键开始录制...")
        
        while True:
            ret, frame = cap.read()
            cv2.imshow('Data Collection', frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord(' '):  # 空格开始录制
                break
            elif key == ord('q'):  # q退出
                return
        
        # 开始录制
        for i in range(duration * fps):
            ret, frame = cap.read()
            if ret:
                frames.append(frame)
                cv2.imshow('Recording...', frame)
                cv2.waitKey(1)
        
        # 保存数据
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        video_path = os.path.join(self.output_dir, f"seq_{label}_{timestamp}.mp4")
        
        # 保存视频
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(video_path, fourcc, fps, 
                             (frame.shape[1], frame.shape[0]))
        
        for frame in frames:
            out.write(frame)
        
        out.release()
        cap.release()
        cv2.destroyAllWindows()
        
        print(f"序列已保存: {video_path}")
```

## 📋 数据标注规范

### 意图类别定义
- **0 - 直行**: 保持当前方向移动
- **1 - 左转**: 向左改变方向
- **2 - 右转**: 向右改变方向  
- **3 - 停止**: 停止移动或准备停止

### 标注质量要求
1. **清晰可见**: 人体主要关节可见
2. **动作明确**: 意图明显，无歧义
3. **时序完整**: 包含完整的动作过程
4. **环境多样**: 不同光照、背景、角度

### 数据格式规范
```json
{
  "video_path": "path/to/video.mp4",
  "label": 1,
  "start_frame": 0,
  "end_frame": 60,
  "quality": "good",
  "notes": "清晰的左转动作"
}
```

## 🔄 数据处理流程

### 1. 原始数据收集
```bash
# 收集不同意图的视频
python collect_data.py --label 0 --count 100  # 直行
python collect_data.py --label 1 --count 100  # 左转
python collect_data.py --label 2 --count 100  # 右转
python collect_data.py --label 3 --count 100  # 停止
```

### 2. 姿态提取
```bash
# 从视频中提取姿态序列
python extract_poses.py --input_dir data/raw --output_dir data/poses
```

### 3. 数据清洗
```python
# 过滤低质量数据
def filter_quality(pose_sequence, confidence):
    # 检查遮挡率
    visible_ratio = np.mean(confidence > 0.3)
    return visible_ratio > 0.7  # 70%的关键点可见
```

### 4. 数据增强
```python
# 扩充数据集
augmented_data = []
for sequence, label in original_data:
    # 原始数据
    augmented_data.append((sequence, label))
    
    # 水平翻转
    flipped = flip_sequence(sequence)
    flipped_label = flip_label(label)
    augmented_data.append((flipped, flipped_label))
    
    # 添加噪声
    noisy = add_noise(sequence)
    augmented_data.append((noisy, label))
```

### 5. 数据集划分
```python
# 按7:1.5:1.5划分
train_data, val_data, test_data = split_dataset(
    all_data, ratios=[0.7, 0.15, 0.15]
)
```

## 📈 数据质量评估

### 自动质量检查
```python
def assess_data_quality(dataset):
    """评估数据集质量"""
    quality_metrics = {
        'total_sequences': len(dataset),
        'avg_sequence_length': np.mean([len(seq) for seq, _ in dataset]),
        'class_distribution': count_classes(dataset),
        'avg_visibility': calculate_visibility(dataset),
        'motion_variance': calculate_motion_variance(dataset)
    }
    return quality_metrics
```

### 质量改进建议
1. **增加数据多样性**: 不同人员、环境、时间
2. **平衡类别分布**: 确保各类别数量相近
3. **提高标注质量**: 多人标注，交叉验证
4. **增强数据清洗**: 自动过滤低质量样本

## 🎯 收集策略建议

### 阶段性收集
1. **第一阶段**: 收集基础数据集（每类500个序列）
2. **第二阶段**: 训练初始模型，评估性能
3. **第三阶段**: 根据错误分析，补充困难样本
4. **第四阶段**: 扩充到完整数据集

### 优先级排序
1. **高优先级**: 清晰、典型的动作样本
2. **中优先级**: 边界情况、过渡动作
3. **低优先级**: 极端情况、罕见场景

### 成本控制
- **人力成本**: 2-3人，兼职收集
- **设备成本**: 普通摄像头即可
- **时间成本**: 每天2-3小时，持续4-8周
- **存储成本**: 约100GB存储空间

## 📞 技术支持

如需帮助，可以：
1. 运行 `python collect_data.py --help` 查看工具使用方法
2. 查看示例数据格式
3. 使用提供的质量评估工具

**记住**：数据质量比数量更重要！优先收集高质量的典型样本。

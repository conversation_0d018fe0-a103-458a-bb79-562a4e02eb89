"""
视频分析器：人体姿态检测、遮挡检测和意图判断
"""

import cv2
import numpy as np
import torch
from ultralytics import YOLO
import matplotlib.pyplot as plt
from collections import deque
import json
import os
from typing import Dict, List, Tuple, Optional
import time

class VideoAnalyzer:
    """视频分析器"""
    
    def __init__(self, model_path: str = None):
        """
        初始化视频分析器
        Args:
            model_path: 训练好的意图识别模型路径（可选）
        """
        # 加载YOLOv8姿态检测模型
        print("加载YOLOv8姿态检测模型...")
        self.yolo_model = YOLO('yolov8n-pose.pt')
        
        # 意图识别模型（如果提供）
        self.intent_model = None
        if model_path and os.path.exists(model_path):
            print(f"加载意图识别模型: {model_path}")
            self.intent_model = self._load_intent_model(model_path)
        
        # 姿态序列缓冲区
        self.sequence_length = 30
        self.pose_buffer = deque(maxlen=self.sequence_length)
        self.confidence_buffer = deque(maxlen=self.sequence_length)
        
        # COCO关键点名称
        self.keypoint_names = [
            'nose', 'left_eye', 'right_eye', 'left_ear', 'right_ear',
            'left_shoulder', 'right_shoulder', 'left_elbow', 'right_elbow',
            'left_wrist', 'right_wrist', 'left_hip', 'right_hip',
            'left_knee', 'right_knee', 'left_ankle', 'right_ankle'
        ]
        
        # 关键点连接关系
        self.connections = [
            (0, 1), (0, 2), (1, 3), (2, 4),  # 头部
            (5, 6), (5, 7), (7, 9), (6, 8), (8, 10),  # 上身
            (5, 11), (6, 12), (11, 12),  # 躯干
            (11, 13), (13, 15), (12, 14), (14, 16)  # 下身
        ]
        
        # 意图类别
        self.intent_classes = ['直行', '左转', '右转', '停止']
        
        # 统计信息
        self.frame_count = 0
        self.detection_count = 0
        
        print("视频分析器初始化完成!")
    
    def _load_intent_model(self, model_path: str):
        """加载意图识别模型"""
        try:
            from src.data_processor import load_config
            from src.model import create_model
            
            # 加载配置
            config = load_config('config.yaml')
            
            # 创建模型
            model = create_model(config)
            
            # 加载权重
            checkpoint = torch.load(model_path, map_location='cpu')
            model.load_state_dict(checkpoint['model_state_dict'])
            model.eval()
            
            return model
        except Exception as e:
            print(f"加载意图识别模型失败: {e}")
            return None
    
    def detect_pose(self, frame: np.ndarray) -> Tuple[Optional[np.ndarray], Optional[np.ndarray], Dict]:
        """
        检测人体姿态
        Args:
            frame: 输入图像
        Returns:
            keypoints: (17, 2) 关键点坐标
            confidence: (17,) 关键点置信度
            detection_info: 检测信息
        """
        # YOLOv8推理
        results = self.yolo_model(frame, conf=0.5, verbose=False)
        
        detection_info = {
            'persons_detected': 0,
            'bbox': None,
            'detection_confidence': 0.0
        }
        
        if len(results) > 0 and results[0].keypoints is not None:
            keypoints_data = results[0].keypoints.data
            
            if len(keypoints_data) > 0:
                # 取第一个检测到的人
                keypoints = keypoints_data[0]  # (17, 3) [x, y, confidence]
                
                if keypoints.shape[0] == 17:  # 确保是COCO格式
                    xy = keypoints[:, :2].cpu().numpy()  # (17, 2)
                    conf = keypoints[:, 2].cpu().numpy()  # (17,)
                    
                    # 获取边界框信息
                    if results[0].boxes is not None and len(results[0].boxes) > 0:
                        bbox = results[0].boxes.xyxy[0].cpu().numpy()
                        detection_conf = results[0].boxes.conf[0].cpu().numpy()
                        
                        detection_info.update({
                            'persons_detected': 1,
                            'bbox': bbox,
                            'detection_confidence': float(detection_conf)
                        })
                    
                    return xy, conf, detection_info
        
        return None, None, detection_info
    
    def analyze_occlusion(self, keypoints: np.ndarray, confidence: np.ndarray) -> Dict:
        """
        分析遮挡情况
        Args:
            keypoints: 关键点坐标
            confidence: 关键点置信度
        Returns:
            occlusion_info: 遮挡分析结果
        """
        conf_threshold = 0.3
        
        # 统计遮挡情况
        visible_points = np.sum(confidence > conf_threshold)
        occluded_points = 17 - visible_points
        occlusion_ratio = occluded_points / 17
        
        # 分析关键部位遮挡
        body_parts = {
            'head': [0, 1, 2, 3, 4],
            'upper_body': [5, 6, 7, 8, 9, 10],
            'lower_body': [11, 12, 13, 14, 15, 16]
        }
        
        part_occlusion = {}
        for part_name, indices in body_parts.items():
            part_conf = confidence[indices]
            part_visible = np.sum(part_conf > conf_threshold)
            part_occlusion[part_name] = {
                'visible': int(part_visible),
                'total': len(indices),
                'occlusion_ratio': 1 - (part_visible / len(indices))
            }
        
        return {
            'total_visible': int(visible_points),
            'total_occluded': int(occluded_points),
            'occlusion_ratio': float(occlusion_ratio),
            'body_parts': part_occlusion,
            'quality': 'good' if occlusion_ratio < 0.3 else 'medium' if occlusion_ratio < 0.6 else 'poor'
        }
    
    def predict_intent(self, keypoints: np.ndarray, confidence: np.ndarray) -> Dict:
        """
        预测行人意图
        Args:
            keypoints: 关键点坐标
            confidence: 关键点置信度
        Returns:
            intent_info: 意图预测结果
        """
        # 归一化关键点
        height, width = 480, 640  # 假设的图像尺寸
        normalized_kp = keypoints.copy()
        normalized_kp[:, 0] /= width
        normalized_kp[:, 1] /= height
        
        # 更新缓冲区
        self.pose_buffer.append(normalized_kp)
        self.confidence_buffer.append(confidence)
        
        intent_info = {
            'buffer_status': f"{len(self.pose_buffer)}/{self.sequence_length}",
            'predicted_class': None,
            'confidence': None,
            'class_name': None
        }
        
        # 如果有训练好的模型且缓冲区满了
        if self.intent_model and len(self.pose_buffer) == self.sequence_length:
            try:
                # 准备输入数据
                pose_sequence = np.array(list(self.pose_buffer))
                conf_sequence = np.array(list(self.confidence_buffer))
                
                # 简单的遮挡处理（线性插值）
                for t in range(pose_sequence.shape[0]):
                    for n in range(pose_sequence.shape[1]):
                        if conf_sequence[t, n] < 0.3:
                            # 使用前一帧的值
                            if t > 0:
                                pose_sequence[t, n] = pose_sequence[t-1, n]
                
                # 归一化（以髋部为中心）
                for t in range(pose_sequence.shape[0]):
                    hip_center = np.mean(pose_sequence[t, [11, 12], :], axis=0)
                    pose_sequence[t] = pose_sequence[t] - hip_center
                
                # 计算运动特征
                velocity = np.diff(pose_sequence, axis=0, prepend=pose_sequence[:1])
                acceleration = np.diff(velocity, axis=0, prepend=velocity[:1])
                motion_features = np.concatenate([pose_sequence, velocity, acceleration], axis=-1)
                
                # 转换为tensor
                input_tensor = torch.FloatTensor(motion_features).unsqueeze(0)
                
                # 模型推理
                with torch.no_grad():
                    outputs = self.intent_model(input_tensor)
                    probabilities = torch.softmax(outputs['intent_logits'], dim=1)
                    confidence, predicted_class = torch.max(probabilities, dim=1)
                
                intent_info.update({
                    'predicted_class': int(predicted_class.item()),
                    'confidence': float(confidence.item()),
                    'class_name': self.intent_classes[predicted_class.item()]
                })
                
            except Exception as e:
                print(f"意图预测错误: {e}")
        else:
            # 简单的基于规则的意图判断（演示用）
            if len(self.pose_buffer) >= 10:
                recent_poses = np.array(list(self.pose_buffer)[-10:])
                
                # 计算重心移动
                centers = np.mean(recent_poses[:, [11, 12], :], axis=1)  # 髋部中心
                movement = centers[-1] - centers[0]
                
                # 简单规则判断
                if abs(movement[0]) < 0.02 and abs(movement[1]) < 0.02:
                    predicted_class = 3  # 停止
                elif movement[0] < -0.03:
                    predicted_class = 1  # 左转
                elif movement[0] > 0.03:
                    predicted_class = 2  # 右转
                else:
                    predicted_class = 0  # 直行
                
                intent_info.update({
                    'predicted_class': predicted_class,
                    'confidence': 0.7,  # 固定置信度
                    'class_name': self.intent_classes[predicted_class],
                    'method': 'rule_based'
                })
        
        return intent_info
    
    def draw_pose(self, frame: np.ndarray, keypoints: np.ndarray, confidence: np.ndarray) -> np.ndarray:
        """绘制姿态"""
        annotated_frame = frame.copy()
        
        # 绘制关键点
        for i, (x, y) in enumerate(keypoints):
            conf = confidence[i]
            if conf > 0.3:
                # 根据置信度选择颜色
                color = (0, 255, 0) if conf > 0.7 else (0, 255, 255) if conf > 0.5 else (0, 0, 255)
                cv2.circle(annotated_frame, (int(x), int(y)), 4, color, -1)
                
                # 显示关键点编号
                cv2.putText(annotated_frame, str(i), (int(x), int(y-8)), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)
        
        # 绘制连接线
        for start_idx, end_idx in self.connections:
            if (confidence[start_idx] > 0.3 and confidence[end_idx] > 0.3):
                start_point = (int(keypoints[start_idx][0]), int(keypoints[start_idx][1]))
                end_point = (int(keypoints[end_idx][0]), int(keypoints[end_idx][1]))
                cv2.line(annotated_frame, start_point, end_point, (255, 0, 0), 2)
        
        return annotated_frame
    
    def draw_analysis_info(self, frame: np.ndarray, detection_info: Dict, 
                          occlusion_info: Dict, intent_info: Dict) -> np.ndarray:
        """绘制分析信息"""
        annotated_frame = frame.copy()
        height, width = frame.shape[:2]
        
        # 绘制信息背景
        info_height = 200
        cv2.rectangle(annotated_frame, (10, 10), (400, info_height), (0, 0, 0), -1)
        cv2.rectangle(annotated_frame, (10, 10), (400, info_height), (255, 255, 255), 2)
        
        y_offset = 30
        line_height = 20
        
        # 检测信息
        cv2.putText(annotated_frame, f"Frame: {self.frame_count}", 
                   (20, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        y_offset += line_height
        
        cv2.putText(annotated_frame, f"Persons: {detection_info['persons_detected']}", 
                   (20, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        y_offset += line_height
        
        # 遮挡信息
        if occlusion_info:
            cv2.putText(annotated_frame, f"Visible: {occlusion_info['total_visible']}/17", 
                       (20, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            y_offset += line_height
            
            quality_color = (0, 255, 0) if occlusion_info['quality'] == 'good' else \
                           (0, 255, 255) if occlusion_info['quality'] == 'medium' else (0, 0, 255)
            cv2.putText(annotated_frame, f"Quality: {occlusion_info['quality']}", 
                       (20, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.6, quality_color, 2)
            y_offset += line_height
        
        # 意图信息
        cv2.putText(annotated_frame, f"Buffer: {intent_info['buffer_status']}", 
                   (20, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        y_offset += line_height
        
        if intent_info['class_name']:
            intent_color = (0, 255, 0) if intent_info['confidence'] > 0.7 else \
                          (0, 255, 255) if intent_info['confidence'] > 0.5 else (0, 0, 255)
            cv2.putText(annotated_frame, f"Intent: {intent_info['class_name']}", 
                       (20, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.6, intent_color, 2)
            y_offset += line_height
            
            cv2.putText(annotated_frame, f"Conf: {intent_info['confidence']:.3f}", 
                       (20, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.6, intent_color, 2)
        
        return annotated_frame
    
    def analyze_video(self, video_path: str, output_path: str = None, 
                     save_results: bool = True) -> Dict:
        """
        分析视频文件
        Args:
            video_path: 输入视频路径
            output_path: 输出视频路径
            save_results: 是否保存分析结果
        Returns:
            analysis_results: 分析结果
        """
        print(f"开始分析视频: {video_path}")
        
        # 打开视频
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"无法打开视频文件: {video_path}")
        
        # 获取视频信息
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        print(f"视频信息: {width}x{height}, {fps} FPS, {total_frames} 帧")
        
        # 创建视频写入器
        writer = None
        if output_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        # 分析结果存储
        analysis_results = {
            'video_info': {
                'path': video_path,
                'width': width,
                'height': height,
                'fps': fps,
                'total_frames': total_frames
            },
            'frame_results': []
        }
        
        self.frame_count = 0
        self.detection_count = 0
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                self.frame_count += 1
                
                # 检测姿态
                keypoints, confidence, detection_info = self.detect_pose(frame)
                
                frame_result = {
                    'frame_id': self.frame_count,
                    'timestamp': self.frame_count / fps,
                    'detection_info': detection_info,
                    'occlusion_info': None,
                    'intent_info': None
                }
                
                annotated_frame = frame.copy()
                
                if keypoints is not None:
                    self.detection_count += 1
                    
                    # 分析遮挡
                    occlusion_info = self.analyze_occlusion(keypoints, confidence)
                    frame_result['occlusion_info'] = occlusion_info
                    
                    # 预测意图
                    intent_info = self.predict_intent(keypoints, confidence)
                    frame_result['intent_info'] = intent_info
                    
                    # 绘制姿态
                    annotated_frame = self.draw_pose(annotated_frame, keypoints, confidence)
                    
                    # 绘制分析信息
                    annotated_frame = self.draw_analysis_info(
                        annotated_frame, detection_info, occlusion_info, intent_info
                    )
                else:
                    # 没有检测到人体
                    cv2.putText(annotated_frame, "No person detected", 
                               (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
                
                # 保存帧结果
                analysis_results['frame_results'].append(frame_result)
                
                # 写入视频
                if writer:
                    writer.write(annotated_frame)
                
                # 显示进度
                if self.frame_count % 30 == 0:
                    progress = self.frame_count / total_frames * 100
                    print(f"处理进度: {progress:.1f}% ({self.frame_count}/{total_frames})")
                
                # 显示实时结果（可选）
                cv2.imshow('Video Analysis', annotated_frame)
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break
        
        finally:
            cap.release()
            if writer:
                writer.release()
            cv2.destroyAllWindows()
        
        # 计算统计信息
        detection_rate = self.detection_count / self.frame_count if self.frame_count > 0 else 0
        analysis_results['statistics'] = {
            'total_frames': self.frame_count,
            'frames_with_detection': self.detection_count,
            'detection_rate': detection_rate
        }
        
        print(f"分析完成!")
        print(f"总帧数: {self.frame_count}")
        print(f"检测到人体的帧数: {self.detection_count}")
        print(f"检测率: {detection_rate:.2%}")
        
        # 保存结果
        if save_results:
            result_path = video_path.replace('.mp4', '_analysis.json')
            with open(result_path, 'w', encoding='utf-8') as f:
                json.dump(analysis_results, f, ensure_ascii=False, indent=2)
            print(f"分析结果已保存到: {result_path}")
        
        if output_path:
            print(f"标注视频已保存到: {output_path}")
        
        return analysis_results


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='视频分析器')
    parser.add_argument('--input', type=str, required=True, help='输入视频路径')
    parser.add_argument('--output', type=str, help='输出视频路径')
    parser.add_argument('--model', type=str, help='意图识别模型路径')
    
    args = parser.parse_args()
    
    # 创建分析器
    analyzer = VideoAnalyzer(model_path=args.model)
    
    # 分析视频
    results = analyzer.analyze_video(
        video_path=args.input,
        output_path=args.output,
        save_results=True
    )
    
    print("视频分析完成!")


if __name__ == '__main__':
    main()

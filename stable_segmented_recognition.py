"""
稳定的分段意图识别
解决跳变问题，识别前半段直走、后半段左转的模式
"""

import cv2
import numpy as np
import os
from collections import deque
from ultralytics import YOLO

class StableSegmentedRecognizer:
    def __init__(self, sequence_length=15, smoothing_window=8):
        self.sequence_length = sequence_length
        self.smoothing_window = smoothing_window
        
        # 意图类别
        self.intent_classes = ['STRAIGHT', 'LEFT', 'RIGHT', 'TURN_AROUND', 'STOP']
        self.intent_colors = [
            (0, 255, 0),    # STRAIGHT - 绿色
            (255, 0, 0),    # LEFT - 蓝色
            (0, 0, 255),    # RIGHT - 红色
            (255, 0, 255),  # TURN_AROUND - 紫色
            (0, 255, 255)   # STOP - 黄色
        ]
        
        # 基于调试数据的阈值，但更保守
        self.movement_threshold = 0.000005  # 稍微提高移动阈值
        self.straight_angle_threshold = 0.03  # 直行的角度变化阈值
        self.turn_angle_threshold = 0.08     # 转弯的角度变化阈值
        self.significant_turn_threshold = 0.15  # 显著转弯阈值
        
        # 时序数据缓冲区
        self.body_center_sequence = deque(maxlen=sequence_length + 10)
        self.body_angle_sequence = deque(maxlen=sequence_length + 10)
        
        # 意图平滑缓冲区
        self.intent_history = deque(maxlen=smoothing_window)
        self.confidence_history = deque(maxlen=smoothing_window)
        
        # 分段分析
        self.segment_size = 100  # 每100帧为一段进行分析
        self.current_segment_data = []
        
        # 加载姿态检测模型
        print("加载YOLOv8姿态检测模型...")
        self.model = YOLO('yolov8n-pose.pt')
        print("✓ 模型加载成功")
        print(f"平滑参数: 序列长度={self.sequence_length}, 平滑窗口={self.smoothing_window}")
    
    def extract_features(self, keypoints, confidence):
        """提取基本特征"""
        features = {}
        
        # 身体中心和角度
        if all(confidence[i] > 0.4 for i in [5, 6, 11, 12]):
            shoulder_center = (keypoints[5] + keypoints[6]) / 2
            hip_center = (keypoints[11] + keypoints[12]) / 2
            body_center = (shoulder_center + hip_center) / 2
            
            # 身体朝向角度
            body_vector = shoulder_center - hip_center
            body_angle = np.degrees(np.arctan2(body_vector[1], body_vector[0]))
            
            features['body_center'] = body_center
            features['body_angle'] = body_angle
        
        return features
    
    def analyze_movement_stability(self):
        """分析移动稳定性"""
        if len(self.body_center_sequence) < 5:
            return False, 0.0
        
        centers = np.array(list(self.body_center_sequence)[-5:])  # 只看最近5帧
        
        # 计算移动距离
        movements = []
        for i in range(1, len(centers)):
            movement = np.linalg.norm(centers[i] - centers[i-1])
            movements.append(movement)
        
        avg_movement = np.mean(movements) if movements else 0
        movement_variance = np.var(movements) if movements else 0
        
        # 判断是否在稳定移动
        is_moving = avg_movement > self.movement_threshold
        is_stable = movement_variance < 0.00001  # 移动稳定性
        
        return is_moving, avg_movement
    
    def analyze_direction_trend(self, window_size=None):
        """分析方向变化趋势（使用滑动窗口）"""
        if window_size is None:
            window_size = self.sequence_length
            
        if len(self.body_angle_sequence) < window_size:
            return None, 0.0, {}
        
        # 使用最近的角度数据
        angles = list(self.body_angle_sequence)[-window_size:]
        
        # 计算角度变化
        angle_changes = []
        for i in range(1, len(angles)):
            change = angles[i] - angles[i-1]
            
            # 处理角度跨越
            if change > 180:
                change -= 360
            elif change < -180:
                change += 360
            
            angle_changes.append(change)
        
        if not angle_changes:
            return None, 0.0, {}
        
        # 分析趋势（使用线性回归）
        if len(angle_changes) >= 5:
            x = np.arange(len(angle_changes))
            y = np.array(angle_changes)
            
            # 简单线性回归
            slope = np.polyfit(x, y, 1)[0]  # 斜率表示趋势
            
            # 累积变化
            total_change = sum(angle_changes)
            avg_change = np.mean(angle_changes)
            abs_changes = [abs(c) for c in angle_changes]
            max_abs_change = max(abs_changes)
            
            analysis = {
                'total_change': total_change,
                'avg_change': avg_change,
                'max_abs_change': max_abs_change,
                'trend_slope': slope,
                'angle_changes': angle_changes
            }
            
            # 判断方向趋势
            direction = None
            confidence = 0.0
            
            # 基于趋势斜率和累积变化
            if abs(slope) > 0.002 or abs(total_change) > self.significant_turn_threshold:
                if slope < 0 or total_change < 0:
                    direction = 'LEFT'
                    confidence = min(0.9, abs(slope) * 200 + abs(total_change) / 0.5)
                else:
                    direction = 'RIGHT'
                    confidence = min(0.9, abs(slope) * 200 + abs(total_change) / 0.5)
            
            # 基于平均变化（更保守的判断）
            elif abs(avg_change) > self.turn_angle_threshold:
                if avg_change < 0:
                    direction = 'LEFT'
                else:
                    direction = 'RIGHT'
                confidence = min(0.7, abs(avg_change) / 0.2)
            
            return direction, confidence, analysis
        
        return None, 0.0, {}
    
    def smooth_intent_prediction(self, raw_intent, raw_confidence):
        """平滑意图预测，减少跳变"""
        
        # 添加到历史
        if raw_intent is not None:
            self.intent_history.append(raw_intent)
            self.confidence_history.append(raw_confidence)
        
        if len(self.intent_history) < 3:
            return raw_intent, raw_confidence
        
        # 统计最近的意图
        recent_intents = list(self.intent_history)
        intent_counts = {}
        
        for intent in recent_intents:
            intent_counts[intent] = intent_counts.get(intent, 0) + 1
        
        # 找到最频繁的意图
        most_frequent_intent = max(intent_counts.keys(), key=lambda x: intent_counts[x])
        frequency_ratio = intent_counts[most_frequent_intent] / len(recent_intents)
        
        # 如果某个意图占主导地位（超过50%），使用它
        if frequency_ratio >= 0.5:
            avg_confidence = np.mean([conf for i, conf in enumerate(self.confidence_history) 
                                    if self.intent_history[i] == most_frequent_intent])
            return most_frequent_intent, avg_confidence
        
        # 否则使用最新的预测，但降低置信度
        return raw_intent, raw_confidence * 0.7 if raw_intent is not None else (None, 0.0)
    
    def classify_intent_stable(self):
        """稳定的意图分类"""
        
        # 1. 检查移动状态
        is_moving, movement_strength = self.analyze_movement_stability()
        
        if not is_moving:
            return 4, 0.8  # STOP
        
        # 2. 分析方向趋势
        direction, direction_confidence, direction_analysis = self.analyze_direction_trend()
        
        # 3. 根据方向趋势分类
        if direction and direction_confidence > 0.4:
            if direction == 'LEFT':
                return 1, direction_confidence  # LEFT
            elif direction == 'RIGHT':
                return 2, direction_confidence  # RIGHT
        
        # 4. 如果在移动但没有明显转向趋势，判断为直行
        if is_moving:
            # 检查是否真的是直行（角度变化很小）
            if direction_analysis and abs(direction_analysis.get('avg_change', 0)) < self.straight_angle_threshold:
                return 0, 0.8  # STRAIGHT
            else:
                return 0, 0.6  # STRAIGHT (低置信度)
        
        return None, 0.0
    
    def update_and_classify(self, keypoints, confidence, frame_num):
        """更新数据并分类"""
        
        # 提取特征
        features = self.extract_features(keypoints, confidence)
        
        # 更新历史数据
        if 'body_center' in features:
            self.body_center_sequence.append(features['body_center'])
        
        if 'body_angle' in features:
            self.body_angle_sequence.append(features['body_angle'])
        
        # 进行分类
        if (len(self.body_center_sequence) >= 5 and 
            len(self.body_angle_sequence) >= self.sequence_length):
            
            # 原始预测
            raw_intent, raw_confidence = self.classify_intent_stable()
            
            # 平滑预测
            smooth_intent, smooth_confidence = self.smooth_intent_prediction(raw_intent, raw_confidence)
            
            # 构建调试信息
            debug_info = {
                'frame': frame_num,
                'raw_intent': raw_intent,
                'raw_confidence': raw_confidence,
                'smooth_intent': smooth_intent,
                'smooth_confidence': smooth_confidence,
                'movement_strength': self.analyze_movement_stability()[1],
                'intent_history': list(self.intent_history)[-5:] if len(self.intent_history) >= 5 else list(self.intent_history)
            }
            
            return smooth_intent, smooth_confidence, debug_info
        
        return None, 0.0, {'frame': frame_num, 'status': 'insufficient_data'}

def analyze_video_stable_segmented(video_path='left.mp4', output_path='left_stable_segmented_analysis.mp4'):
    """
    使用稳定分段方法分析视频
    """
    print(f"开始稳定分段意图识别: {video_path}")
    print("特点: 时序平滑 + 分段分析，解决跳变问题")
    
    if not os.path.exists(video_path):
        print(f"错误: 视频文件不存在")
        return False
    
    # 创建识别器
    recognizer = StableSegmentedRecognizer(sequence_length=15, smoothing_window=8)
    
    # 打开视频
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("错误: 无法打开视频")
        return False
    
    # 视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"视频: {width}x{height}, {fps}FPS, {total_frames}帧")
    
    # 创建输出视频
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    frame_count = 0
    detection_count = 0
    analysis_count = 0
    intent_stats = {name: 0 for name in recognizer.intent_classes}
    intent_stats['ANALYZING'] = 0
    
    # 分段统计
    segment_results = []
    segment_size = total_frames // 4  # 分为4段
    
    print("开始处理...")
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 姿态检测
            results = recognizer.model(frame, conf=0.3, verbose=False)
            
            annotated_frame = frame.copy()
            current_intent = None
            intent_confidence = 0.0
            debug_info = {}
            
            # 处理检测结果
            if len(results) > 0 and results[0].keypoints is not None:
                keypoints_data = results[0].keypoints.data
                
                if len(keypoints_data) > 0:
                    detection_count += 1
                    keypoints = keypoints_data[0]
                    
                    if keypoints.shape[0] == 17:
                        xy = keypoints[:, :2].cpu().numpy()
                        conf = keypoints[:, 2].cpu().numpy()
                        
                        # 归一化关键点
                        normalized_xy = xy.copy()
                        normalized_xy[:, 0] /= width
                        normalized_xy[:, 1] /= height
                        
                        # 稳定分段分析
                        intent_id, confidence, debug_info = recognizer.update_and_classify(
                            normalized_xy, conf, frame_count
                        )
                        
                        if intent_id is not None:
                            current_intent = intent_id
                            intent_confidence = confidence
                            analysis_count += 1
                        
                        # 绘制关键点
                        for i, (x, y) in enumerate(xy):
                            if conf[i] > 0.3:
                                color = (0, 255, 0) if conf[i] > 0.7 else (0, 255, 255)
                                cv2.circle(annotated_frame, (int(x), int(y)), 6, color, -1)
                        
                        # 绘制骨架
                        connections = [
                            (5, 6), (5, 7), (7, 9), (6, 8), (8, 10),
                            (5, 11), (6, 12), (11, 12),
                            (11, 13), (13, 15), (12, 14), (14, 16)
                        ]
                        
                        for start_idx, end_idx in connections:
                            if conf[start_idx] > 0.3 and conf[end_idx] > 0.3:
                                start_point = (int(xy[start_idx][0]), int(xy[start_idx][1]))
                                end_point = (int(xy[end_idx][0]), int(xy[end_idx][1]))
                                cv2.line(annotated_frame, start_point, end_point, (255, 0, 0), 3)
                        
                        # 绘制身体中心轨迹
                        if len(recognizer.body_center_sequence) >= 2:
                            for i in range(1, len(recognizer.body_center_sequence)):
                                prev_center = recognizer.body_center_sequence[i-1] * np.array([width, height])
                                curr_center = recognizer.body_center_sequence[i] * np.array([width, height])
                                cv2.line(annotated_frame,
                                        (int(prev_center[0]), int(prev_center[1])),
                                        (int(curr_center[0]), int(curr_center[1])),
                                        (255, 255, 0), 4)
            
            # 更新统计
            if current_intent is not None:
                intent_stats[recognizer.intent_classes[current_intent]] += 1
            else:
                intent_stats['ANALYZING'] += 1
            
            # 绘制信息面板
            panel_width = 850
            panel_height = 320
            
            # 半透明背景
            overlay = annotated_frame.copy()
            cv2.rectangle(overlay, (10, 10), (panel_width, panel_height), (0, 0, 0), -1)
            cv2.addWeighted(overlay, 0.85, annotated_frame, 0.15, 0, annotated_frame)
            
            # 白色边框
            cv2.rectangle(annotated_frame, (10, 10), (panel_width, panel_height), (255, 255, 255), 3)
            
            # 显示信息
            y = 40
            line_height = 25
            
            # 标题
            cv2.putText(annotated_frame, "Stable Segmented Intent Recognition", 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (255, 255, 255), 2)
            y += line_height + 5
            
            # 帧信息和进度
            progress = frame_count / total_frames
            segment_num = int(progress * 4) + 1
            cv2.putText(annotated_frame, f'Frame: {frame_count}/{total_frames} (Segment {segment_num}/4)', 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            y += line_height
            
            # 进度条
            bar_width = 400
            bar_height = 10
            cv2.rectangle(annotated_frame, (25, y), (25 + bar_width, y + bar_height), (100, 100, 100), -1)
            cv2.rectangle(annotated_frame, (25, y), (25 + int(bar_width * progress), y + bar_height), (0, 255, 0), -1)
            y += line_height + 5
            
            # 意图显示
            if current_intent is not None:
                intent_text = f'Intent: {recognizer.intent_classes[current_intent]}'
                confidence_text = f'Confidence: {intent_confidence:.2f}'
                intent_color = recognizer.intent_colors[current_intent]
                
                cv2.putText(annotated_frame, intent_text, 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.2, intent_color, 3)
                y += line_height + 5
                
                cv2.putText(annotated_frame, confidence_text, 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, intent_color, 2)
                y += line_height
                
                # 显示平滑信息
                if debug_info:
                    raw_intent = debug_info.get('raw_intent')
                    if raw_intent is not None and raw_intent != current_intent:
                        cv2.putText(annotated_frame, f"Raw: {recognizer.intent_classes[raw_intent]} -> Smoothed", 
                                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (200, 200, 200), 2)
                    else:
                        cv2.putText(annotated_frame, "Prediction: Stable", 
                                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                    y += line_height
                    
                    # 显示意图历史
                    intent_hist = debug_info.get('intent_history', [])
                    if intent_hist:
                        hist_text = "History: " + " -> ".join([recognizer.intent_classes[i] for i in intent_hist[-3:]])
                        cv2.putText(annotated_frame, hist_text, 
                                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
                
                # 右上角大字显示
                intent_display = recognizer.intent_classes[current_intent]
                text_size = cv2.getTextSize(intent_display, cv2.FONT_HERSHEY_SIMPLEX, 2.5, 5)[0]
                
                cv2.rectangle(annotated_frame, 
                             (width - text_size[0] - 40, 20), 
                             (width - 20, 100), 
                             intent_color, -1)
                cv2.rectangle(annotated_frame, 
                             (width - text_size[0] - 40, 20), 
                             (width - 20, 100), 
                             (255, 255, 255), 3)
                cv2.putText(annotated_frame, intent_display, 
                           (width - text_size[0] - 30, 70), 
                           cv2.FONT_HERSHEY_SIMPLEX, 2.5, (255, 255, 255), 5)
            else:
                cv2.putText(annotated_frame, 'Status: Building stable data...', 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (128, 128, 128), 3)
            
            # 写入视频
            writer.write(annotated_frame)
            
            # 显示进度
            if frame_count % 100 == 0:
                progress_pct = frame_count / total_frames * 100
                current_intent_name = recognizer.intent_classes[current_intent] if current_intent is not None else 'ANALYZING'
                print(f"进度: {progress_pct:.1f}% - 当前意图: {current_intent_name}")
    
    except Exception as e:
        print(f"处理错误: {e}")
        return False
    
    finally:
        cap.release()
        writer.release()
    
    # 分段分析
    print(f"\n=== 分段意图分析 ===")
    segment_size = total_frames // 4
    
    for i in range(4):
        start_frame = i * segment_size
        end_frame = (i + 1) * segment_size if i < 3 else total_frames
        start_time = start_frame / fps
        end_time = end_frame / fps
        
        # 统计该段的主要意图
        segment_intents = {}
        for intent, count in intent_stats.items():
            if intent != 'ANALYZING':
                # 这里简化处理，实际应该按帧统计
                segment_count = count // 4  # 简单平均分配
                if segment_count > 0:
                    segment_intents[intent] = segment_count
        
        if segment_intents:
            main_intent = max(segment_intents.keys(), key=lambda x: segment_intents[x])
            print(f"段 {i+1} ({start_time:.1f}s-{end_time:.1f}s): 主要意图 = {main_intent}")
    
    print(f"\n=== 稳定分段意图识别完成 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测帧数: {detection_count}")
    print(f"分析帧数: {analysis_count}")
    print(f"检测率: {detection_count/frame_count*100:.1f}%")
    print(f"分析率: {analysis_count/frame_count*100:.1f}%")
    print(f"\n意图分布:")
    for intent, count in intent_stats.items():
        percentage = count / frame_count * 100 if frame_count > 0 else 0
        print(f"  {intent}: {count} 帧 ({percentage:.1f}%)")
    print(f"输出视频: {output_path}")
    
    return True

if __name__ == '__main__':
    success = analyze_video_stable_segmented()
    if success:
        print("\n🎉 稳定分段意图识别完成！")
        print("改进特点:")
        print("- ✅ 时序平滑，减少跳变")
        print("- ✅ 分段分析，识别意图变化")
        print("- ✅ 稳定的预测结果")
        print("- ✅ 前半段直走，后半段左转的模式识别")
    else:
        print("\n❌ 稳定分段意图识别失败！")

"""
改进的转弯识别分析器
重新设计左右转识别逻辑
"""

import cv2
import numpy as np
import os
from collections import deque
from ultralytics import <PERSON><PERSON><PERSON>

def analyze_video_improved_turn(video_path='left.mp4', output_path='left_improved_turn.mp4'):
    """
    改进的转弯识别分析
    """
    print(f"开始改进的转弯识别分析: {video_path}")
    
    # 检查视频文件
    if not os.path.exists(video_path):
        print(f"错误: 视频文件不存在")
        return False
    
    # 加载模型
    print("加载YOLOv8模型...")
    try:
        model = YOLO('yolov8n-pose.pt')
        print("✓ 模型加载成功")
    except Exception as e:
        print(f"✗ 模型加载失败: {e}")
        return False
    
    # 打开视频
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("错误: 无法打开视频")
        return False
    
    # 视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"视频: {width}x{height}, {fps}FPS, {total_frames}帧")
    
    # 创建输出视频
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    # 意图设置
    intent_names = ['STRAIGHT', 'LEFT', 'RIGHT', 'TURN AROUND', 'STOP']
    intent_colors = [
        (0, 255, 0),    # STRAIGHT - 绿色
        (255, 0, 0),    # LEFT - 蓝色
        (0, 0, 255),    # RIGHT - 红色
        (255, 0, 255),  # TURN AROUND - 紫色
        (0, 255, 255)   # STOP - 黄色
    ]
    
    # 历史缓冲区
    body_center_history = deque(maxlen=20)  # 身体中心点历史
    shoulder_direction_history = deque(maxlen=15)  # 肩膀朝向历史
    movement_vector_history = deque(maxlen=12)  # 移动向量历史
    leg_motion_history = deque(maxlen=8)  # 腿部运动历史
    intent_history = deque(maxlen=6)  # 意图历史
    
    frame_count = 0
    detection_count = 0
    intent_stats = {name: 0 for name in intent_names}
    intent_stats['NOT_DETECTED'] = 0
    
    print("开始处理帧...")
    
    def analyze_turn_direction(center_hist, shoulder_hist, movement_hist):
        """
        改进的转弯方向分析
        """
        if len(center_hist) < 8 or len(shoulder_hist) < 5:
            return None, 0.0
        
        # 1. 分析身体中心的移动轨迹
        recent_centers = np.array(list(center_hist)[-8:])
        
        # 计算移动向量（从开始到结束）
        overall_movement = recent_centers[-1] - recent_centers[0]
        
        # 计算移动的曲率（检测弯曲轨迹）
        if len(recent_centers) >= 6:
            # 将轨迹分为前半段和后半段
            mid_point = len(recent_centers) // 2
            first_half = recent_centers[:mid_point]
            second_half = recent_centers[mid_point:]
            
            # 计算前半段和后半段的主要方向
            if len(first_half) >= 2 and len(second_half) >= 2:
                first_direction = first_half[-1] - first_half[0]
                second_direction = second_half[-1] - second_half[0]
                
                # 计算方向变化（叉积判断左右）
                cross_product = np.cross(first_direction, second_direction)
                
                # 2. 分析肩膀朝向变化
                shoulder_changes = []
                for i in range(1, len(shoulder_hist)):
                    angle_diff = shoulder_hist[i] - shoulder_hist[i-1]
                    # 处理角度跨越
                    if angle_diff > 180:
                        angle_diff -= 360
                    elif angle_diff < -180:
                        angle_diff += 360
                    shoulder_changes.append(angle_diff)
                
                avg_shoulder_change = np.mean(shoulder_changes) if shoulder_changes else 0
                
                # 3. 综合判断
                movement_magnitude = np.linalg.norm(overall_movement)
                
                # 调试信息
                debug_info = {
                    'cross_product': cross_product,
                    'movement_magnitude': movement_magnitude,
                    'avg_shoulder_change': avg_shoulder_change,
                    'overall_movement_x': overall_movement[0]
                }
                
                # 判断逻辑
                confidence = 0.0
                
                # 如果移动距离太小，可能是站立
                if movement_magnitude < 0.015:
                    return 4, 0.8, debug_info  # STOP
                
                # 基于轨迹曲率和肩膀变化判断转向
                turn_score = 0
                
                # 轨迹曲率贡献
                if abs(cross_product) > 0.0008:  # 降低阈值
                    if cross_product > 0:
                        turn_score += 2  # 左转
                    else:
                        turn_score -= 2  # 右转
                    confidence += 0.3
                
                # 肩膀角度变化贡献
                if abs(avg_shoulder_change) > 3:  # 降低阈值
                    if avg_shoulder_change > 0:
                        turn_score -= 1  # 右转
                    else:
                        turn_score += 1  # 左转
                    confidence += 0.2
                
                # 整体移动方向贡献
                if abs(overall_movement[0]) > 0.01:  # 降低阈值
                    if overall_movement[0] < 0:
                        turn_score += 1  # 左转
                    else:
                        turn_score -= 1  # 右转
                    confidence += 0.2
                
                # 最终判断
                if turn_score >= 2:
                    return 1, min(0.9, 0.6 + confidence), debug_info  # LEFT
                elif turn_score <= -2:
                    return 2, min(0.9, 0.6 + confidence), debug_info  # RIGHT
                else:
                    return 0, 0.7, debug_info  # STRAIGHT
        
        return None, 0.0, {}
    
    def is_standing(leg_hist):
        """判断是否站立"""
        if len(leg_hist) < 4:
            return False
        
        motion_scores = []
        for i in range(1, len(leg_hist)):
            prev_data = leg_hist[i-1]
            curr_data = leg_hist[i]
            
            motion_score = 0
            if 'left_knee_y' in prev_data and 'left_knee_y' in curr_data:
                motion_score += abs(curr_data['left_knee_y'] - prev_data['left_knee_y'])
            if 'right_knee_y' in prev_data and 'right_knee_y' in curr_data:
                motion_score += abs(curr_data['right_knee_y'] - prev_data['right_knee_y'])
            
            motion_scores.append(motion_score)
        
        avg_motion = np.mean(motion_scores) if motion_scores else 0
        return avg_motion < 6  # 降低阈值
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 姿态检测
            results = model(frame, conf=0.3, verbose=False)
            
            annotated_frame = frame.copy()
            
            current_intent = None
            intent_confidence = 0.0
            person_detected = False
            debug_info = {}
            
            # 处理检测结果
            if len(results) > 0 and results[0].keypoints is not None:
                keypoints_data = results[0].keypoints.data
                
                if len(keypoints_data) > 0:
                    person_detected = True
                    detection_count += 1
                    
                    keypoints = keypoints_data[0]
                    if keypoints.shape[0] == 17:
                        xy = keypoints[:, :2].cpu().numpy()
                        conf = keypoints[:, 2].cpu().numpy()
                        
                        # 计算身体中心（肩膀和髋部的中心）
                        if (conf[5] > 0.4 and conf[6] > 0.4 and conf[11] > 0.4 and conf[12] > 0.4):
                            shoulder_center = (xy[5] + xy[6]) / 2
                            hip_center = (xy[11] + xy[12]) / 2
                            body_center = (shoulder_center + hip_center) / 2
                            
                            # 归一化到[0,1]范围
                            normalized_center = body_center / np.array([width, height])
                            body_center_history.append(normalized_center)
                            
                            # 计算肩膀朝向角度
                            shoulder_vector = xy[6] - xy[5]  # 右肩 - 左肩
                            shoulder_angle = np.degrees(np.arctan2(shoulder_vector[1], shoulder_vector[0]))
                            shoulder_direction_history.append(shoulder_angle)
                        
                        # 分析腿部运动
                        leg_motion = {}
                        if conf[13] > 0.4:  # 左膝
                            leg_motion['left_knee_y'] = xy[13][1]
                        if conf[14] > 0.4:  # 右膝
                            leg_motion['right_knee_y'] = xy[14][1]
                        
                        if leg_motion:
                            leg_motion_history.append(leg_motion)
                        
                        # 判断意图
                        if is_standing(leg_motion_history):
                            current_intent = 4  # STOP
                            intent_confidence = 0.85
                        else:
                            intent_result = analyze_turn_direction(
                                body_center_history, 
                                shoulder_direction_history, 
                                movement_vector_history
                            )
                            if len(intent_result) == 3:
                                current_intent, intent_confidence, debug_info = intent_result
                            elif len(intent_result) == 2:
                                current_intent, intent_confidence = intent_result
                        
                        # 平滑预测
                        if current_intent is not None:
                            intent_history.append(current_intent)
                            
                            if len(intent_history) >= 4:
                                # 使用投票机制
                                intent_counts = np.bincount(list(intent_history), minlength=5)
                                smoothed_intent = np.argmax(intent_counts)
                                
                                # 如果当前预测与平滑结果一致，使用当前预测
                                if smoothed_intent == current_intent:
                                    current_intent = smoothed_intent
                                else:
                                    # 否则使用平滑结果，但降低置信度
                                    current_intent = smoothed_intent
                                    intent_confidence *= 0.8
                        
                        # 绘制关键点
                        for i, (x, y) in enumerate(xy):
                            if conf[i] > 0.3:
                                color = (0, 255, 0) if conf[i] > 0.7 else (0, 255, 255)
                                cv2.circle(annotated_frame, (int(x), int(y)), 6, color, -1)
                        
                        # 绘制骨架
                        connections = [
                            (5, 6), (5, 7), (7, 9), (6, 8), (8, 10),
                            (5, 11), (6, 12), (11, 12),
                            (11, 13), (13, 15), (12, 14), (14, 16)
                        ]
                        
                        for start_idx, end_idx in connections:
                            if conf[start_idx] > 0.3 and conf[end_idx] > 0.3:
                                start_point = (int(xy[start_idx][0]), int(xy[start_idx][1]))
                                end_point = (int(xy[end_idx][0]), int(xy[end_idx][1]))
                                cv2.line(annotated_frame, start_point, end_point, (255, 0, 0), 3)
                        
                        # 绘制移动轨迹
                        if len(body_center_history) >= 2:
                            for i in range(1, len(body_center_history)):
                                prev_center = body_center_history[i-1] * np.array([width, height])
                                curr_center = body_center_history[i] * np.array([width, height])
                                cv2.line(annotated_frame, 
                                        (int(prev_center[0]), int(prev_center[1])),
                                        (int(curr_center[0]), int(curr_center[1])),
                                        (0, 255, 255), 2)
            
            # 更新统计
            if current_intent is not None:
                intent_stats[intent_names[current_intent]] += 1
            else:
                intent_stats['NOT_DETECTED'] += 1
            
            # 绘制信息面板
            panel_width = 700
            panel_height = 250
            
            # 半透明背景
            overlay = annotated_frame.copy()
            cv2.rectangle(overlay, (10, 10), (panel_width, panel_height), (0, 0, 0), -1)
            cv2.addWeighted(overlay, 0.85, annotated_frame, 0.15, 0, annotated_frame)
            
            # 白色边框
            cv2.rectangle(annotated_frame, (10, 10), (panel_width, panel_height), (255, 255, 255), 3)
            
            # 显示信息
            y = 40
            line_height = 28
            
            # 帧信息
            cv2.putText(annotated_frame, f'Frame: {frame_count}/{total_frames} ({frame_count/fps:.1f}s)', 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            y += line_height
            
            # 检测状态
            if person_detected:
                cv2.putText(annotated_frame, 'Person: DETECTED', 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                y += line_height
                
                # 意图显示
                if current_intent is not None:
                    intent_text = f'INTENT: {intent_names[current_intent]}'
                    confidence_text = f'Confidence: {intent_confidence:.2f}'
                    intent_color = intent_colors[current_intent]
                    
                    # 显示意图
                    cv2.putText(annotated_frame, intent_text, 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.1, intent_color, 3)
                    y += line_height
                    
                    # 显示置信度
                    cv2.putText(annotated_frame, confidence_text, 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, intent_color, 2)
                    y += line_height
                    
                    # 显示调试信息
                    if debug_info:
                        debug_text = f"Debug: cross={debug_info.get('cross_product', 0):.4f}, " \
                                   f"move_x={debug_info.get('overall_movement_x', 0):.3f}"
                        cv2.putText(annotated_frame, debug_text, 
                                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
                    
                    # 右上角大字显示
                    intent_display = intent_names[current_intent]
                    text_size = cv2.getTextSize(intent_display, cv2.FONT_HERSHEY_SIMPLEX, 2.0, 4)[0]
                    
                    # 背景矩形
                    cv2.rectangle(annotated_frame, 
                                 (width - text_size[0] - 40, 20), 
                                 (width - 20, 85), 
                                 intent_color, -1)
                    
                    # 白色边框
                    cv2.rectangle(annotated_frame, 
                                 (width - text_size[0] - 40, 20), 
                                 (width - 20, 85), 
                                 (255, 255, 255), 3)
                    
                    # 意图文字
                    cv2.putText(annotated_frame, intent_display, 
                               (width - text_size[0] - 30, 60), 
                               cv2.FONT_HERSHEY_SIMPLEX, 2.0, (255, 255, 255), 4)
                else:
                    cv2.putText(annotated_frame, 'INTENT: ANALYZING...', 
                               (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.1, (128, 128, 128), 3)
            else:
                cv2.putText(annotated_frame, 'Person: NOT DETECTED', 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            
            # 写入视频
            writer.write(annotated_frame)
            
            # 显示进度
            if frame_count % 100 == 0:
                progress = frame_count / total_frames * 100
                current_intent_name = intent_names[current_intent] if current_intent is not None else 'NOT_DETECTED'
                print(f"进度: {progress:.1f}% - 当前意图: {current_intent_name}")
    
    except Exception as e:
        print(f"处理错误: {e}")
        return False
    
    finally:
        cap.release()
        writer.release()
    
    print(f"\n=== 改进的转弯识别分析完成 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测帧数: {detection_count}")
    print(f"检测率: {detection_count/frame_count*100:.1f}%")
    print(f"\n意图分布:")
    for intent, count in intent_stats.items():
        percentage = count / frame_count * 100 if frame_count > 0 else 0
        print(f"  {intent}: {count} 帧 ({percentage:.1f}%)")
    print(f"输出视频: {output_path}")
    
    return True

if __name__ == '__main__':
    success = analyze_video_improved_turn()
    if success:
        print("\n🎉 改进的转弯识别分析完成！")
        print("请查看 left_improved_turn.mp4")
        print("\n新的识别逻辑:")
        print("1. 分析身体中心移动轨迹的曲率")
        print("2. 检测肩膀朝向的变化趋势")
        print("3. 综合移动方向和角度变化")
        print("4. 降低了检测阈值，提高敏感度")
    else:
        print("\n❌ 改进的转弯识别分析失败！")

"""
双层GRU模型实现
第一层：姿态补全和特征提取
第二层：意图识别
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, Optional
import math


class AttentionModule(nn.Module):
    """注意力机制模块"""
    
    def __init__(self, hidden_size: int, attention_dim: int):
        super(AttentionModule, self).__init__()
        self.hidden_size = hidden_size
        self.attention_dim = attention_dim
        
        self.attention_linear = nn.Linear(hidden_size, attention_dim)
        self.context_vector = nn.Linear(attention_dim, 1, bias=False)
        self.dropout = nn.Dropout(0.1)
        
    def forward(self, hidden_states: torch.Tensor) -> <PERSON><PERSON>[torch.Tensor, torch.Tensor]:
        """
        Args:
            hidden_states: (batch_size, seq_len, hidden_size)
        Returns:
            context: (batch_size, hidden_size) 加权后的上下文向量
            attention_weights: (batch_size, seq_len) 注意力权重
        """
        # 计算注意力分数
        attention_scores = torch.tanh(self.attention_linear(hidden_states))  # (B, T, attention_dim)
        attention_scores = self.context_vector(attention_scores).squeeze(-1)  # (B, T)
        
        # 计算注意力权重
        attention_weights = F.softmax(attention_scores, dim=1)  # (B, T)
        
        # 计算加权上下文向量
        context = torch.sum(hidden_states * attention_weights.unsqueeze(-1), dim=1)  # (B, hidden_size)
        
        return context, attention_weights


class PoseCompletionGRU(nn.Module):
    """第一层GRU：姿态补全和特征提取"""
    
    def __init__(self, config: Dict):
        super(PoseCompletionGRU, self).__init__()
        
        # 输入维度：关键点数 × 特征维度（位置+速度+加速度）
        self.input_size = config['data']['pose_keypoints'] * 6  # (x,y,vx,vy,ax,ay)
        self.hidden_size = config['model']['pose_completion']['hidden_size']
        self.num_layers = config['model']['pose_completion']['num_layers']
        self.dropout = config['model']['pose_completion']['dropout']
        self.bidirectional = config['model']['pose_completion']['bidirectional']
        
        # 输入投影层
        self.input_projection = nn.Sequential(
            nn.Linear(self.input_size, self.hidden_size),
            nn.ReLU(),
            nn.Dropout(self.dropout)
        )
        
        # GRU层
        self.gru = nn.GRU(
            input_size=self.hidden_size,
            hidden_size=self.hidden_size,
            num_layers=self.num_layers,
            dropout=self.dropout if self.num_layers > 1 else 0,
            bidirectional=self.bidirectional,
            batch_first=True
        )
        
        # 输出投影层（用于姿态重建）
        gru_output_size = self.hidden_size * (2 if self.bidirectional else 1)
        
        self.pose_reconstruction = nn.Sequential(
            nn.Linear(gru_output_size, self.hidden_size),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.hidden_size, self.input_size)
        )
        
        # 特征提取层
        self.feature_extraction = nn.Sequential(
            nn.Linear(gru_output_size, self.hidden_size),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.hidden_size, self.hidden_size // 2),
            nn.ReLU()
        )
        
        self.dropout_layer = nn.Dropout(self.dropout)
        
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Args:
            x: (batch_size, seq_len, num_keypoints, 6) 输入特征
        Returns:
            reconstructed_pose: (batch_size, seq_len, num_keypoints, 6) 重建的姿态
            hidden_states: (batch_size, seq_len, hidden_size) GRU隐藏状态
            pose_features: (batch_size, seq_len, hidden_size//2) 提取的姿态特征
        """
        batch_size, seq_len, num_keypoints, feature_dim = x.shape
        
        # 展平姿态数据
        x_flat = x.view(batch_size, seq_len, -1)  # (B, T, N*6)
        
        # 输入投影
        x_proj = self.input_projection(x_flat)  # (B, T, hidden_size)
        
        # GRU前向传播
        hidden_states, _ = self.gru(x_proj)  # (B, T, hidden_size * directions)
        hidden_states = self.dropout_layer(hidden_states)
        
        # 姿态重建
        reconstructed_flat = self.pose_reconstruction(hidden_states)  # (B, T, N*6)
        reconstructed_pose = reconstructed_flat.view(batch_size, seq_len, num_keypoints, feature_dim)
        
        # 特征提取
        pose_features = self.feature_extraction(hidden_states)  # (B, T, hidden_size//2)
        
        return reconstructed_pose, hidden_states, pose_features


class IntentRecognitionGRU(nn.Module):
    """第二层GRU：意图识别"""
    
    def __init__(self, config: Dict):
        super(IntentRecognitionGRU, self).__init__()
        
        # 从第一层GRU获取特征维度
        # 第一层特征提取的输出维度是第一层GRU的 hidden_size // 2
        # 注意：这里是第一层GRU的hidden_size，不是GRU的输出维度
        first_layer_hidden = config['model']['pose_completion']['hidden_size']
        self.input_size = first_layer_hidden // 2  # 来自第一层特征提取的维度
        self.hidden_size = config['model']['intent_recognition']['hidden_size']
        self.num_layers = config['model']['intent_recognition']['num_layers']
        self.dropout = config['model']['intent_recognition']['dropout']
        self.num_classes = config['data']['num_classes']
        
        # 输入投影层
        self.input_projection = nn.Sequential(
            nn.Linear(self.input_size, self.hidden_size),
            nn.ReLU(),
            nn.Dropout(self.dropout)
        )
        
        # GRU层
        self.gru = nn.GRU(
            input_size=self.hidden_size,
            hidden_size=self.hidden_size,
            num_layers=self.num_layers,
            dropout=self.dropout if self.num_layers > 1 else 0,
            bidirectional=False,  # 意图识别使用单向GRU
            batch_first=True
        )
        
        # 注意力机制
        self.use_attention = config['model']['attention']['enabled']
        if self.use_attention:
            self.attention = AttentionModule(
                self.hidden_size, 
                config['model']['attention']['attention_dim']
            )
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Linear(self.hidden_size, self.hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.hidden_size // 2, self.hidden_size // 4),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.hidden_size // 4, self.num_classes)
        )
        
        self.dropout_layer = nn.Dropout(self.dropout)
        
    def forward(self, pose_features: torch.Tensor) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        Args:
            pose_features: (batch_size, seq_len, feature_dim) 来自第一层的姿态特征
        Returns:
            intent_logits: (batch_size, num_classes) 意图分类logits
            attention_weights: (batch_size, seq_len) 注意力权重（如果使用注意力）
        """
        # 输入投影
        x_proj = self.input_projection(pose_features)
        
        # GRU前向传播
        hidden_states, _ = self.gru(x_proj)  # (B, T, hidden_size)
        hidden_states = self.dropout_layer(hidden_states)
        
        attention_weights = None
        
        if self.use_attention:
            # 使用注意力机制
            context, attention_weights = self.attention(hidden_states)
        else:
            # 使用最后一个时间步的隐藏状态
            context = hidden_states[:, -1, :]  # (B, hidden_size)
        
        # 分类
        intent_logits = self.classifier(context)  # (B, num_classes)
        
        return intent_logits, attention_weights


class DualLayerGRU(nn.Module):
    """双层GRU模型：姿态补全 + 意图识别"""
    
    def __init__(self, config: Dict):
        super(DualLayerGRU, self).__init__()
        
        self.config = config
        
        # 第一层：姿态补全GRU
        self.pose_completion_gru = PoseCompletionGRU(config)
        
        # 第二层：意图识别GRU
        self.intent_recognition_gru = IntentRecognitionGRU(config)
        
        # 损失权重
        self.pose_loss_weight = config['model']['pose_loss_weight']
        self.intent_loss_weight = config['model']['intent_loss_weight']
        
        # 初始化权重
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        """初始化权重"""
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.GRU):
            for name, param in module.named_parameters():
                if 'weight_ih' in name:
                    torch.nn.init.xavier_uniform_(param.data)
                elif 'weight_hh' in name:
                    torch.nn.init.orthogonal_(param.data)
                elif 'bias' in name:
                    torch.nn.init.zeros_(param.data)
        
    def forward(self, x: torch.Tensor, return_attention: bool = False) -> Dict[str, torch.Tensor]:
        """
        Args:
            x: (batch_size, seq_len, num_keypoints, 6) 输入姿态序列
            return_attention: 是否返回注意力权重
        Returns:
            outputs: 包含各种输出的字典
        """
        # 第一层：姿态补全和特征提取
        reconstructed_pose, hidden_states, pose_features = self.pose_completion_gru(x)
        
        # 第二层：意图识别
        intent_logits, attention_weights = self.intent_recognition_gru(pose_features)
        
        outputs = {
            'intent_logits': intent_logits,
            'reconstructed_pose': reconstructed_pose,
            'pose_features': pose_features,
            'hidden_states': hidden_states
        }
        
        if return_attention and attention_weights is not None:
            outputs['attention_weights'] = attention_weights
        
        return outputs
    
    def compute_loss(self, outputs: Dict[str, torch.Tensor], 
                    targets: torch.Tensor, 
                    original_pose: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        计算总损失
        Args:
            outputs: 模型输出
            targets: (batch_size,) 意图标签
            original_pose: (batch_size, seq_len, num_keypoints, 6) 原始姿态
        Returns:
            losses: 包含各种损失的字典
        """
        # 意图识别损失
        intent_loss = F.cross_entropy(outputs['intent_logits'], targets.squeeze())
        
        # 姿态重建损失（只对位置信息计算损失）
        original_positions = original_pose[:, :, :, :2]  # 只取位置信息
        reconstructed_positions = outputs['reconstructed_pose'][:, :, :, :2]
        pose_loss = F.mse_loss(reconstructed_positions, original_positions)
        
        # 总损失
        total_loss = (self.intent_loss_weight * intent_loss + 
                     self.pose_loss_weight * pose_loss)
        
        losses = {
            'total_loss': total_loss,
            'intent_loss': intent_loss,
            'pose_loss': pose_loss
        }
        
        return losses
    
    def predict_intent(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        预测意图
        Args:
            x: 输入姿态序列
        Returns:
            predicted_class: 预测的类别
            confidence: 预测置信度
        """
        self.eval()
        with torch.no_grad():
            outputs = self.forward(x)
            probabilities = F.softmax(outputs['intent_logits'], dim=1)
            confidence, predicted_class = torch.max(probabilities, dim=1)
        
        return predicted_class, confidence


def create_model(config: Dict) -> DualLayerGRU:
    """创建模型"""
    model = DualLayerGRU(config)
    
    # 打印模型信息
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"模型创建完成!")
    print(f"总参数数量: {total_params:,}")
    print(f"可训练参数数量: {trainable_params:,}")
    
    return model

"""
可视化训练验证工具
通过实际训练网络来验证标注数据的有效性
"""

import json
import numpy as np
import cv2
import os
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, classification_report
import warnings
warnings.filterwarnings('ignore')

class PoseIntentDataset(Dataset):
    """姿态意图数据集"""
    def __init__(self, annotation_files, augment=False):
        self.data = []
        self.intent_names = ['直行', '左转', '右转', '转身', '停止']
        
        for ann_file in annotation_files:
            with open(ann_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            for i, ann in enumerate(data['annotations']):
                image_info = data['images'][i] if i < len(data['images']) else data['images'][0]
                
                # 提取关键点
                keypoints = np.array(ann['keypoints']).reshape(17, 3)
                
                # 归一化坐标
                keypoints_norm = keypoints.copy()
                keypoints_norm[:, 0] /= image_info['width']
                keypoints_norm[:, 1] /= image_info['height']
                
                # 获取意图标签
                intent_label = ann.get('intent_label', image_info.get('intent_label', 0))
                
                self.data.append({
                    'keypoints': keypoints_norm[:, :2].flatten(),  # (34,)
                    'visibility': keypoints[:, 2],                # (17,)
                    'intent': intent_label,
                    'file_name': image_info['file_name']
                })
        
        print(f"加载了 {len(self.data)} 个训练样本")
        
        # 数据增强
        if augment and len(self.data) > 0:
            self._augment_data()
    
    def _augment_data(self):
        """数据增强"""
        original_data = self.data.copy()
        
        for sample in original_data:
            # 水平翻转
            flipped_keypoints = sample['keypoints'].copy().reshape(17, 2)
            flipped_keypoints[:, 0] = 1.0 - flipped_keypoints[:, 0]  # 翻转x坐标
            
            # 交换左右关键点
            left_right_pairs = [(1, 2), (3, 4), (5, 6), (7, 8), (9, 10), (11, 12), (13, 14), (15, 16)]
            for left_idx, right_idx in left_right_pairs:
                flipped_keypoints[[left_idx, right_idx]] = flipped_keypoints[[right_idx, left_idx]]
            
            # 调整意图标签（左右翻转）
            flipped_intent = sample['intent']
            if sample['intent'] == 1:  # 左转 -> 右转
                flipped_intent = 2
            elif sample['intent'] == 2:  # 右转 -> 左转
                flipped_intent = 1
            
            self.data.append({
                'keypoints': flipped_keypoints.flatten(),
                'visibility': sample['visibility'],
                'intent': flipped_intent,
                'file_name': f"aug_flip_{sample['file_name']}"
            })
            
            # 添加噪声
            noisy_keypoints = sample['keypoints'] + np.random.normal(0, 0.02, sample['keypoints'].shape)
            noisy_keypoints = np.clip(noisy_keypoints, 0, 1)
            
            self.data.append({
                'keypoints': noisy_keypoints,
                'visibility': sample['visibility'],
                'intent': sample['intent'],
                'file_name': f"aug_noise_{sample['file_name']}"
            })
        
        print(f"数据增强后: {len(self.data)} 个样本")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        sample = self.data[idx]
        return {
            'keypoints': torch.FloatTensor(sample['keypoints']),
            'visibility': torch.FloatTensor(sample['visibility']),
            'intent': torch.LongTensor([sample['intent']]),
            'file_name': sample['file_name']
        }

class IntentClassifier(nn.Module):
    """意图分类网络"""
    def __init__(self, input_dim=51, hidden_dims=[128, 64, 32], num_classes=5, dropout=0.3):
        super(IntentClassifier, self).__init__()
        
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim),
                nn.Dropout(dropout)
            ])
            prev_dim = hidden_dim
        
        layers.append(nn.Linear(prev_dim, num_classes))
        
        self.network = nn.Sequential(*layers)
        
    def forward(self, keypoints, visibility):
        # 合并关键点坐标和可见性
        x = torch.cat([keypoints, visibility], dim=1)  # (batch, 34+17=51)
        return self.network(x)

class VisualTrainingValidator:
    """可视化训练验证器"""
    def __init__(self, annotation_files):
        self.annotation_files = annotation_files
        self.intent_names = ['直行', '左转', '右转', '转身', '停止']
        self.intent_colors = ['green', 'blue', 'red', 'purple', 'orange']
        
        # 创建输出目录
        self.output_dir = "training_validation_output"
        os.makedirs(self.output_dir, exist_ok=True)
        
    def validate_data_quality(self):
        """验证数据质量"""
        print("=== 数据质量验证 ===")
        
        # 加载数据
        dataset = PoseIntentDataset(self.annotation_files)
        
        if len(dataset) == 0:
            print("❌ 没有找到有效的训练数据")
            return False
        
        # 分析数据分布
        intent_counts = {}
        visibility_stats = []
        
        for i in range(len(dataset)):
            sample = dataset[i]
            intent = sample['intent'].item()
            visibility = sample['visibility'].numpy()
            
            intent_counts[intent] = intent_counts.get(intent, 0) + 1
            visibility_stats.append(np.sum(visibility > 0))
        
        # 可视化数据分布
        self._plot_data_distribution(intent_counts, visibility_stats)
        
        print(f"✅ 数据质量验证完成，共 {len(dataset)} 个样本")
        return True
    
    def train_and_validate(self, epochs=50, test_split=0.2):
        """训练并验证模型"""
        print(f"\n=== 开始训练验证 (epochs={epochs}) ===")
        
        # 创建数据集
        dataset = PoseIntentDataset(self.annotation_files, augment=True)
        
        if len(dataset) < 5:
            print("❌ 数据量太少，无法进行有效训练")
            return False
        
        # 分割训练和测试集
        test_size = max(1, int(len(dataset) * test_split))
        train_size = len(dataset) - test_size
        
        train_dataset, test_dataset = torch.utils.data.random_split(
            dataset, [train_size, test_size]
        )
        
        train_loader = DataLoader(train_dataset, batch_size=min(8, len(train_dataset)), shuffle=True)
        test_loader = DataLoader(test_dataset, batch_size=min(8, len(test_dataset)), shuffle=False)
        
        # 创建模型
        model = IntentClassifier()
        criterion = nn.CrossEntropyLoss()
        optimizer = optim.Adam(model.parameters(), lr=0.001)
        
        # 训练历史
        train_losses = []
        train_accuracies = []
        test_accuracies = []
        
        print(f"训练集: {len(train_dataset)} 样本")
        print(f"测试集: {len(test_dataset)} 样本")
        
        # 训练循环
        for epoch in range(epochs):
            # 训练阶段
            model.train()
            train_loss = 0
            train_correct = 0
            train_total = 0
            
            for batch in train_loader:
                keypoints = batch['keypoints']
                visibility = batch['visibility']
                labels = batch['intent'].squeeze()
                
                optimizer.zero_grad()
                outputs = model(keypoints, visibility)
                loss = criterion(outputs, labels)
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                if labels.dim() == 0:
                    labels = labels.unsqueeze(0)
                train_total += labels.size(0)
                train_correct += (predicted == labels).sum().item()
            
            train_accuracy = 100 * train_correct / train_total
            avg_train_loss = train_loss / len(train_loader)
            
            # 测试阶段
            model.eval()
            test_correct = 0
            test_total = 0
            
            with torch.no_grad():
                for batch in test_loader:
                    keypoints = batch['keypoints']
                    visibility = batch['visibility']
                    labels = batch['intent'].squeeze()
                    
                    outputs = model(keypoints, visibility)
                    _, predicted = torch.max(outputs.data, 1)
                    if labels.dim() == 0:
                        labels = labels.unsqueeze(0)
                    test_total += labels.size(0)
                    test_correct += (predicted == labels).sum().item()
            
            test_accuracy = 100 * test_correct / test_total if test_total > 0 else 0
            
            # 记录历史
            train_losses.append(avg_train_loss)
            train_accuracies.append(train_accuracy)
            test_accuracies.append(test_accuracy)
            
            # 打印进度
            if (epoch + 1) % 10 == 0 or epoch == 0:
                print(f"Epoch {epoch+1}/{epochs}: "
                      f"Loss={avg_train_loss:.4f}, "
                      f"Train Acc={train_accuracy:.1f}%, "
                      f"Test Acc={test_accuracy:.1f}%")
        
        # 可视化训练过程
        self._plot_training_history(train_losses, train_accuracies, test_accuracies)
        
        # 详细评估
        self._detailed_evaluation(model, test_loader)
        
        print(f"✅ 训练验证完成")
        print(f"最终测试准确率: {test_accuracies[-1]:.1f}%")
        
        return test_accuracies[-1] > 60  # 60%以上认为验证成功
    
    def _plot_data_distribution(self, intent_counts, visibility_stats):
        """绘制数据分布图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 意图分布
        intents = [self.intent_names[i] for i in sorted(intent_counts.keys())]
        counts = [intent_counts[i] for i in sorted(intent_counts.keys())]
        colors = [self.intent_colors[i] for i in sorted(intent_counts.keys())]
        
        bars = ax1.bar(intents, counts, color=colors, alpha=0.7)
        ax1.set_title('意图标签分布', fontsize=14, fontweight='bold')
        ax1.set_ylabel('样本数量')
        ax1.tick_params(axis='x', rotation=45)
        
        # 添加数值标签
        for bar, count in zip(bars, counts):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                    str(count), ha='center', va='bottom', fontweight='bold')
        
        # 可见关键点分布
        ax2.hist(visibility_stats, bins=range(0, 19), alpha=0.7, color='skyblue', edgecolor='black')
        ax2.set_title('可见关键点数量分布', fontsize=14, fontweight='bold')
        ax2.set_xlabel('可见关键点数量')
        ax2.set_ylabel('样本数量')
        ax2.axvline(np.mean(visibility_stats), color='red', linestyle='--', 
                   label=f'平均值: {np.mean(visibility_stats):.1f}')
        ax2.legend()
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'data_distribution.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 数据分布图已保存: {self.output_dir}/data_distribution.png")
    
    def _plot_training_history(self, losses, train_accs, test_accs):
        """绘制训练历史"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        epochs = range(1, len(losses) + 1)
        
        # 损失曲线
        ax1.plot(epochs, losses, 'b-', linewidth=2, label='训练损失')
        ax1.set_title('训练损失曲线', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # 准确率曲线
        ax2.plot(epochs, train_accs, 'g-', linewidth=2, label='训练准确率')
        ax2.plot(epochs, test_accs, 'r-', linewidth=2, label='测试准确率')
        ax2.set_title('准确率曲线', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Accuracy (%)')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        ax2.set_ylim(0, 105)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'training_history.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 训练历史图已保存: {self.output_dir}/training_history.png")
    
    def _detailed_evaluation(self, model, test_loader):
        """详细评估模型性能"""
        model.eval()
        all_predictions = []
        all_labels = []
        all_filenames = []
        
        with torch.no_grad():
            for batch in test_loader:
                keypoints = batch['keypoints']
                visibility = batch['visibility']
                labels = batch['intent'].squeeze()
                filenames = batch['file_name']
                
                outputs = model(keypoints, visibility)
                _, predicted = torch.max(outputs.data, 1)
                
                all_predictions.extend(predicted.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
                all_filenames.extend(filenames)
        
        if len(all_predictions) == 0:
            print("⚠️ 没有测试数据进行详细评估")
            return
        
        # 混淆矩阵
        cm = confusion_matrix(all_labels, all_predictions)
        
        plt.figure(figsize=(10, 8))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=self.intent_names,
                   yticklabels=self.intent_names)
        plt.title('混淆矩阵', fontsize=16, fontweight='bold')
        plt.xlabel('预测标签')
        plt.ylabel('真实标签')
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'confusion_matrix.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        # 分类报告
        report = classification_report(all_labels, all_predictions, 
                                     target_names=self.intent_names, 
                                     output_dict=True)
        
        # 保存详细结果
        results = {
            'confusion_matrix': cm.tolist(),
            'classification_report': report,
            'predictions': [
                {
                    'file_name': fname,
                    'true_label': int(true_label),
                    'predicted_label': int(pred_label),
                    'true_intent': self.intent_names[true_label],
                    'predicted_intent': self.intent_names[pred_label],
                    'correct': int(true_label) == int(pred_label)
                }
                for fname, true_label, pred_label in zip(all_filenames, all_labels, all_predictions)
            ]
        }
        
        with open(os.path.join(self.output_dir, 'evaluation_results.json'), 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 混淆矩阵已保存: {self.output_dir}/confusion_matrix.png")
        print(f"✅ 详细评估结果已保存: {self.output_dir}/evaluation_results.json")
        
        # 打印分类报告
        print("\n📊 分类报告:")
        for intent_name in self.intent_names:
            if intent_name in report:
                metrics = report[intent_name]
                print(f"  {intent_name}: "
                      f"精确率={metrics['precision']:.3f}, "
                      f"召回率={metrics['recall']:.3f}, "
                      f"F1={metrics['f1-score']:.3f}")

def main():
    """主函数"""
    print("🔍 可视化训练验证工具")
    print("=" * 50)
    
    # 查找标注文件
    annotation_files = []
    search_dirs = ['annotation_output', 'intent_annotations', 'intent_annotations_fixed', 'enhanced_annotations', '.']
    
    for search_dir in search_dirs:
        if os.path.exists(search_dir):
            for file in os.listdir(search_dir):
                if file.endswith('.json') and ('keypoints' in file or 'intent' in file):
                    annotation_files.append(os.path.join(search_dir, file))
    
    if not annotation_files:
        print("❌ 未找到标注文件")
        return
    
    print(f"找到 {len(annotation_files)} 个标注文件:")
    for file in annotation_files:
        print(f"  - {file}")
    
    # 创建验证器
    validator = VisualTrainingValidator(annotation_files)
    
    # 执行验证
    print("\n🚀 开始可视化训练验证...")
    
    # 1. 数据质量验证
    if not validator.validate_data_quality():
        print("❌ 数据质量验证失败")
        return
    
    # 2. 训练验证
    success = validator.train_and_validate(epochs=100)
    
    # 总结
    print(f"\n{'='*50}")
    if success:
        print("🎉 验证成功！您的标注数据可以有效训练深度学习模型")
        print(f"\n📁 生成的可视化文件:")
        print(f"  - training_validation_output/data_distribution.png")
        print(f"  - training_validation_output/training_history.png") 
        print(f"  - training_validation_output/confusion_matrix.png")
        print(f"  - training_validation_output/evaluation_results.json")
    else:
        print("❌ 验证失败，建议检查数据质量或增加数据量")

if __name__ == '__main__':
    main()

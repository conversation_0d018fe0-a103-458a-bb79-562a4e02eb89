"""
经典方法的行走意图识别
基于关键点几何分析，不使用深度学习
"""

import cv2
import numpy as np
import os
import json
from collections import deque
from ultralytics import YOLO
import math

class ClassicalIntentRecognizer:
    def __init__(self):
        # 意图类别
        self.intent_classes = ['STRAIGHT', 'LEFT', 'RIGHT', 'TURN_AROUND', 'STOP']
        self.intent_colors = [
            (0, 255, 0),    # STRAIGHT - 绿色
            (255, 0, 0),    # LEFT - 蓝色
            (0, 0, 255),    # RIGHT - 红色
            (255, 0, 255),  # TURN_AROUND - 紫色
            (0, 255, 255)   # STOP - 黄色
        ]
        
        # 历史数据缓冲区
        self.pose_history = deque(maxlen=20)
        self.body_center_history = deque(maxlen=15)
        self.shoulder_angle_history = deque(maxlen=12)
        self.hip_angle_history = deque(maxlen=12)
        self.step_frequency_history = deque(maxlen=10)
        
        # 关键点索引
        self.keypoints_idx = {
            'nose': 0, 'left_eye': 1, 'right_eye': 2, 'left_ear': 3, 'right_ear': 4,
            'left_shoulder': 5, 'right_shoulder': 6, 'left_elbow': 7, 'right_elbow': 8,
            'left_wrist': 9, 'right_wrist': 10, 'left_hip': 11, 'right_hip': 12,
            'left_knee': 13, 'right_knee': 14, 'left_ankle': 15, 'right_ankle': 16
        }
        
        # 加载姿态检测模型
        print("加载YOLOv8姿态检测模型...")
        self.model = YOLO('yolov8n-pose.pt')
        print("✓ 模型加载成功")
    
    def calculate_angle(self, p1, p2, p3):
        """计算三点之间的角度"""
        v1 = p1 - p2
        v2 = p3 - p2
        
        cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2) + 1e-6)
        cos_angle = np.clip(cos_angle, -1.0, 1.0)
        angle = np.arccos(cos_angle)
        return np.degrees(angle)
    
    def calculate_vector_angle(self, vector):
        """计算向量与水平轴的角度"""
        return np.degrees(np.arctan2(vector[1], vector[0]))
    
    def analyze_body_orientation(self, keypoints, confidence):
        """分析身体朝向"""
        features = {}
        
        # 1. 肩膀朝向分析
        if confidence[5] > 0.4 and confidence[6] > 0.4:
            left_shoulder = keypoints[5]
            right_shoulder = keypoints[6]
            shoulder_vector = right_shoulder - left_shoulder
            shoulder_angle = self.calculate_vector_angle(shoulder_vector)
            features['shoulder_angle'] = shoulder_angle
            features['shoulder_width'] = np.linalg.norm(shoulder_vector)
        
        # 2. 髋部朝向分析
        if confidence[11] > 0.4 and confidence[12] > 0.4:
            left_hip = keypoints[11]
            right_hip = keypoints[12]
            hip_vector = right_hip - left_hip
            hip_angle = self.calculate_vector_angle(hip_vector)
            features['hip_angle'] = hip_angle
            features['hip_width'] = np.linalg.norm(hip_vector)
        
        # 3. 身体中轴分析
        if (confidence[5] > 0.4 and confidence[6] > 0.4 and 
            confidence[11] > 0.4 and confidence[12] > 0.4):
            shoulder_center = (keypoints[5] + keypoints[6]) / 2
            hip_center = (keypoints[11] + keypoints[12]) / 2
            body_axis = hip_center - shoulder_center
            features['body_axis_angle'] = self.calculate_vector_angle(body_axis)
            features['body_center'] = (shoulder_center + hip_center) / 2
        
        return features
    
    def analyze_leg_movement(self, keypoints, confidence):
        """分析腿部运动模式"""
        features = {}
        
        # 1. 膝盖角度分析
        if confidence[11] > 0.4 and confidence[13] > 0.4 and confidence[15] > 0.4:
            left_knee_angle = self.calculate_angle(keypoints[11], keypoints[13], keypoints[15])
            features['left_knee_angle'] = left_knee_angle
        
        if confidence[12] > 0.4 and confidence[14] > 0.4 and confidence[16] > 0.4:
            right_knee_angle = self.calculate_angle(keypoints[12], keypoints[14], keypoints[16])
            features['right_knee_angle'] = right_knee_angle
        
        # 2. 步幅分析
        if confidence[15] > 0.4 and confidence[16] > 0.4:
            left_ankle = keypoints[15]
            right_ankle = keypoints[16]
            step_width = np.linalg.norm(right_ankle - left_ankle)
            features['step_width'] = step_width
            
            # 脚踝高度差（检测抬腿）
            ankle_height_diff = abs(left_ankle[1] - right_ankle[1])
            features['ankle_height_diff'] = ankle_height_diff
        
        # 3. 腿部对称性
        if 'left_knee_angle' in features and 'right_knee_angle' in features:
            knee_symmetry = abs(features['left_knee_angle'] - features['right_knee_angle'])
            features['knee_symmetry'] = knee_symmetry
        
        return features
    
    def analyze_movement_trajectory(self):
        """分析移动轨迹"""
        if len(self.body_center_history) < 5:
            return {}
        
        features = {}
        centers = np.array(list(self.body_center_history))
        
        # 1. 移动方向分析
        movement_vector = centers[-1] - centers[0]
        movement_angle = self.calculate_vector_angle(movement_vector)
        movement_distance = np.linalg.norm(movement_vector)
        
        features['movement_angle'] = movement_angle
        features['movement_distance'] = movement_distance
        features['movement_speed'] = movement_distance / len(centers)
        
        # 2. 轨迹曲率分析
        if len(centers) >= 8:
            # 计算轨迹的弯曲程度
            first_half = centers[:len(centers)//2]
            second_half = centers[len(centers)//2:]
            
            if len(first_half) >= 2 and len(second_half) >= 2:
                first_direction = first_half[-1] - first_half[0]
                second_direction = second_half[-1] - second_half[0]
                
                # 计算方向变化
                direction_change = np.cross(first_direction, second_direction)
                features['trajectory_curvature'] = direction_change
        
        # 3. 移动稳定性
        if len(centers) >= 3:
            velocities = []
            for i in range(1, len(centers)):
                velocity = np.linalg.norm(centers[i] - centers[i-1])
                velocities.append(velocity)
            
            features['velocity_variance'] = np.var(velocities) if velocities else 0
        
        return features
    
    def analyze_body_angle_changes(self):
        """分析身体角度变化"""
        features = {}
        
        # 肩膀角度变化
        if len(self.shoulder_angle_history) >= 5:
            angles = list(self.shoulder_angle_history)
            angle_changes = []
            for i in range(1, len(angles)):
                change = angles[i] - angles[i-1]
                # 处理角度跨越
                if change > 180:
                    change -= 360
                elif change < -180:
                    change += 360
                angle_changes.append(change)
            
            features['shoulder_angle_change_rate'] = np.mean(angle_changes) if angle_changes else 0
            features['shoulder_angle_variance'] = np.var(angle_changes) if angle_changes else 0
        
        # 髋部角度变化
        if len(self.hip_angle_history) >= 5:
            angles = list(self.hip_angle_history)
            angle_changes = []
            for i in range(1, len(angles)):
                change = angles[i] - angles[i-1]
                if change > 180:
                    change -= 360
                elif change < -180:
                    change += 360
                angle_changes.append(change)
            
            features['hip_angle_change_rate'] = np.mean(angle_changes) if angle_changes else 0
            features['hip_angle_variance'] = np.var(angle_changes) if angle_changes else 0
        
        return features
    
    def classify_intent(self, body_features, leg_features, trajectory_features, angle_features):
        """基于规则的意图分类"""
        
        # 初始化得分
        intent_scores = [0, 0, 0, 0, 0]  # [STRAIGHT, LEFT, RIGHT, TURN_AROUND, STOP]
        
        # 1. 基于移动距离判断是否停止
        movement_distance = trajectory_features.get('movement_distance', 0)
        movement_speed = trajectory_features.get('movement_speed', 0)
        
        if movement_distance < 0.015 and movement_speed < 0.003:
            intent_scores[4] += 50  # STOP
        
        # 2. 基于轨迹曲率判断转向
        trajectory_curvature = trajectory_features.get('trajectory_curvature', 0)
        
        if abs(trajectory_curvature) > 0.0008:
            if trajectory_curvature > 0:
                intent_scores[1] += 30  # LEFT
            else:
                intent_scores[2] += 30  # RIGHT
        
        # 3. 基于身体角度变化判断转向
        shoulder_change = angle_features.get('shoulder_angle_change_rate', 0)
        hip_change = angle_features.get('hip_angle_change_rate', 0)
        
        total_angle_change = abs(shoulder_change) + abs(hip_change)
        
        if total_angle_change > 5:  # 显著角度变化
            if shoulder_change < -3 or hip_change < -3:
                intent_scores[1] += 25  # LEFT
            elif shoulder_change > 3 or hip_change > 3:
                intent_scores[2] += 25  # RIGHT
        
        # 4. 基于移动方向判断
        movement_angle = trajectory_features.get('movement_angle', 0)
        
        # 将角度标准化到[-180, 180]
        while movement_angle > 180:
            movement_angle -= 360
        while movement_angle < -180:
            movement_angle += 360
        
        if abs(movement_angle) < 30:  # 向前移动
            intent_scores[0] += 20  # STRAIGHT
        elif movement_angle < -30:  # 向左移动
            intent_scores[1] += 20  # LEFT
        elif movement_angle > 30:  # 向右移动
            intent_scores[2] += 20  # RIGHT
        
        # 5. 检测转身（大幅度角度变化）
        shoulder_variance = angle_features.get('shoulder_angle_variance', 0)
        hip_variance = angle_features.get('hip_angle_variance', 0)
        
        if shoulder_variance > 100 or hip_variance > 100:
            intent_scores[3] += 40  # TURN_AROUND
        
        # 6. 基于腿部运动判断停止
        ankle_height_diff = leg_features.get('ankle_height_diff', 0)
        knee_symmetry = leg_features.get('knee_symmetry', 0)
        
        if ankle_height_diff < 10 and knee_symmetry < 20:  # 腿部运动很小
            intent_scores[4] += 20  # STOP
        
        # 7. 基于速度变化判断
        velocity_variance = trajectory_features.get('velocity_variance', 0)
        
        if velocity_variance < 0.0001:  # 速度很稳定
            if movement_speed > 0.005:
                intent_scores[0] += 15  # STRAIGHT
            else:
                intent_scores[4] += 15  # STOP
        
        # 选择得分最高的意图
        max_score = max(intent_scores)
        if max_score > 20:  # 最低置信度阈值
            predicted_intent = intent_scores.index(max_score)
            confidence = min(0.95, max_score / 100.0)
            return predicted_intent, confidence
        
        return None, 0.0
    
    def update_and_classify(self, keypoints, confidence):
        """更新历史数据并进行意图分类"""
        
        # 分析当前帧
        body_features = self.analyze_body_orientation(keypoints, confidence)
        leg_features = self.analyze_leg_movement(keypoints, confidence)
        
        # 更新历史数据
        self.pose_history.append(keypoints.copy())
        
        if 'body_center' in body_features:
            self.body_center_history.append(body_features['body_center'])
        
        if 'shoulder_angle' in body_features:
            self.shoulder_angle_history.append(body_features['shoulder_angle'])
        
        if 'hip_angle' in body_features:
            self.hip_angle_history.append(body_features['hip_angle'])
        
        # 分析轨迹和角度变化
        trajectory_features = self.analyze_movement_trajectory()
        angle_features = self.analyze_body_angle_changes()
        
        # 进行意图分类
        intent, confidence_score = self.classify_intent(
            body_features, leg_features, trajectory_features, angle_features
        )
        
        # 返回详细信息用于调试
        debug_info = {
            'body_features': body_features,
            'leg_features': leg_features,
            'trajectory_features': trajectory_features,
            'angle_features': angle_features
        }
        
        return intent, confidence_score, debug_info

def analyze_video_classical(video_path='left.mp4', output_path='left_classical_analysis.mp4'):
    """
    使用经典方法分析视频
    """
    print(f"开始经典方法意图识别: {video_path}")
    
    if not os.path.exists(video_path):
        print(f"错误: 视频文件不存在")
        return False
    
    # 创建识别器
    recognizer = ClassicalIntentRecognizer()
    
    # 打开视频
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("错误: 无法打开视频")
        return False
    
    # 视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"视频: {width}x{height}, {fps}FPS, {total_frames}帧")
    
    # 创建输出视频
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    frame_count = 0
    detection_count = 0
    intent_stats = {name: 0 for name in recognizer.intent_classes}
    intent_stats['NOT_DETECTED'] = 0
    
    print("开始处理...")
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 姿态检测
            results = recognizer.model(frame, conf=0.3, verbose=False)
            
            annotated_frame = frame.copy()
            current_intent = None
            intent_confidence = 0.0
            debug_info = {}
            
            # 处理检测结果
            if len(results) > 0 and results[0].keypoints is not None:
                keypoints_data = results[0].keypoints.data
                
                if len(keypoints_data) > 0:
                    detection_count += 1
                    keypoints = keypoints_data[0]
                    
                    if keypoints.shape[0] == 17:
                        xy = keypoints[:, :2].cpu().numpy()
                        conf = keypoints[:, 2].cpu().numpy()
                        
                        # 归一化关键点
                        normalized_xy = xy.copy()
                        normalized_xy[:, 0] /= width
                        normalized_xy[:, 1] /= height
                        
                        # 经典方法意图识别
                        intent_id, confidence, debug_info = recognizer.update_and_classify(
                            normalized_xy, conf
                        )
                        
                        current_intent = intent_id
                        intent_confidence = confidence
                        
                        # 绘制关键点和骨架
                        for i, (x, y) in enumerate(xy):
                            if conf[i] > 0.3:
                                color = (0, 255, 0) if conf[i] > 0.7 else (0, 255, 255)
                                cv2.circle(annotated_frame, (int(x), int(y)), 6, color, -1)
                        
                        # 绘制骨架
                        connections = [
                            (5, 6), (5, 7), (7, 9), (6, 8), (8, 10),
                            (5, 11), (6, 12), (11, 12),
                            (11, 13), (13, 15), (12, 14), (14, 16)
                        ]
                        
                        for start_idx, end_idx in connections:
                            if conf[start_idx] > 0.3 and conf[end_idx] > 0.3:
                                start_point = (int(xy[start_idx][0]), int(xy[start_idx][1]))
                                end_point = (int(xy[end_idx][0]), int(xy[end_idx][1]))
                                cv2.line(annotated_frame, start_point, end_point, (255, 0, 0), 3)
                        
                        # 绘制移动轨迹
                        if len(recognizer.body_center_history) >= 2:
                            for i in range(1, len(recognizer.body_center_history)):
                                prev_center = recognizer.body_center_history[i-1] * np.array([width, height])
                                curr_center = recognizer.body_center_history[i] * np.array([width, height])
                                cv2.line(annotated_frame,
                                        (int(prev_center[0]), int(prev_center[1])),
                                        (int(curr_center[0]), int(curr_center[1])),
                                        (0, 255, 255), 3)
            
            # 更新统计
            if current_intent is not None:
                intent_stats[recognizer.intent_classes[current_intent]] += 1
            else:
                intent_stats['NOT_DETECTED'] += 1
            
            # 绘制信息面板
            panel_width = 700
            panel_height = 280
            
            # 半透明背景
            overlay = annotated_frame.copy()
            cv2.rectangle(overlay, (10, 10), (panel_width, panel_height), (0, 0, 0), -1)
            cv2.addWeighted(overlay, 0.85, annotated_frame, 0.15, 0, annotated_frame)
            
            # 白色边框
            cv2.rectangle(annotated_frame, (10, 10), (panel_width, panel_height), (255, 255, 255), 3)
            
            # 显示信息
            y = 40
            line_height = 25
            
            # 标题
            cv2.putText(annotated_frame, "Classical Intent Recognition", 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
            y += line_height + 5
            
            # 帧信息
            cv2.putText(annotated_frame, f'Frame: {frame_count}/{total_frames} ({frame_count/fps:.1f}s)', 
                       (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            y += line_height
            
            # 意图显示
            if current_intent is not None:
                intent_text = f'Intent: {recognizer.intent_classes[current_intent]}'
                confidence_text = f'Confidence: {intent_confidence:.2f}'
                intent_color = recognizer.intent_colors[current_intent]
                
                cv2.putText(annotated_frame, intent_text, 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.0, intent_color, 3)
                y += line_height + 5
                
                cv2.putText(annotated_frame, confidence_text, 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, intent_color, 2)
                y += line_height
                
                # 显示关键特征
                if debug_info:
                    trajectory = debug_info.get('trajectory_features', {})
                    if 'movement_distance' in trajectory:
                        cv2.putText(annotated_frame, f"Move Dist: {trajectory['movement_distance']:.4f}", 
                                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
                        y += 20
                    
                    if 'trajectory_curvature' in trajectory:
                        cv2.putText(annotated_frame, f"Curvature: {trajectory['trajectory_curvature']:.4f}", 
                                   (25, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
                        y += 20
                
                # 右上角大字显示
                intent_display = recognizer.intent_classes[current_intent]
                text_size = cv2.getTextSize(intent_display, cv2.FONT_HERSHEY_SIMPLEX, 2.0, 4)[0]
                
                cv2.rectangle(annotated_frame, 
                             (width - text_size[0] - 40, 20), 
                             (width - 20, 80), 
                             intent_color, -1)
                cv2.rectangle(annotated_frame, 
                             (width - text_size[0] - 40, 20), 
                             (width - 20, 80), 
                             (255, 255, 255), 3)
                cv2.putText(annotated_frame, intent_display, 
                           (width - text_size[0] - 30, 60), 
                           cv2.FONT_HERSHEY_SIMPLEX, 2.0, (255, 255, 255), 4)
            else:
                cv2.putText(annotated_frame, 'Intent: ANALYZING...', 
                           (25, y), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (128, 128, 128), 3)
            
            # 写入视频
            writer.write(annotated_frame)
            
            # 显示进度
            if frame_count % 100 == 0:
                progress = frame_count / total_frames * 100
                current_intent_name = recognizer.intent_classes[current_intent] if current_intent is not None else 'NOT_DETECTED'
                print(f"进度: {progress:.1f}% - 当前意图: {current_intent_name}")
    
    except Exception as e:
        print(f"处理错误: {e}")
        return False
    
    finally:
        cap.release()
        writer.release()
    
    print(f"\n=== 经典方法分析完成 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测帧数: {detection_count}")
    print(f"检测率: {detection_count/frame_count*100:.1f}%")
    print(f"\n意图分布:")
    for intent, count in intent_stats.items():
        percentage = count / frame_count * 100 if frame_count > 0 else 0
        print(f"  {intent}: {count} 帧 ({percentage:.1f}%)")
    print(f"输出视频: {output_path}")
    
    return True

if __name__ == '__main__':
    success = analyze_video_classical()
    if success:
        print("\n🎉 经典方法意图识别完成！")
        print("特点:")
        print("- 基于几何分析，无需训练")
        print("- 可解释性强")
        print("- 实时性好")
        print("- 规则可调整")
    else:
        print("\n❌ 经典方法意图识别失败！")
